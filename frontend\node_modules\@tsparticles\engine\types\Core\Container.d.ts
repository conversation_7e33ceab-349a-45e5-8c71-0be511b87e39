import { Canvas } from "./Canvas.js";
import type { Engine } from "./Engine.js";
import type { IContainerInteractivity } from "./Interfaces/IContainerInteractivity.js";
import type { IContainerPlugin } from "./Interfaces/IContainerPlugin.js";
import type { IEffectDrawer } from "./Interfaces/IEffectDrawer.js";
import type { IMovePathGenerator } from "./Interfaces/IMovePathGenerator.js";
import type { IShapeDrawer } from "./Interfaces/IShapeDrawer.js";
import type { ISourceOptions } from "../Types/ISourceOptions.js";
import { Options } from "../Options/Classes/Options.js";
import type { Particle } from "./Particle.js";
import { Particles } from "./Particles.js";
import { Retina } from "./Retina.js";
export declare class Container {
    actualOptions: Options;
    readonly canvas: Canvas;
    destroyed: boolean;
    readonly effectDrawers: Map<string, IEffectDrawer<Particle>>;
    fpsLimit: number;
    readonly id: symbol;
    interactivity: IContainerInteractivity;
    pageHidden: boolean;
    readonly particles: Particles;
    readonly pathGenerators: Map<string, IMovePathGenerator>;
    readonly plugins: Map<string, IContainerPlugin>;
    readonly retina: Retina;
    readonly shapeDrawers: Map<string, IShapeDrawer<Particle>>;
    started: boolean;
    zLayers: number;
    private readonly _clickHandlers;
    private _currentTheme?;
    private _delay;
    private _delayTimeout?;
    private _drawAnimationFrame?;
    private _duration;
    private readonly _engine;
    private readonly _eventListeners;
    private _firstStart;
    private _initialSourceOptions;
    private readonly _intersectionObserver;
    private _lastFrameTime?;
    private _lifeTime;
    private _options;
    private _paused;
    private _responsiveMaxWidth?;
    private _smooth;
    private _sourceOptions;
    constructor(engine: Engine, id: string, sourceOptions?: ISourceOptions);
    get animationStatus(): boolean;
    get options(): Options;
    get sourceOptions(): ISourceOptions | undefined;
    addClickHandler(callback: (evt: Event, particles?: Particle[]) => void): void;
    addLifeTime(value: number): void;
    addPath(key: string, generator: IMovePathGenerator, override?: boolean): boolean;
    alive(): boolean;
    clearClickHandlers(): void;
    destroy(remove?: boolean): void;
    draw(force: boolean): void;
    export(type: string, options?: Record<string, unknown>): Promise<Blob | undefined>;
    handleClickMode(mode: string): void;
    init(): Promise<void>;
    loadTheme(name?: string): Promise<void>;
    pause(): void;
    play(force?: boolean): void;
    refresh(): Promise<void>;
    reset(sourceOptions?: ISourceOptions): Promise<void>;
    start(): Promise<void>;
    stop(): void;
    updateActualOptions(): boolean;
    private readonly _intersectionManager;
    private readonly _nextFrame;
}
