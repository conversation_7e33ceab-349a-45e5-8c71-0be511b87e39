import React, { useEffect, useRef } from 'react';
import { Parallax } from 'react-scroll-parallax';
import { motion, useAnimation, useInView } from 'framer-motion';
import { ServiceGradientCards } from './ui/service-gradient-cards';

const Services = () => {

  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: false, amount: 0.1 });
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);

  const titleVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 10,
        delay: 0.2
      }
    }
  };

  return (
    <section id="services" ref={sectionRef} className="py-20 bg-black relative overflow-hidden min-h-screen">
      {/* Background elements with subtle parallax */}
      <Parallax speed={-10} className="absolute top-0 right-0 w-1/3 h-1/3">
        <div className="w-full h-full bg-purple-600/10 rounded-full filter blur-3xl transform translate-x-1/4 -translate-y-1/4"></div>
      </Parallax>

      <Parallax speed={5} className="absolute bottom-0 left-0 w-1/2 h-1/2">
        <div className="w-full h-full bg-blue-600/10 rounded-full filter blur-3xl transform -translate-x-1/4 translate-y-1/4"></div>
      </Parallax>

      {/* Grid pattern for depth */}
      <Parallax speed={-5} className="absolute inset-0 z-0 opacity-5">
        <div className="w-full h-full" style={{
          backgroundImage: 'linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px), linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px)',
          backgroundSize: '80px 80px'
        }}></div>
      </Parallax>

      {/* Large Gradient Heading - positioned to span across cards area */}
      <div className="absolute top-1/5 left-1/2 transform -translate-x-1/2 -translate-y-1/4 pointer-events-none z-10">
        <motion.h1
          className="text-[12rem] sm:text-[16rem] md:text-[20rem] lg:text-[24rem] xl:text-[28rem] font-black leading-none select-none"
          style={{
            background: 'linear-gradient(135deg, rgba(147, 51, 234, 0.8) 0%, rgba(59, 130, 246, 0.8) 50%, rgba(147, 51, 234, 0.4) 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            textShadow: '0 0 100px rgba(147, 51, 234, 0.3)',
          }}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={isInView ? {
            opacity: 1,
            scale: 1,
            transition: {
              duration: 1.2,
              ease: [0.16, 1, 0.3, 1],
              delay: 0.2
            }
          } : {}}
        >
          SERVICES
        </motion.h1>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-20">
        {/* Small header section */}
        <motion.div
          className="text-center mb-16 relative z-30"
          initial="hidden"
          animate={controls}
          variants={{
            hidden: { opacity: 0 },
            visible: { opacity: 1 }
          }}
        >
          <motion.div
            className="flex justify-center mb-6"
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: { delay: 0.1, duration: 0.6 }
              }
            }}
          >
           
          </motion.div>

          <motion.h2
            className="text-2xl md:text-3xl font-bold text-white mb-4 relative"
            variants={{
              hidden: { opacity: 0, y: -20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: {
                  type: 'spring',
                  stiffness: 100,
                  damping: 10,
                  delay: 0.2
                }
              }
            }}
          >

            {/* Quick Delivery badge */}
            <motion.div
              className="md:absolute md:-top-10 md:right-0 hidden md:block z-10 transform -rotate-6"
              initial={{ opacity: 0, y: 20, scale: 0.8 }}
              animate={isInView ? {
                opacity: 1,
                y: 0,
                scale: 1,
                transition: {
                  type: 'spring',
                  stiffness: 300,
                  damping: 15,
                  delay: 0.5
                }
              } : {}}
              whileHover={{
                scale: 1.05,
                rotate: 0,
                transition: { duration: 0.2 }
              }}
            >
              <div className="bg-gradient-to-r from-purple-600 to-blue-500 text-white px-2 py-1 md:px-4 md:py-2 rounded-lg shadow-lg relative overflow-hidden scale-75 md:scale-100">
                <span className="relative z-10 font-bold text-xs md:text-sm">Quick Delivery!</span>

                {/* Animated dots/sparkles */}
                <motion.div
                  className="absolute top-1 right-1 w-1 h-1 bg-white rounded-full"
                  animate={{
                    opacity: [0, 1, 0],
                    scale: [0.8, 1.2, 0.8],
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 2,
                    ease: "easeInOut"
                  }}
                />
                <motion.div
                  className="absolute bottom-1 left-2 w-1 h-1 bg-white rounded-full"
                  animate={{
                    opacity: [0, 1, 0],
                    scale: [0.8, 1.2, 0.8],
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 1.5,
                    delay: 0.5,
                    ease: "easeInOut"
                  }}
                />
              </div>
            </motion.div>
          </motion.h2>

          
        </motion.div>

        {/* Service Gradient Cards - positioned above the middle of large text */}
        <motion.div
          className="relative z-20"
          initial="hidden"
          animate={controls}
          variants={{
            hidden: { opacity: 0, y: 50 },
            visible: {
              opacity: 1,
              y: 0,
              transition: {
                type: 'spring',
                stiffness: 100,
                damping: 12,
                delay: 0.5
              }
            }
          }}
        >
          <ServiceGradientCards />
        </motion.div>
      </div>
    </section>
  );
};

export default Services;
