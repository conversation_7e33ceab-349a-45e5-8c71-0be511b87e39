import { GradientCard } from "@/components/ui/gradient-card"
import { Footer2 } from "@/components/ui/shadcnblocks-com-footer2"
import TestimonialsDemo from "@/components/ui/testimonials-demo"

const demoData = {
  logo: {
    src: "/favicon.png",
    alt: "Intelermate",
    title: "Intelermate",
    url: "/",
  },
  tagline: "Connecting student developers with startups for affordable web services.",
  menuItems: [
    {
      title: "Services",
      links: [
        { text: "Website Development", url: "/services/website-development" },
        { text: "Web Applications", url: "/services/web-application-development" },
        { text: "E-commerce Solutions", url: "/services/ecommerce-solutions" },
        { text: "UI/UX Design", url: "/services/uiux-design" },
        { text: "SEO & Marketing", url: "/services/seo-digital-marketing" },
      ],
    },
    {
      title: "Company",
      links: [
        { text: "About", url: "/about" },
        { text: "Blog", url: "/blog" },
        { text: "Careers", url: "/careers" },
        { text: "Contact", url: "/#contact" },
        { text: "FAQ", url: "/faq" },
      ],
    },
    {
      title: "Resources",
      links: [
        { text: "Consultation", url: "/consultation" },
        { text: "Portfolio", url: "/#portfolio" },
        { text: "Testimonials", url: "/#testimonials" },
      ],
    },
    {
      title: "Legal",
      links: [
        { text: "Privacy Policy", url: "/privacy-policy" },
        { text: "Terms & Conditions", url: "/terms-conditions" },
      ],
    },
  ],
  copyright: "© 2024 Intelermate. All rights reserved.",
  bottomLinks: [
    { text: "Terms and Conditions", url: "/terms-conditions" },
    { text: "Privacy Policy", url: "/privacy-policy" },
  ],
};

export const Demo = () => {
    return <GradientCard />
}

export const Footer2Demo = () => {
  return <Footer2 {...demoData} />;
}

export { TestimonialsDemo };
