import React from 'react';
import { GradientCard } from './gradient-card';

// Service icons
const WebDesignIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
  </svg>
);

const WebDevelopmentIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
  </svg>
);

const SEOIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
);

const MobileAppIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
  </svg>
);

const DigitalMarketingIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
  </svg>
);

const EcommerceIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
  </svg>
);

export const ServiceGradientCards = () => {
  const services = [
    {
      icon: <WebDesignIcon />,
      title: "Web Design",
      description: "We create stunning, responsive websites that captivate your audience and reflect your brand identity perfectly.",
      linkText: "View Portfolio",
      linkHref: "#portfolio"
    },
    {
      icon: <WebDevelopmentIcon />,
      title: "Web Development",
      description: "Our expert developers build robust, scalable web applications using the latest technologies and best practices.",
      linkText: "See Projects",
      linkHref: "#projects"
    },
    {
      icon: <SEOIcon />,
      title: "SEO Optimization",
      description: "We optimize your website to rank higher in search engines, driving more organic traffic to your business.",
      linkText: "Learn SEO",
      linkHref: "#seo"
    },
    {
      icon: <MobileAppIcon />,
      title: "Mobile App Development",
      description: "We build native and cross-platform mobile applications that provide seamless experiences across all devices.",
      linkText: "View Apps",
      linkHref: "#mobile"
    },
    {
      icon: <DigitalMarketingIcon />,
      title: "Digital Marketing",
      description: "Our comprehensive digital marketing strategies help you reach your target audience and grow your online presence.",
      linkText: "Get Started",
      linkHref: "#marketing"
    },
    {
      icon: <EcommerceIcon />,
      title: "E-commerce Solutions",
      description: "We develop secure, user-friendly e-commerce platforms that drive sales and enhance customer experience.",
      linkText: "Shop Demo",
      linkHref: "#ecommerce"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
      {services.map((service, index) => (
        <div key={`service-${service.title}-${index}`} className="flex justify-center">
          <GradientCard
            key={`gradient-card-${service.title}-${index}`}
            icon={service.icon}
            title={service.title}
            description={service.description}
            linkText={service.linkText}
            linkHref={service.linkHref}
          />
        </div>
      ))}
    </div>
  );
};
