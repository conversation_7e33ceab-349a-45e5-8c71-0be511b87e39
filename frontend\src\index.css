@import "tailwindcss";
@import "./styles/BlogStyles.css";

@layer base {
  :root {
    --gradient-color: #8350e8;
    --sparkles-color: #8350e8;
  }

  .dark {
    --gradient-color: #8350e8;
    --sparkles-color: #ffffff;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    @apply bg-black text-white;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 10px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-black;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-purple-900 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-purple-800;
  }
}

@layer components {
  /* Hide scrollbar but allow scrolling */
  .hide-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }

  /* Animation classes */
  .floating-element {
    transition: transform 0.3s ease-out;
  }

  /* Reveal animations */
  .reveal-element {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }

  .reveal-element.revealed {
    opacity: 1;
    transform: translateY(0);
  }

  /* Staggered reveal for multiple elements */
  .stagger-reveal > * {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }

  .stagger-reveal > *.revealed {
    opacity: 1;
    transform: translateY(0);
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-500;
  }

  /* Gradient border */
  .gradient-border {
    position: relative;
  }

  /* Tech tag animations */
  .tech-tag {
    cursor: grab;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .tech-tag:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .tech-tag:active {
    cursor: grabbing;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Tech tags that appear behind content */
  .tech-tag-behind {
    cursor: grab;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-style: preserve-3d;
    backface-visibility: hidden;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  }

  .tech-tag-behind:hover {
    transform: translateZ(20px) translateY(-10px);
    box-shadow: 0 15px 30px -10px rgba(124, 58, 237, 0.5);
  }

  .tech-tag-behind:active {
    cursor: grabbing;
  }

  /* Center tech tags specific styles */
  .center-tech-tag {
    cursor: grab;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transform-origin: center bottom;
  }

  .center-tech-tag:hover {
    transform: translateY(-15px);
    box-shadow: 0 15px 20px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
  }

  .center-tech-tag:active {
    cursor: grabbing;
  }

  /* Floating navigation styles */
  .floating-nav {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
    z-index: 9999 !important;
  }

  .floating-nav-button {
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .floating-nav-button:hover {
    transform: translateY(-5px);
  }

  .floating-nav-center {
    box-shadow: 0 10px 25px -5px rgba(139, 92, 246, 0.5), 0 8px 10px -6px rgba(139, 92, 246, 0.4);
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .floating-nav-center:hover {
    transform: translateY(-15px);
    box-shadow: 0 15px 30px -5px rgba(139, 92, 246, 0.6), 0 10px 15px -5px rgba(139, 92, 246, 0.5);
  }

  /* Mobile navigation animations */
  .animate-fade-in-up {
    animation: fadeInUp 0.3s ease-out forwards;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}