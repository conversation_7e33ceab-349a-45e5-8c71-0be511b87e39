import type { ICoordinates, ICoordinatesWithMode } from "../Core/Interfaces/ICoordinates.js";
import type { IDimension, IDimensionWithMode } from "../Core/Interfaces/IDimension.js";
import { DestroyType } from "../Enums/Types/DestroyType.js";
import type { DivEvent } from "../Options/Classes/Interactivity/Events/DivEvent.js";
import type { IBounds } from "../Core/Interfaces/IBounds.js";
import type { ICircleBouncer } from "../Core/Interfaces/ICircleBouncer.js";
import type { IDelta } from "../Core/Interfaces/IDelta.js";
import type { IModeDiv } from "../Options/Interfaces/Interactivity/Modes/IModeDiv.js";
import type { IParticleNumericValueAnimation } from "../Core/Interfaces/IParticleValueAnimation.js";
import { OutModeDirection } from "../Enums/Directions/OutModeDirection.js";
import type { Particle } from "../Core/Particle.js";
import type { RangedAnimationValueWithRandom } from "../Options/Classes/ValueWithRandom.js";
import type { SingleOrMultiple } from "../Types/SingleOrMultiple.js";
interface ILogger {
    debug(this: void, message?: unknown, ...optionalParams: unknown[]): void;
    error(this: void, message?: unknown, ...optionalParams: unknown[]): void;
    info(this: void, message?: unknown, ...optionalParams: unknown[]): void;
    log(this: void, message?: unknown, ...optionalParams: unknown[]): void;
    verbose(this: void, message?: unknown, ...optionalParams: unknown[]): void;
    warning(this: void, message?: unknown, ...optionalParams: unknown[]): void;
}
export declare function setLogger(logger: ILogger): void;
export declare function getLogger(): ILogger;
export declare function isSsr(): boolean;
export declare function hasMatchMedia(): boolean;
export declare function safeMatchMedia(query: string): MediaQueryList | undefined;
export declare function safeIntersectionObserver(callback: (records: IntersectionObserverEntry[]) => void): IntersectionObserver | undefined;
export declare function safeMutationObserver(callback: (records: MutationRecord[]) => void): MutationObserver | undefined;
export declare function isInArray<T>(value: T, array: SingleOrMultiple<T>): boolean;
export declare function loadFont(font?: string, weight?: string): Promise<void>;
export declare function arrayRandomIndex<T>(array: T[]): number;
export declare function itemFromArray<T>(array: T[], index?: number, useIndex?: boolean): T;
export declare function isPointInside(point: ICoordinates, size: IDimension, offset: ICoordinates, radius?: number, direction?: OutModeDirection): boolean;
export declare function areBoundsInside(bounds: IBounds, size: IDimension, offset: ICoordinates, direction?: OutModeDirection): boolean;
export declare function calculateBounds(point: ICoordinates, radius: number): IBounds;
export declare function deepExtend(destination: unknown, ...sources: unknown[]): unknown;
export declare function isDivModeEnabled(mode: string, divs: SingleOrMultiple<DivEvent>): boolean;
export declare function divModeExecute(mode: string, divs: SingleOrMultiple<DivEvent>, callback: (id: string, div: DivEvent) => void): void;
export declare function singleDivModeExecute(div: DivEvent, callback: (selector: string, div: DivEvent) => void): void;
export declare function divMode<T extends IModeDiv>(divs?: SingleOrMultiple<T>, element?: HTMLElement): T | undefined;
export declare function circleBounceDataFromParticle(p: Particle): ICircleBouncer;
export declare function circleBounce(p1: ICircleBouncer, p2: ICircleBouncer): void;
export declare function rectBounce(particle: Particle, divBounds: IBounds): void;
export declare function executeOnSingleOrMultiple<T, U = void>(obj: SingleOrMultiple<T>, callback: (obj: T, index: number) => U): SingleOrMultiple<U>;
export declare function itemFromSingleOrMultiple<T>(obj: SingleOrMultiple<T>, index?: number, useIndex?: boolean): T;
export declare function findItemFromSingleOrMultiple<T>(obj: SingleOrMultiple<T>, callback: (obj: T, index: number) => boolean): T | undefined;
export declare function initParticleNumericAnimationValue(options: RangedAnimationValueWithRandom, pxRatio: number): IParticleNumericValueAnimation;
export declare function getPosition(position: ICoordinatesWithMode, canvasSize: IDimension): ICoordinates;
export declare function getSize(size: IDimensionWithMode, canvasSize: IDimension): IDimension;
export declare function updateAnimation(particle: Particle, data: IParticleNumericValueAnimation, changeDirection: boolean, destroyType: DestroyType | keyof typeof DestroyType, delta: IDelta): void;
export declare function cloneStyle(style: Partial<CSSStyleDeclaration>): CSSStyleDeclaration;
export declare const getFullScreenStyle: (zIndex: number) => CSSStyleDeclaration;
export {};
