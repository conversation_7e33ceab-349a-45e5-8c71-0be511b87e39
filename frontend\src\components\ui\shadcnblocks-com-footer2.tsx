import React from 'react';
import { Link } from 'react-router-dom';

interface MenuItem {
  title: string;
  links: {
    text: string;
    url: string;
  }[];
}

interface Footer2Props {
  logo?: {
    url: string;
    src: string;
    alt: string;
    title: string;
  };
  tagline?: string;
  menuItems?: MenuItem[];
  copyright?: string;
  bottomLinks?: {
    text: string;
    url: string;
  }[];
}

const Footer2 = ({
  logo = {
    src: "/favicon.png",
    alt: "Intelermate",
    title: "Intelermate",
    url: "/",
  },
  tagline = "Connecting student developers with startups for affordable web services.",
  menuItems = [
    {
      title: "Services",
      links: [
        { text: "Website Development", url: "/services/website-development" },
        { text: "Web Applications", url: "/services/web-application-development" },
        { text: "E-commerce Solutions", url: "/services/ecommerce-solutions" },
        { text: "UI/UX Design", url: "/services/uiux-design" },
        { text: "SEO & Marketing", url: "/services/seo-digital-marketing" },
      ],
    },
    {
      title: "Company",
      links: [
        { text: "About", url: "/about" },
        { text: "Blog", url: "/blog" },
        { text: "Careers", url: "/careers" },
        { text: "Contact", url: "/#contact" },
        { text: "FAQ", url: "/faq" },
      ],
    },
    {
      title: "Resources",
      links: [
        { text: "Consultation", url: "/consultation" },
        { text: "Portfolio", url: "/#portfolio" },
        { text: "Testimonials", url: "/#testimonials" },
      ],
    },
    {
      title: "Legal",
      links: [
        { text: "Privacy Policy", url: "/privacy-policy" },
        { text: "Terms & Conditions", url: "/terms-conditions" },
      ],
    },
  ],
  copyright = "© 2024 Intelermate. All rights reserved.",
  bottomLinks = [
    { text: "Terms and Conditions", url: "/terms-conditions" },
    { text: "Privacy Policy", url: "/privacy-policy" },
  ],
}: Footer2Props) => {
  return (
    <section className="py-32 bg-black">
      <div className="container mx-auto px-4 md:px-6">
        <footer>
          <div className="grid grid-cols-2 gap-8 lg:grid-cols-6">
            <div className="col-span-2 mb-8 lg:mb-0">
              <div className="flex items-center gap-2 lg:justify-start">
                <Link to={logo.url}>
                  <img
                    src={logo.src}
                    alt={logo.alt}
                    title={logo.title}
                    className="h-10"
                  />
                </Link>
                <p className="text-xl font-semibold text-white">{logo.title}</p>
              </div>
              <p className="mt-4 font-bold text-gray-300">{tagline}</p>
            </div>
            {menuItems.map((section, sectionIdx) => (
              <div key={sectionIdx}>
                <h3 className="mb-4 font-bold text-white">{section.title}</h3>
                <ul className="space-y-4 text-gray-400">
                  {section.links.map((link, linkIdx) => (
                    <li
                      key={linkIdx}
                      className="font-medium hover:text-purple-400 transition-colors"
                    >
                      {link.url.startsWith('/') ? (
                        <Link to={link.url}>{link.text}</Link>
                      ) : (
                        <a href={link.url}>{link.text}</a>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
          <div className="mt-24 flex flex-col justify-between gap-4 border-t border-white/10 pt-8 text-sm font-medium text-gray-400 md:flex-row md:items-center">
            <p>{copyright}</p>
            <ul className="flex gap-4">
              {bottomLinks.map((link, linkIdx) => (
                <li key={linkIdx} className="underline hover:text-purple-400 transition-colors">
                  {link.url.startsWith('/') ? (
                    <Link to={link.url}>{link.text}</Link>
                  ) : (
                    <a href={link.url}>{link.text}</a>
                  )}
                </li>
              ))}
            </ul>
          </div>
        </footer>
      </div>
    </section>
  );
};

export { Footer2 };
