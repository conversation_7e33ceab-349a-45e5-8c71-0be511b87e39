import React from 'react';
import { Parallax } from 'react-scroll-parallax';
import { ServiceGradientCards } from './ui/service-gradient-cards';

const Services = () => {

  return (
    <section id="services" className="py-20 bg-black relative overflow-hidden min-h-screen">
      {/* Background elements with subtle parallax */}

      {/* Grid pattern for depth */}
      <Parallax speed={-5} className="absolute inset-0 z-0 opacity-5">
        <div className="w-full h-full" style={{
          backgroundImage: 'linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px), linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px)',
          backgroundSize: '80px 80px'
        }}></div>
      </Parallax>

      {/* Large Gradient Heading - positioned to span across cards area */}
      <div className="absolute top-20 left-1/2 transform -translate-x-1/2 -translate-y-1/4 pointer-events-none z-10">
        <h1
          className="text-[8rem] sm:text-[16rem] md:text-[10rem] lg:text-[10rem] xl:text-[10rem] font-black leading-none select-none"
          style={{
            background: 'linear-gradient(135deg, rgba(147, 51, 234, 0.8) 0%, rgba(59, 130, 246, 0.8) 50%, rgba(147, 51, 234, 0.4) 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            textShadow: '0 0 100px rgba(147, 51, 234, 0.3)',
          }}
        >
          SERVICES
        </h1>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-20">
        {/* Small header section */}
        <div className="text-center mb-16 relative z-30">
          <div className="flex justify-center mb-6">

          </div>

          <h2
            className="text-2xl md:text-3xl font-bold mb-4 relative"
            style={{
              background: 'linear-gradient(135deg, #9333ea 0%, #3b82f6 50%, #9333ea 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
            }}
          >

            {/* Quick Delivery badge */}
            <div className="md:absolute md:-top-10 md:right-0 hidden md:block z-10 transform -rotate-6">
              <div className="bg-gradient-to-r from-purple-600 to-blue-500 text-white px-2 py-1 md:px-4 md:py-2 rounded-lg shadow-lg relative overflow-hidden scale-75 md:scale-100">
                <span className="relative z-10 font-bold text-xs md:text-sm">Quick Delivery!</span>

                {/* Static dots/sparkles */}
                <div className="absolute top-1 right-1 w-1 h-1 bg-white rounded-full" />
                <div className="absolute bottom-1 left-2 w-1 h-1 bg-white rounded-full" />
              </div>
            </div>
          </h2>


        </div>

        {/* Service Gradient Cards - positioned above the middle of large text */}
        <div className="relative z-20">
          <ServiceGradientCards />
        </div>
      </div>
    </section>
  );
};

export default Services;
