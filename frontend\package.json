{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.3", "@types/gl-matrix": "^2.4.5", "axios": "^1.8.4", "framer-motion": "^12.6.5", "gl-matrix": "^3.4.3", "lodash": "^4.17.21", "motion": "^12.23.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.0", "react-scroll-parallax": "^3.4.5", "tailwindcss": "^4.1.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}