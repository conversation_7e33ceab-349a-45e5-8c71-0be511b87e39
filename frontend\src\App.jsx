import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import ScrollToTop from './components/ScrollToTop';
import ProtectedRoute from './components/ProtectedRoute';
import AdminLayout from './components/AdminLayout';
import HomePage from './pages/HomePage';
import AboutPage from './pages/AboutPage';
import CareersPage from './pages/CareersPage';
import ConsultationPage from './pages/ConsultationPage';
import ConsultationFormPage from './pages/ConsultationFormPage';
import ApplicationFormPage from './pages/ApplicationFormPage';
import PrivacyPolicyPage from './pages/PrivacyPolicyPage';
import TermsConditionsPage from './pages/TermsConditionsPage';
import FAQPage from './pages/FAQPage';
import BlogPage from './pages/BlogPage';
import StudentDevelopersExperience from './pages/blog/StudentDevelopersExperience';
import StartupsStudentDevelopers from './pages/blog/StartupsStudentDevelopers';
import WebDesignTrends from './pages/blog/WebDesignTrends';
import WebDevelopmentTrends from './pages/blog/WebDevelopmentTrends';
import PortfolioWebsite from './pages/blog/PortfolioWebsite';
import WebsiteOptimization from './pages/blog/WebsiteOptimization';
import AIImpactWebDevelopment from './pages/blog/AIImpactWebDevelopment';
import EffectiveCommunication from './pages/blog/EffectiveCommunication';
import ServicesPage from './pages/ServicesPage';
import WebsiteDevelopment from './pages/services/WebsiteDevelopment';
import WebApplicationDevelopment from './pages/services/WebApplicationDevelopment';
import ECommerceSolutions from './pages/services/ECommerceSolutions';
import UIUXDesign from './pages/services/UIUXDesign';
import SEODigitalMarketing from './pages/services/SEODigitalMarketing';
import AdminLoginPage from './pages/AdminLoginPage';
import AdminDashboardPage from './pages/AdminDashboardPage';
import AdminConsultationsPage from './pages/AdminConsultationsPage';
import AdminApplicationsPage from './pages/AdminApplicationsPage';
import AdminContactsPage from './pages/AdminContactsPage';
import AdminUsersPage from './pages/AdminUsersPage';
import ComponentsDemo from './pages/ComponentsDemo';

const App = () => {
  return (
    <Router>
      <ScrollToTop />
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<HomePage />} />
          <Route path="about" element={<AboutPage />} />
          <Route path="services" element={<ServicesPage />} />
          <Route path="services/website-development" element={<WebsiteDevelopment />} />
          <Route path="services/web-application-development" element={<WebApplicationDevelopment />} />
          <Route path="services/ecommerce-solutions" element={<ECommerceSolutions />} />
          <Route path="services/uiux-design" element={<UIUXDesign />} />
          <Route path="services/seo-digital-marketing" element={<SEODigitalMarketing />} />
          <Route path="careers" element={<CareersPage />} />
          <Route path="consultation" element={<ConsultationPage />} />
          <Route path="consultation-form" element={<ConsultationFormPage />} />
          <Route path="apply" element={<ApplicationFormPage />} />
          <Route path="privacy-policy" element={<PrivacyPolicyPage />} />
          <Route path="terms-conditions" element={<TermsConditionsPage />} />
          <Route path="faq" element={<FAQPage />} />
          <Route path="components-demo" element={<ComponentsDemo />} />
          <Route path="blog" element={<BlogPage />} />
          <Route path="blog/student-developers-real-world-experience" element={<StudentDevelopersExperience />} />
          <Route path="blog/startups-student-developers-benefits" element={<StartupsStudentDevelopers />} />
          <Route path="blog/web-design-trends-2025" element={<WebDesignTrends />} />
          <Route path="blog/future-web-development-trends-2025" element={<WebDevelopmentTrends />} />
          <Route path="blog/first-portfolio-website-student-developer" element={<PortfolioWebsite />} />
          <Route path="blog/website-optimization-seo-performance" element={<WebsiteOptimization />} />
          <Route path="blog/ai-impact-modern-web-development" element={<AIImpactWebDevelopment />} />
          <Route path="blog/communication-strategies-remote-development-teams" element={<EffectiveCommunication />} />
        </Route>

        {/* Admin Routes */}
        <Route path="admin/login" element={<AdminLoginPage />} />
        <Route path="admin" element={
          <ProtectedRoute>
            <AdminLayout />
          </ProtectedRoute>
        }>
          <Route index element={<AdminDashboardPage />} />
          <Route path="dashboard" element={<AdminDashboardPage />} />
          <Route path="consultations" element={<AdminConsultationsPage />} />
          <Route path="applications" element={<AdminApplicationsPage />} />
          <Route path="contacts" element={<AdminContactsPage />} />
          <Route path="users" element={<AdminUsersPage />} />
        </Route>
      </Routes>
    </Router>
  );
};

export default App;