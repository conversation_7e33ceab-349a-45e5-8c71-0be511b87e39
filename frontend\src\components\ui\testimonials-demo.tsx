import { TestimonialsColumn } from "@/components/ui/testimonials-columns-1";
import { motion } from "motion/react";

const testimonials = [
  {
    text: "Intelermate transformed our startup's digital presence completely. Their team of student developers delivered a website that perfectly represents our brand while ensuring excellent performance. Our user engagement increased by 60%!",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    name: "<PERSON><PERSON>",
    role: "CEO, TechVision India",
  },
  {
    text: "Working with Intelermate was the best decision for our business. They understood our requirements perfectly and created a web application that stands out in our competitive market. Exceptional quality at affordable rates.",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    name: "<PERSON><PERSON>",
    role: "Founder, InnovateBiz Solutions",
  },
  {
    text: "The support team at Intelermate is exceptional. They guided us through the entire development process and provided ongoing assistance. Their student developers are incredibly talented and professional.",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    name: "<PERSON><PERSON>",
    role: "Operations Manager, NextGen Enterprises",
  },
  {
    text: "Intelermate's seamless integration and development process enhanced our business operations significantly. The e-commerce platform they built has improved our sales by 45%. Highly recommend their services!",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    name: "Vikram Mehta",
    role: "Director, Digital Commerce Co.",
  },
  {
    text: "The robust features and quick implementation have transformed our workflow completely. Intelermate's team made us significantly more efficient with their custom web solutions and ongoing support.",
    image: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
    name: "Zainab Hussain",
    role: "Project Manager, StartupHub",
  },
  {
    text: "The smooth implementation exceeded all our expectations. Intelermate streamlined our business processes and improved our overall performance. Their affordable pricing model is perfect for startups.",
    image: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face",
    name: "Arjun Patel",
    role: "CTO, GrowthTech Solutions",
  },
  {
    text: "Our business functions improved dramatically with Intelermate's user-friendly design and development approach. The positive customer feedback we've received has been overwhelming since the launch.",
    image: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
    name: "Aliza Khan",
    role: "Marketing Director, BrandBoost",
  },
  {
    text: "Intelermate delivered a solution that exceeded our expectations. They truly understood our startup needs and enhanced our operations with their innovative web development approach and student talent.",
    image: "https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=150&h=150&fit=crop&crop=face",
    name: "Hassan Ali",
    role: "Founder, TechStart India",
  },
  {
    text: "Using Intelermate's services, our online presence and conversions improved significantly. Their team's dedication to quality and affordable pricing makes them the perfect partner for growing businesses.",
    image: "https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=150&h=150&fit=crop&crop=face",
    name: "Meera Reddy",
    role: "E-commerce Manager, ShopSmart",
  },
];

const firstColumn = testimonials.slice(0, 3);
const secondColumn = testimonials.slice(3, 6);
const thirdColumn = testimonials.slice(6, 9);

const TestimonialsDemo = () => {
  return (
    <section className="bg-black my-20 relative py-20">
      <div className="container z-10 mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.1, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
          className="flex flex-col items-center justify-center max-w-[540px] mx-auto"
        >
          <div className="flex justify-center">
            <div className="border border-purple-500/30 py-1 px-4 rounded-lg bg-purple-500/10 text-purple-300">
              Testimonials
            </div>
          </div>

          <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold tracking-tighter mt-5 text-white text-center">
            What our <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-500">clients</span> say
          </h2>
          <p className="text-center mt-5 opacity-75 text-gray-300">
            See what our customers have to say about working with our talented student developers.
          </p>
        </motion.div>

        <div className="flex justify-center gap-6 mt-10 [mask-image:linear-gradient(to_bottom,transparent,black_25%,black_75%,transparent)] max-h-[740px] overflow-hidden">
          <TestimonialsColumn testimonials={firstColumn} duration={15} />
          <TestimonialsColumn testimonials={secondColumn} className="hidden md:block" duration={19} />
          <TestimonialsColumn testimonials={thirdColumn} className="hidden lg:block" duration={17} />
        </div>
      </div>
    </section>
  );
};

export default TestimonialsDemo;
