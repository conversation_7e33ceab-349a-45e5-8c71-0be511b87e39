import type { IColor, IHsl, IHsla, IRangeColor, IRgb, IRgba } from "../Core/Interfaces/Colors.js";
import type { Engine } from "../Core/Engine.js";
import type { HslAnimation } from "../Options/Classes/HslAnimation.js";
import type { IDelta } from "../Core/Interfaces/IDelta.js";
import type { IOptionsColor } from "../Options/Interfaces/IOptionsColor.js";
import type { IParticleColorAnimation } from "../Core/Interfaces/IParticleValueAnimation.js";
import type { IParticleHslAnimation } from "../Core/Interfaces/IParticleHslAnimation.js";
import type { IRangeValue } from "../Core/Interfaces/IRangeValue.js";
import type { Particle } from "../Core/Particle.js";
export declare function rangeColorToRgb(engine: Engine, input?: string | IRangeColor, index?: number, useIndex?: boolean): IRgb | undefined;
export declare function colorToRgb(engine: Engine, input?: string | IColor, index?: number, useIndex?: boolean): IRgb | undefined;
export declare function colorToHsl(engine: Engine, color: string | IColor | undefined, index?: number, useIndex?: boolean): IHsl | undefined;
export declare function rangeColorToHsl(engine: Engine, color: string | IRangeColor | undefined, index?: number, useIndex?: boolean): IHsl | undefined;
export declare function rgbToHsl(color: IRgb): IHsl;
export declare function stringToAlpha(engine: Engine, input: string): number | undefined;
export declare function stringToRgb(engine: Engine, input: string): IRgb | undefined;
export declare function hslToRgb(hsl: IHsl): IRgb;
export declare function hslaToRgba(hsla: IHsla): IRgba;
export declare function getRandomRgbColor(min?: number): IRgb;
export declare function getStyleFromRgb(color: IRgb, opacity?: number): string;
export declare function getStyleFromHsl(color: IHsl, opacity?: number): string;
export declare function colorMix(color1: IRgb | IHsl, color2: IRgb | IHsl, size1: number, size2: number): IRgb;
export declare function getLinkColor(p1: Particle, p2?: Particle, linkColor?: string | IRgb): IRgb | undefined;
export declare function getLinkRandomColor(engine: Engine, optColor: string | IOptionsColor, blink: boolean, consent: boolean): IRgb | string | undefined;
export declare function getHslFromAnimation(animation?: IParticleHslAnimation): IHsl | undefined;
export declare function getHslAnimationFromHsl(hsl: IHsl, animationOptions: HslAnimation | undefined, reduceFactor: number): IParticleHslAnimation;
export declare function updateColorValue(data: IParticleColorAnimation, range: IRangeValue, decrease: boolean, delta: IDelta): void;
export declare function updateColor(color: IParticleHslAnimation | undefined, delta: IDelta): void;
