import React from 'react';
import { Demo, Footer2Demo, TestimonialsDemo } from '../components/ui/demo';
import { ServiceGradientCards } from '../components/ui/service-gradient-cards';
import SEO from '../components/SEO';

const ComponentsDemo = () => {
  return (
    <>
      <SEO
        title="Components Demo"
        description="Demo page showcasing the integrated components"
      />
      
      {/* Hero Section */}
      <section className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-8">
            Components Demo
          </h1>
          <p className="text-xl text-gray-300 mb-12">
            Showcasing the integrated gradient cards and footer components
          </p>
        </div>
      </section>

      {/* Gradient Card Demo */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-3xl font-bold text-white text-center mb-16">
            Single Gradient Card Demo
          </h2>
          <div className="flex justify-center">
            <Demo />
          </div>
        </div>
      </section>

      {/* Service Cards Demo */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-3xl font-bold text-white text-center mb-16">
            Service Gradient Cards
          </h2>
          <ServiceGradientCards />
        </div>
      </section>

      {/* Testimonials Demo */}
      <section className="bg-black">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-3xl font-bold text-white text-center mb-16">
            New Testimonials Component
          </h2>
          <TestimonialsDemo />
        </div>
      </section>

      {/* Footer Demo */}
      <section className="bg-black">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-3xl font-bold text-white text-center mb-16">
            New Footer Component
          </h2>
          <Footer2Demo />
        </div>
      </section>
    </>
  );
};

export default ComponentsDemo;
