(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "./Constants.js", "../../Utils/TypeUtils.js"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Vector = exports.Vector3d = void 0;
    const Constants_js_1 = require("./Constants.js");
    const TypeUtils_js_1 = require("../../Utils/TypeUtils.js");
    class Vector3d {
        constructor(xOrCoords, y, z) {
            this._updateFromAngle = (angle, length) => {
                this.x = Math.cos(angle) * length;
                this.y = Math.sin(angle) * length;
            };
            if (!(0, TypeUtils_js_1.isNumber)(xOrCoords) && xOrCoords) {
                this.x = xOrCoords.x;
                this.y = xOrCoords.y;
                const coords3d = xOrCoords;
                this.z = coords3d.z ? coords3d.z : Constants_js_1.originPoint.z;
            }
            else if (xOrCoords !== undefined && y !== undefined) {
                this.x = xOrCoords;
                this.y = y;
                this.z = z ?? Constants_js_1.originPoint.z;
            }
            else {
                throw new Error(`${Constants_js_1.errorPrefix} Vector3d not initialized correctly`);
            }
        }
        static get origin() {
            return Vector3d.create(Constants_js_1.originPoint.x, Constants_js_1.originPoint.y, Constants_js_1.originPoint.z);
        }
        get angle() {
            return Math.atan2(this.y, this.x);
        }
        set angle(angle) {
            this._updateFromAngle(angle, this.length);
        }
        get length() {
            return Math.sqrt(this.getLengthSq());
        }
        set length(length) {
            this._updateFromAngle(this.angle, length);
        }
        static clone(source) {
            return Vector3d.create(source.x, source.y, source.z);
        }
        static create(x, y, z) {
            return new Vector3d(x, y, z);
        }
        add(v) {
            return Vector3d.create(this.x + v.x, this.y + v.y, this.z + v.z);
        }
        addTo(v) {
            this.x += v.x;
            this.y += v.y;
            this.z += v.z;
        }
        copy() {
            return Vector3d.clone(this);
        }
        distanceTo(v) {
            return this.sub(v).length;
        }
        distanceToSq(v) {
            return this.sub(v).getLengthSq();
        }
        div(n) {
            return Vector3d.create(this.x / n, this.y / n, this.z / n);
        }
        divTo(n) {
            this.x /= n;
            this.y /= n;
            this.z /= n;
        }
        getLengthSq() {
            return this.x ** Constants_js_1.squareExp + this.y ** Constants_js_1.squareExp;
        }
        mult(n) {
            return Vector3d.create(this.x * n, this.y * n, this.z * n);
        }
        multTo(n) {
            this.x *= n;
            this.y *= n;
            this.z *= n;
        }
        normalize() {
            const length = this.length;
            if (length != Constants_js_1.none) {
                this.multTo(Constants_js_1.inverseFactorNumerator / length);
            }
        }
        rotate(angle) {
            return Vector3d.create(this.x * Math.cos(angle) - this.y * Math.sin(angle), this.x * Math.sin(angle) + this.y * Math.cos(angle), Constants_js_1.originPoint.z);
        }
        setTo(c) {
            this.x = c.x;
            this.y = c.y;
            const v3d = c;
            this.z = v3d.z ? v3d.z : Constants_js_1.originPoint.z;
        }
        sub(v) {
            return Vector3d.create(this.x - v.x, this.y - v.y, this.z - v.z);
        }
        subFrom(v) {
            this.x -= v.x;
            this.y -= v.y;
            this.z -= v.z;
        }
    }
    exports.Vector3d = Vector3d;
    class Vector extends Vector3d {
        constructor(xOrCoords, y) {
            super(xOrCoords, y, Constants_js_1.originPoint.z);
        }
        static get origin() {
            return Vector.create(Constants_js_1.originPoint.x, Constants_js_1.originPoint.y);
        }
        static clone(source) {
            return Vector.create(source.x, source.y);
        }
        static create(x, y) {
            return new Vector(x, y);
        }
    }
    exports.Vector = Vector;
});
