import React, { useEffect } from 'react';
import Hero from '../components/Hero';
import Services from '../components/Services';
import Portfolio from '../components/Portfolio';
import Testimonials from '../components/Testimonials';
import CallToAction from '../components/CallToAction';
import SEO from '../components/SEO';

const HomePage = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Define schema for homepage
  const homePageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Intelermate - Web Services Agency",
    "description": "Connecting student developers with startups for affordable web services. We design and develop stunning websites and web applications.",
    "url": "https://www.intelermate.tech/",
    "mainEntity": {
      "@type": "Organization",
      "name": "Intelermate",
      "url": "https://www.intelermate.tech"
    }
  };

  return (
    <>
      <SEO
        title="Web Services Agency"
        description="Connecting student developers with startups for affordable web services. We design and develop stunning websites and web applications that drive growth."
        schema={homePageSchema}
      />
      <Hero />
      <Services />
      <Portfolio />
      <CallToAction />
    </>
  );
};

export default HomePage;
