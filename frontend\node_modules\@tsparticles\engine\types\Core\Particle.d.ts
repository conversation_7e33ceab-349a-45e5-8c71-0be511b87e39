import type { ICenterCoordinates, ICoordinates, ICoordinates3d } from "./Interfaces/ICoordinates.js";
import type { IHsl, IRgb } from "./Interfaces/Colors.js";
import { Vector, Vector3d } from "./Utils/Vectors.js";
import type { Container } from "./Container.js";
import type { Engine } from "./Engine.js";
import type { IBubbleParticleData } from "./Interfaces/IBubbleParticleData.js";
import type { IDelta } from "./Interfaces/IDelta.js";
import type { IMovePathGenerator } from "./Interfaces/IMovePathGenerator.js";
import type { IParticleHslAnimation } from "./Interfaces/IParticleHslAnimation.js";
import type { IParticleNumericValueAnimation } from "./Interfaces/IParticleValueAnimation.js";
import type { IParticleRetinaProps } from "./Interfaces/IParticleRetinaProps.js";
import type { IParticleRoll } from "./Interfaces/IParticleRoll.js";
import type { IParticlesOptions } from "../Options/Interfaces/Particles/IParticlesOptions.js";
import type { IShapeValues } from "./Interfaces/IShapeValues.js";
import type { ISlowParticleData } from "./Interfaces/ISlowParticleData.js";
import { Interactivity } from "../Options/Classes/Interactivity/Interactivity.js";
import { ParticleOutType } from "../Enums/Types/ParticleOutType.js";
import type { ParticlesOptions } from "../Options/Classes/Particles/ParticlesOptions.js";
import type { RecursivePartial } from "../Types/RecursivePartial.js";
export declare class Particle {
    readonly container: Container;
    backColor?: IHsl;
    bubble: IBubbleParticleData;
    color?: IParticleHslAnimation;
    destroyed: boolean;
    direction: number;
    effect: string;
    effectClose: boolean;
    effectData?: IShapeValues;
    effectFill: boolean;
    group?: string;
    id: number;
    ignoresResizeRatio: boolean;
    initialPosition: Vector;
    initialVelocity: Vector;
    interactivity: Interactivity;
    isRotating: boolean;
    lastPathTime: number;
    misplaced: boolean;
    moveCenter: ICenterCoordinates;
    moveDecay: number;
    offset: Vector;
    opacity?: IParticleNumericValueAnimation;
    options: ParticlesOptions;
    outType: ParticleOutType;
    pathDelay: number;
    pathGenerator?: IMovePathGenerator;
    pathRotation: boolean;
    position: Vector3d;
    randomIndexData?: number;
    retina: IParticleRetinaProps;
    roll?: IParticleRoll;
    rotation: number;
    shadowColor?: IRgb;
    shape: string;
    shapeClose: boolean;
    shapeData?: IShapeValues;
    shapeFill: boolean;
    sides: number;
    size: IParticleNumericValueAnimation;
    slow: ISlowParticleData;
    spawning: boolean;
    strokeColor?: IParticleHslAnimation;
    strokeOpacity?: number;
    strokeWidth?: number;
    unbreakable: boolean;
    velocity: Vector;
    zIndexFactor: number;
    private readonly _engine;
    constructor(engine: Engine, container: Container);
    destroy(override?: boolean): void;
    draw(delta: IDelta): void;
    getFillColor(): IHsl | undefined;
    getMass(): number;
    getPosition(): ICoordinates3d;
    getRadius(): number;
    getStrokeColor(): IHsl | undefined;
    init(id: number, position?: ICoordinates, overrideOptions?: RecursivePartial<IParticlesOptions>, group?: string): void;
    isInsideCanvas(): boolean;
    isVisible(): boolean;
    reset(): void;
    private readonly _calcPosition;
    private readonly _calculateVelocity;
    private readonly _checkOverlap;
    private readonly _getRollColor;
    private readonly _initPosition;
}
