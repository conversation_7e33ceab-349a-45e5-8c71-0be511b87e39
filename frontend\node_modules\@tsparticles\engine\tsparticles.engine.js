/*!
 * tsParticles Engine v3.8.1
 * Author: <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Website: https://particles.js.org/
 * Confetti Website: https://confetti.js.org
 * GitHub: https://www.github.com/matteobruni/tsparticles
 * How to use?: Check the GitHub README
 * ------------------------------------------------------
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else {
		var a = factory();
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, () => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/Core/Canvas.js":
/*!*************************************!*\
  !*** ./dist/browser/Core/Canvas.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Canvas: () => (/* binding */ Canvas)\n/* harmony export */ });\n/* harmony import */ var _Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Utils/CanvasUtils.js */ \"./dist/browser/Utils/CanvasUtils.js\");\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Utils/Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n/* harmony import */ var _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../Utils/ColorUtils.js */ \"./dist/browser/Utils/ColorUtils.js\");\n\n\n\n\nfunction setTransformValue(factor, newFactor, key) {\n  const newValue = newFactor[key];\n  if (newValue !== undefined) {\n    factor[key] = (factor[key] ?? _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultTransformValue) * newValue;\n  }\n}\nfunction setStyle(canvas, style, important = false) {\n  if (!style) {\n    return;\n  }\n  const element = canvas;\n  if (!element) {\n    return;\n  }\n  const elementStyle = element.style;\n  if (!elementStyle) {\n    return;\n  }\n  const keys = new Set();\n  for (const key in elementStyle) {\n    if (!Object.prototype.hasOwnProperty.call(elementStyle, key)) {\n      continue;\n    }\n    keys.add(elementStyle[key]);\n  }\n  for (const key in style) {\n    if (!Object.prototype.hasOwnProperty.call(style, key)) {\n      continue;\n    }\n    keys.add(style[key]);\n  }\n  for (const key of keys) {\n    const value = style.getPropertyValue(key);\n    if (!value) {\n      elementStyle.removeProperty(key);\n    } else {\n      elementStyle.setProperty(key, value, important ? \"important\" : \"\");\n    }\n  }\n}\nclass Canvas {\n  constructor(container, engine) {\n    this.container = container;\n    this._applyPostDrawUpdaters = particle => {\n      for (const updater of this._postDrawUpdaters) {\n        updater.afterDraw?.(particle);\n      }\n    };\n    this._applyPreDrawUpdaters = (ctx, particle, radius, zOpacity, colorStyles, transform) => {\n      for (const updater of this._preDrawUpdaters) {\n        if (updater.getColorStyles) {\n          const {\n            fill,\n            stroke\n          } = updater.getColorStyles(particle, ctx, radius, zOpacity);\n          if (fill) {\n            colorStyles.fill = fill;\n          }\n          if (stroke) {\n            colorStyles.stroke = stroke;\n          }\n        }\n        if (updater.getTransformValues) {\n          const updaterTransform = updater.getTransformValues(particle);\n          for (const key in updaterTransform) {\n            setTransformValue(transform, updaterTransform, key);\n          }\n        }\n        updater.beforeDraw?.(particle);\n      }\n    };\n    this._applyResizePlugins = () => {\n      for (const plugin of this._resizePlugins) {\n        plugin.resize?.();\n      }\n    };\n    this._getPluginParticleColors = particle => {\n      let fColor, sColor;\n      for (const plugin of this._colorPlugins) {\n        if (!fColor && plugin.particleFillColor) {\n          fColor = (0,_Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_1__.rangeColorToHsl)(this._engine, plugin.particleFillColor(particle));\n        }\n        if (!sColor && plugin.particleStrokeColor) {\n          sColor = (0,_Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_1__.rangeColorToHsl)(this._engine, plugin.particleStrokeColor(particle));\n        }\n        if (fColor && sColor) {\n          break;\n        }\n      }\n      return [fColor, sColor];\n    };\n    this._initCover = async () => {\n      const options = this.container.actualOptions,\n        cover = options.backgroundMask.cover,\n        color = cover.color;\n      if (color) {\n        const coverRgb = (0,_Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_1__.rangeColorToRgb)(this._engine, color);\n        if (coverRgb) {\n          const coverColor = {\n            ...coverRgb,\n            a: cover.opacity\n          };\n          this._coverColorStyle = (0,_Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_1__.getStyleFromRgb)(coverColor, coverColor.a);\n        }\n      } else {\n        await new Promise((resolve, reject) => {\n          if (!cover.image) {\n            return;\n          }\n          const img = document.createElement(\"img\");\n          img.addEventListener(\"load\", () => {\n            this._coverImage = {\n              image: img,\n              opacity: cover.opacity\n            };\n            resolve();\n          });\n          img.addEventListener(\"error\", evt => {\n            reject(evt.error);\n          });\n          img.src = cover.image;\n        });\n      }\n    };\n    this._initStyle = () => {\n      const element = this.element,\n        options = this.container.actualOptions;\n      if (!element) {\n        return;\n      }\n      if (this._fullScreen) {\n        this._setFullScreenStyle();\n      } else {\n        this._resetOriginalStyle();\n      }\n      for (const key in options.style) {\n        if (!key || !options.style || !Object.prototype.hasOwnProperty.call(options.style, key)) {\n          continue;\n        }\n        const value = options.style[key];\n        if (!value) {\n          continue;\n        }\n        element.style.setProperty(key, value, \"important\");\n      }\n    };\n    this._initTrail = async () => {\n      const options = this.container.actualOptions,\n        trail = options.particles.move.trail,\n        trailFill = trail.fill;\n      if (!trail.enable) {\n        return;\n      }\n      const opacity = _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.inverseFactorNumerator / trail.length;\n      if (trailFill.color) {\n        const fillColor = (0,_Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_1__.rangeColorToRgb)(this._engine, trailFill.color);\n        if (!fillColor) {\n          return;\n        }\n        this._trailFill = {\n          color: {\n            ...fillColor\n          },\n          opacity\n        };\n      } else {\n        await new Promise((resolve, reject) => {\n          if (!trailFill.image) {\n            return;\n          }\n          const img = document.createElement(\"img\");\n          img.addEventListener(\"load\", () => {\n            this._trailFill = {\n              image: img,\n              opacity\n            };\n            resolve();\n          });\n          img.addEventListener(\"error\", evt => {\n            reject(evt.error);\n          });\n          img.src = trailFill.image;\n        });\n      }\n    };\n    this._paintBase = baseColor => {\n      this.draw(ctx => (0,_Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_2__.paintBase)(ctx, this.size, baseColor));\n    };\n    this._paintImage = (image, opacity) => {\n      this.draw(ctx => (0,_Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_2__.paintImage)(ctx, this.size, image, opacity));\n    };\n    this._repairStyle = () => {\n      const element = this.element;\n      if (!element) {\n        return;\n      }\n      this._safeMutationObserver(observer => observer.disconnect());\n      this._initStyle();\n      this.initBackground();\n      this._safeMutationObserver(observer => {\n        if (!element || !(element instanceof Node)) {\n          return;\n        }\n        observer.observe(element, {\n          attributes: true\n        });\n      });\n    };\n    this._resetOriginalStyle = () => {\n      const element = this.element,\n        originalStyle = this._originalStyle;\n      if (!element || !originalStyle) {\n        return;\n      }\n      setStyle(element, originalStyle, true);\n    };\n    this._safeMutationObserver = callback => {\n      if (!this._mutationObserver) {\n        return;\n      }\n      callback(this._mutationObserver);\n    };\n    this._setFullScreenStyle = () => {\n      const element = this.element;\n      if (!element) {\n        return;\n      }\n      setStyle(element, (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_3__.getFullScreenStyle)(this.container.actualOptions.fullScreen.zIndex), true);\n    };\n    this._engine = engine;\n    this._standardSize = {\n      height: 0,\n      width: 0\n    };\n    const pxRatio = container.retina.pixelRatio,\n      stdSize = this._standardSize;\n    this.size = {\n      height: stdSize.height * pxRatio,\n      width: stdSize.width * pxRatio\n    };\n    this._context = null;\n    this._generated = false;\n    this._preDrawUpdaters = [];\n    this._postDrawUpdaters = [];\n    this._resizePlugins = [];\n    this._colorPlugins = [];\n  }\n  get _fullScreen() {\n    return this.container.actualOptions.fullScreen.enable;\n  }\n  clear() {\n    const options = this.container.actualOptions,\n      trail = options.particles.move.trail,\n      trailFill = this._trailFill;\n    if (options.backgroundMask.enable) {\n      this.paint();\n    } else if (trail.enable && trail.length > _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minimumLength && trailFill) {\n      if (trailFill.color) {\n        this._paintBase((0,_Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_1__.getStyleFromRgb)(trailFill.color, trailFill.opacity));\n      } else if (trailFill.image) {\n        this._paintImage(trailFill.image, trailFill.opacity);\n      }\n    } else if (options.clear) {\n      this.draw(ctx => {\n        (0,_Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_2__.clear)(ctx, this.size);\n      });\n    }\n  }\n  destroy() {\n    this.stop();\n    if (this._generated) {\n      const element = this.element;\n      element?.remove();\n      this.element = undefined;\n    } else {\n      this._resetOriginalStyle();\n    }\n    this._preDrawUpdaters = [];\n    this._postDrawUpdaters = [];\n    this._resizePlugins = [];\n    this._colorPlugins = [];\n  }\n  draw(cb) {\n    const ctx = this._context;\n    if (!ctx) {\n      return;\n    }\n    return cb(ctx);\n  }\n  drawAsync(cb) {\n    const ctx = this._context;\n    if (!ctx) {\n      return undefined;\n    }\n    return cb(ctx);\n  }\n  drawParticle(particle, delta) {\n    if (particle.spawning || particle.destroyed) {\n      return;\n    }\n    const radius = particle.getRadius();\n    if (radius <= _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minimumSize) {\n      return;\n    }\n    const pfColor = particle.getFillColor(),\n      psColor = particle.getStrokeColor() ?? pfColor;\n    let [fColor, sColor] = this._getPluginParticleColors(particle);\n    if (!fColor) {\n      fColor = pfColor;\n    }\n    if (!sColor) {\n      sColor = psColor;\n    }\n    if (!fColor && !sColor) {\n      return;\n    }\n    this.draw(ctx => {\n      const container = this.container,\n        options = container.actualOptions,\n        zIndexOptions = particle.options.zIndex,\n        zIndexFactor = _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.zIndexFactorOffset - particle.zIndexFactor,\n        zOpacityFactor = zIndexFactor ** zIndexOptions.opacityRate,\n        opacity = particle.bubble.opacity ?? particle.opacity?.value ?? _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultOpacity,\n        strokeOpacity = particle.strokeOpacity ?? opacity,\n        zOpacity = opacity * zOpacityFactor,\n        zStrokeOpacity = strokeOpacity * zOpacityFactor,\n        transform = {},\n        colorStyles = {\n          fill: fColor ? (0,_Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_1__.getStyleFromHsl)(fColor, zOpacity) : undefined\n        };\n      colorStyles.stroke = sColor ? (0,_Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_1__.getStyleFromHsl)(sColor, zStrokeOpacity) : colorStyles.fill;\n      this._applyPreDrawUpdaters(ctx, particle, radius, zOpacity, colorStyles, transform);\n      (0,_Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_2__.drawParticle)({\n        container,\n        context: ctx,\n        particle,\n        delta,\n        colorStyles,\n        backgroundMask: options.backgroundMask.enable,\n        composite: options.backgroundMask.composite,\n        radius: radius * zIndexFactor ** zIndexOptions.sizeRate,\n        opacity: zOpacity,\n        shadow: particle.options.shadow,\n        transform\n      });\n      this._applyPostDrawUpdaters(particle);\n    });\n  }\n  drawParticlePlugin(plugin, particle, delta) {\n    this.draw(ctx => (0,_Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_2__.drawParticlePlugin)(ctx, plugin, particle, delta));\n  }\n  drawPlugin(plugin, delta) {\n    this.draw(ctx => (0,_Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_2__.drawPlugin)(ctx, plugin, delta));\n  }\n  async init() {\n    this._safeMutationObserver(obs => obs.disconnect());\n    this._mutationObserver = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_3__.safeMutationObserver)(records => {\n      for (const record of records) {\n        if (record.type === \"attributes\" && record.attributeName === \"style\") {\n          this._repairStyle();\n        }\n      }\n    });\n    this.resize();\n    this._initStyle();\n    await this._initCover();\n    try {\n      await this._initTrail();\n    } catch (e) {\n      (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_3__.getLogger)().error(e);\n    }\n    this.initBackground();\n    this._safeMutationObserver(obs => {\n      if (!this.element || !(this.element instanceof Node)) {\n        return;\n      }\n      obs.observe(this.element, {\n        attributes: true\n      });\n    });\n    this.initUpdaters();\n    this.initPlugins();\n    this.paint();\n  }\n  initBackground() {\n    const options = this.container.actualOptions,\n      background = options.background,\n      element = this.element;\n    if (!element) {\n      return;\n    }\n    const elementStyle = element.style;\n    if (!elementStyle) {\n      return;\n    }\n    if (background.color) {\n      const color = (0,_Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_1__.rangeColorToRgb)(this._engine, background.color);\n      elementStyle.backgroundColor = color ? (0,_Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_1__.getStyleFromRgb)(color, background.opacity) : \"\";\n    } else {\n      elementStyle.backgroundColor = \"\";\n    }\n    elementStyle.backgroundImage = background.image || \"\";\n    elementStyle.backgroundPosition = background.position || \"\";\n    elementStyle.backgroundRepeat = background.repeat || \"\";\n    elementStyle.backgroundSize = background.size || \"\";\n  }\n  initPlugins() {\n    this._resizePlugins = [];\n    for (const plugin of this.container.plugins.values()) {\n      if (plugin.resize) {\n        this._resizePlugins.push(plugin);\n      }\n      if (plugin.particleFillColor ?? plugin.particleStrokeColor) {\n        this._colorPlugins.push(plugin);\n      }\n    }\n  }\n  initUpdaters() {\n    this._preDrawUpdaters = [];\n    this._postDrawUpdaters = [];\n    for (const updater of this.container.particles.updaters) {\n      if (updater.afterDraw) {\n        this._postDrawUpdaters.push(updater);\n      }\n      if (updater.getColorStyles ?? updater.getTransformValues ?? updater.beforeDraw) {\n        this._preDrawUpdaters.push(updater);\n      }\n    }\n  }\n  loadCanvas(canvas) {\n    if (this._generated && this.element) {\n      this.element.remove();\n    }\n    this._generated = canvas.dataset && _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.generatedAttribute in canvas.dataset ? canvas.dataset[_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.generatedAttribute] === \"true\" : this._generated;\n    this.element = canvas;\n    this.element.ariaHidden = \"true\";\n    this._originalStyle = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_3__.cloneStyle)(this.element.style);\n    const standardSize = this._standardSize;\n    standardSize.height = canvas.offsetHeight;\n    standardSize.width = canvas.offsetWidth;\n    const pxRatio = this.container.retina.pixelRatio,\n      retinaSize = this.size;\n    canvas.height = retinaSize.height = standardSize.height * pxRatio;\n    canvas.width = retinaSize.width = standardSize.width * pxRatio;\n    this._context = this.element.getContext(\"2d\");\n    this._safeMutationObserver(obs => obs.disconnect());\n    this.container.retina.init();\n    this.initBackground();\n    this._safeMutationObserver(obs => {\n      if (!this.element || !(this.element instanceof Node)) {\n        return;\n      }\n      obs.observe(this.element, {\n        attributes: true\n      });\n    });\n  }\n  paint() {\n    const options = this.container.actualOptions;\n    this.draw(ctx => {\n      if (options.backgroundMask.enable && options.backgroundMask.cover) {\n        (0,_Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_2__.clear)(ctx, this.size);\n        if (this._coverImage) {\n          this._paintImage(this._coverImage.image, this._coverImage.opacity);\n        } else if (this._coverColorStyle) {\n          this._paintBase(this._coverColorStyle);\n        } else {\n          this._paintBase();\n        }\n      } else {\n        this._paintBase();\n      }\n    });\n  }\n  resize() {\n    if (!this.element) {\n      return false;\n    }\n    const container = this.container,\n      currentSize = container.canvas._standardSize,\n      newSize = {\n        width: this.element.offsetWidth,\n        height: this.element.offsetHeight\n      },\n      pxRatio = container.retina.pixelRatio,\n      retinaSize = {\n        width: newSize.width * pxRatio,\n        height: newSize.height * pxRatio\n      };\n    if (newSize.height === currentSize.height && newSize.width === currentSize.width && retinaSize.height === this.element.height && retinaSize.width === this.element.width) {\n      return false;\n    }\n    const oldSize = {\n      ...currentSize\n    };\n    currentSize.height = newSize.height;\n    currentSize.width = newSize.width;\n    const canvasSize = this.size;\n    this.element.width = canvasSize.width = retinaSize.width;\n    this.element.height = canvasSize.height = retinaSize.height;\n    if (this.container.started) {\n      container.particles.setResizeFactor({\n        width: currentSize.width / oldSize.width,\n        height: currentSize.height / oldSize.height\n      });\n    }\n    return true;\n  }\n  stop() {\n    this._safeMutationObserver(obs => obs.disconnect());\n    this._mutationObserver = undefined;\n    this.draw(ctx => (0,_Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_2__.clear)(ctx, this.size));\n  }\n  async windowResize() {\n    if (!this.element || !this.resize()) {\n      return;\n    }\n    const container = this.container,\n      needsRefresh = container.updateActualOptions();\n    container.particles.setDensity();\n    this._applyResizePlugins();\n    if (needsRefresh) {\n      await container.refresh();\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Core/Canvas.js?");

/***/ }),

/***/ "./dist/browser/Core/Container.js":
/*!****************************************!*\
  !*** ./dist/browser/Core/Container.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Container: () => (/* binding */ Container)\n/* harmony export */ });\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n/* harmony import */ var _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Utils/Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Canvas_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Canvas.js */ \"./dist/browser/Core/Canvas.js\");\n/* harmony import */ var _Utils_EventListeners_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Utils/EventListeners.js */ \"./dist/browser/Core/Utils/EventListeners.js\");\n/* harmony import */ var _Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../Enums/Types/EventType.js */ \"./dist/browser/Enums/Types/EventType.js\");\n/* harmony import */ var _Options_Classes_Options_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../Options/Classes/Options.js */ \"./dist/browser/Options/Classes/Options.js\");\n/* harmony import */ var _Particles_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Particles.js */ \"./dist/browser/Core/Particles.js\");\n/* harmony import */ var _Retina_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Retina.js */ \"./dist/browser/Core/Retina.js\");\n/* harmony import */ var _Utils_OptionsUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Utils/OptionsUtils.js */ \"./dist/browser/Utils/OptionsUtils.js\");\n\n\n\n\n\n\n\n\n\n\nfunction guardCheck(container) {\n  return container && !container.destroyed;\n}\nfunction initDelta(value, fpsLimit = _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultFps, smooth = false) {\n  return {\n    value,\n    factor: smooth ? _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultFps / fpsLimit : _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultFps * value / _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds\n  };\n}\nfunction loadContainerOptions(engine, container, ...sourceOptionsArr) {\n  const options = new _Options_Classes_Options_js__WEBPACK_IMPORTED_MODULE_1__.Options(engine, container);\n  (0,_Utils_OptionsUtils_js__WEBPACK_IMPORTED_MODULE_2__.loadOptions)(options, ...sourceOptionsArr);\n  return options;\n}\nclass Container {\n  constructor(engine, id, sourceOptions) {\n    this._intersectionManager = entries => {\n      if (!guardCheck(this) || !this.actualOptions.pauseOnOutsideViewport) {\n        return;\n      }\n      for (const entry of entries) {\n        if (entry.target !== this.interactivity.element) {\n          continue;\n        }\n        if (entry.isIntersecting) {\n          void this.play();\n        } else {\n          this.pause();\n        }\n      }\n    };\n    this._nextFrame = timestamp => {\n      try {\n        if (!this._smooth && this._lastFrameTime !== undefined && timestamp < this._lastFrameTime + _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds / this.fpsLimit) {\n          this.draw(false);\n          return;\n        }\n        this._lastFrameTime ??= timestamp;\n        const delta = initDelta(timestamp - this._lastFrameTime, this.fpsLimit, this._smooth);\n        this.addLifeTime(delta.value);\n        this._lastFrameTime = timestamp;\n        if (delta.value > _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds) {\n          this.draw(false);\n          return;\n        }\n        this.particles.draw(delta);\n        if (!this.alive()) {\n          this.destroy();\n          return;\n        }\n        if (this.animationStatus) {\n          this.draw(false);\n        }\n      } catch (e) {\n        (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_3__.getLogger)().error(`${_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.errorPrefix} in animation loop`, e);\n      }\n    };\n    this._engine = engine;\n    this.id = Symbol(id);\n    this.fpsLimit = 120;\n    this._smooth = false;\n    this._delay = 0;\n    this._duration = 0;\n    this._lifeTime = 0;\n    this._firstStart = true;\n    this.started = false;\n    this.destroyed = false;\n    this._paused = true;\n    this._lastFrameTime = 0;\n    this.zLayers = 100;\n    this.pageHidden = false;\n    this._clickHandlers = new Map();\n    this._sourceOptions = sourceOptions;\n    this._initialSourceOptions = sourceOptions;\n    this.retina = new _Retina_js__WEBPACK_IMPORTED_MODULE_4__.Retina(this);\n    this.canvas = new _Canvas_js__WEBPACK_IMPORTED_MODULE_5__.Canvas(this, this._engine);\n    this.particles = new _Particles_js__WEBPACK_IMPORTED_MODULE_6__.Particles(this._engine, this);\n    this.pathGenerators = new Map();\n    this.interactivity = {\n      mouse: {\n        clicking: false,\n        inside: false\n      }\n    };\n    this.plugins = new Map();\n    this.effectDrawers = new Map();\n    this.shapeDrawers = new Map();\n    this._options = loadContainerOptions(this._engine, this);\n    this.actualOptions = loadContainerOptions(this._engine, this);\n    this._eventListeners = new _Utils_EventListeners_js__WEBPACK_IMPORTED_MODULE_7__.EventListeners(this);\n    this._intersectionObserver = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_3__.safeIntersectionObserver)(entries => this._intersectionManager(entries));\n    this._engine.dispatchEvent(_Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_8__.EventType.containerBuilt, {\n      container: this\n    });\n  }\n  get animationStatus() {\n    return !this._paused && !this.pageHidden && guardCheck(this);\n  }\n  get options() {\n    return this._options;\n  }\n  get sourceOptions() {\n    return this._sourceOptions;\n  }\n  addClickHandler(callback) {\n    if (!guardCheck(this)) {\n      return;\n    }\n    const el = this.interactivity.element;\n    if (!el) {\n      return;\n    }\n    const clickOrTouchHandler = (e, pos, radius) => {\n        if (!guardCheck(this)) {\n          return;\n        }\n        const pxRatio = this.retina.pixelRatio,\n          posRetina = {\n            x: pos.x * pxRatio,\n            y: pos.y * pxRatio\n          },\n          particles = this.particles.quadTree.queryCircle(posRetina, radius * pxRatio);\n        callback(e, particles);\n      },\n      clickHandler = e => {\n        if (!guardCheck(this)) {\n          return;\n        }\n        const mouseEvent = e,\n          pos = {\n            x: mouseEvent.offsetX || mouseEvent.clientX,\n            y: mouseEvent.offsetY || mouseEvent.clientY\n          };\n        clickOrTouchHandler(e, pos, _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.clickRadius);\n      },\n      touchStartHandler = () => {\n        if (!guardCheck(this)) {\n          return;\n        }\n        touched = true;\n        touchMoved = false;\n      },\n      touchMoveHandler = () => {\n        if (!guardCheck(this)) {\n          return;\n        }\n        touchMoved = true;\n      },\n      touchEndHandler = e => {\n        if (!guardCheck(this)) {\n          return;\n        }\n        if (touched && !touchMoved) {\n          const touchEvent = e;\n          let lastTouch = touchEvent.touches[touchEvent.touches.length - _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.touchEndLengthOffset];\n          if (!lastTouch) {\n            lastTouch = touchEvent.changedTouches[touchEvent.changedTouches.length - _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.touchEndLengthOffset];\n            if (!lastTouch) {\n              return;\n            }\n          }\n          const element = this.canvas.element,\n            canvasRect = element ? element.getBoundingClientRect() : undefined,\n            pos = {\n              x: lastTouch.clientX - (canvasRect ? canvasRect.left : _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minCoordinate),\n              y: lastTouch.clientY - (canvasRect ? canvasRect.top : _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minCoordinate)\n            };\n          clickOrTouchHandler(e, pos, Math.max(lastTouch.radiusX, lastTouch.radiusY));\n        }\n        touched = false;\n        touchMoved = false;\n      },\n      touchCancelHandler = () => {\n        if (!guardCheck(this)) {\n          return;\n        }\n        touched = false;\n        touchMoved = false;\n      };\n    let touched = false,\n      touchMoved = false;\n    this._clickHandlers.set(\"click\", clickHandler);\n    this._clickHandlers.set(\"touchstart\", touchStartHandler);\n    this._clickHandlers.set(\"touchmove\", touchMoveHandler);\n    this._clickHandlers.set(\"touchend\", touchEndHandler);\n    this._clickHandlers.set(\"touchcancel\", touchCancelHandler);\n    for (const [key, handler] of this._clickHandlers) {\n      el.addEventListener(key, handler);\n    }\n  }\n  addLifeTime(value) {\n    this._lifeTime += value;\n  }\n  addPath(key, generator, override = false) {\n    if (!guardCheck(this) || !override && this.pathGenerators.has(key)) {\n      return false;\n    }\n    this.pathGenerators.set(key, generator);\n    return true;\n  }\n  alive() {\n    return !this._duration || this._lifeTime <= this._duration;\n  }\n  clearClickHandlers() {\n    if (!guardCheck(this)) {\n      return;\n    }\n    for (const [key, handler] of this._clickHandlers) {\n      this.interactivity.element?.removeEventListener(key, handler);\n    }\n    this._clickHandlers.clear();\n  }\n  destroy(remove = true) {\n    if (!guardCheck(this)) {\n      return;\n    }\n    this.stop();\n    this.clearClickHandlers();\n    this.particles.destroy();\n    this.canvas.destroy();\n    for (const effectDrawer of this.effectDrawers.values()) {\n      effectDrawer.destroy?.(this);\n    }\n    for (const shapeDrawer of this.shapeDrawers.values()) {\n      shapeDrawer.destroy?.(this);\n    }\n    for (const key of this.effectDrawers.keys()) {\n      this.effectDrawers.delete(key);\n    }\n    for (const key of this.shapeDrawers.keys()) {\n      this.shapeDrawers.delete(key);\n    }\n    this._engine.clearPlugins(this);\n    this.destroyed = true;\n    if (remove) {\n      const mainArr = this._engine.items,\n        idx = mainArr.findIndex(t => t === this);\n      if (idx >= _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.removeMinIndex) {\n        mainArr.splice(idx, _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.removeDeleteCount);\n      }\n    }\n    this._engine.dispatchEvent(_Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_8__.EventType.containerDestroyed, {\n      container: this\n    });\n  }\n  draw(force) {\n    if (!guardCheck(this)) {\n      return;\n    }\n    let refreshTime = force;\n    const frame = timestamp => {\n      if (refreshTime) {\n        this._lastFrameTime = undefined;\n        refreshTime = false;\n      }\n      this._nextFrame(timestamp);\n    };\n    this._drawAnimationFrame = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_9__.animate)(timestamp => frame(timestamp));\n  }\n  async export(type, options = {}) {\n    for (const plugin of this.plugins.values()) {\n      if (!plugin.export) {\n        continue;\n      }\n      const res = await plugin.export(type, options);\n      if (!res.supported) {\n        continue;\n      }\n      return res.blob;\n    }\n    (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_3__.getLogger)().error(`${_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.errorPrefix} - Export plugin with type ${type} not found`);\n  }\n  handleClickMode(mode) {\n    if (!guardCheck(this)) {\n      return;\n    }\n    this.particles.handleClickMode(mode);\n    for (const plugin of this.plugins.values()) {\n      plugin.handleClickMode?.(mode);\n    }\n  }\n  async init() {\n    if (!guardCheck(this)) {\n      return;\n    }\n    const effects = this._engine.getSupportedEffects();\n    for (const type of effects) {\n      const drawer = this._engine.getEffectDrawer(type);\n      if (drawer) {\n        this.effectDrawers.set(type, drawer);\n      }\n    }\n    const shapes = this._engine.getSupportedShapes();\n    for (const type of shapes) {\n      const drawer = this._engine.getShapeDrawer(type);\n      if (drawer) {\n        this.shapeDrawers.set(type, drawer);\n      }\n    }\n    await this.particles.initPlugins();\n    this._options = loadContainerOptions(this._engine, this, this._initialSourceOptions, this.sourceOptions);\n    this.actualOptions = loadContainerOptions(this._engine, this, this._options);\n    const availablePlugins = await this._engine.getAvailablePlugins(this);\n    for (const [id, plugin] of availablePlugins) {\n      this.plugins.set(id, plugin);\n    }\n    this.retina.init();\n    await this.canvas.init();\n    this.updateActualOptions();\n    this.canvas.initBackground();\n    this.canvas.resize();\n    const {\n      zLayers,\n      duration,\n      delay,\n      fpsLimit,\n      smooth\n    } = this.actualOptions;\n    this.zLayers = zLayers;\n    this._duration = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_9__.getRangeValue)(duration) * _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds;\n    this._delay = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_9__.getRangeValue)(delay) * _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds;\n    this._lifeTime = 0;\n    this.fpsLimit = fpsLimit > _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minFpsLimit ? fpsLimit : _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultFpsLimit;\n    this._smooth = smooth;\n    for (const drawer of this.effectDrawers.values()) {\n      await drawer.init?.(this);\n    }\n    for (const drawer of this.shapeDrawers.values()) {\n      await drawer.init?.(this);\n    }\n    for (const plugin of this.plugins.values()) {\n      await plugin.init?.();\n    }\n    this._engine.dispatchEvent(_Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_8__.EventType.containerInit, {\n      container: this\n    });\n    await this.particles.init();\n    this.particles.setDensity();\n    for (const plugin of this.plugins.values()) {\n      plugin.particlesSetup?.();\n    }\n    this._engine.dispatchEvent(_Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_8__.EventType.particlesSetup, {\n      container: this\n    });\n  }\n  async loadTheme(name) {\n    if (!guardCheck(this)) {\n      return;\n    }\n    this._currentTheme = name;\n    await this.refresh();\n  }\n  pause() {\n    if (!guardCheck(this)) {\n      return;\n    }\n    if (this._drawAnimationFrame !== undefined) {\n      (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_9__.cancelAnimation)(this._drawAnimationFrame);\n      delete this._drawAnimationFrame;\n    }\n    if (this._paused) {\n      return;\n    }\n    for (const plugin of this.plugins.values()) {\n      plugin.pause?.();\n    }\n    if (!this.pageHidden) {\n      this._paused = true;\n    }\n    this._engine.dispatchEvent(_Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_8__.EventType.containerPaused, {\n      container: this\n    });\n  }\n  play(force) {\n    if (!guardCheck(this)) {\n      return;\n    }\n    const needsUpdate = this._paused || force;\n    if (this._firstStart && !this.actualOptions.autoPlay) {\n      this._firstStart = false;\n      return;\n    }\n    if (this._paused) {\n      this._paused = false;\n    }\n    if (needsUpdate) {\n      for (const plugin of this.plugins.values()) {\n        if (plugin.play) {\n          plugin.play();\n        }\n      }\n    }\n    this._engine.dispatchEvent(_Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_8__.EventType.containerPlay, {\n      container: this\n    });\n    this.draw(needsUpdate ?? false);\n  }\n  async refresh() {\n    if (!guardCheck(this)) {\n      return;\n    }\n    this.stop();\n    return this.start();\n  }\n  async reset(sourceOptions) {\n    if (!guardCheck(this)) {\n      return;\n    }\n    this._initialSourceOptions = sourceOptions;\n    this._sourceOptions = sourceOptions;\n    this._options = loadContainerOptions(this._engine, this, this._initialSourceOptions, this.sourceOptions);\n    this.actualOptions = loadContainerOptions(this._engine, this, this._options);\n    return this.refresh();\n  }\n  async start() {\n    if (!guardCheck(this) || this.started) {\n      return;\n    }\n    await this.init();\n    this.started = true;\n    await new Promise(resolve => {\n      const start = async () => {\n        this._eventListeners.addListeners();\n        if (this.interactivity.element instanceof HTMLElement && this._intersectionObserver) {\n          this._intersectionObserver.observe(this.interactivity.element);\n        }\n        for (const plugin of this.plugins.values()) {\n          await plugin.start?.();\n        }\n        this._engine.dispatchEvent(_Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_8__.EventType.containerStarted, {\n          container: this\n        });\n        this.play();\n        resolve();\n      };\n      this._delayTimeout = setTimeout(() => void start(), this._delay);\n    });\n  }\n  stop() {\n    if (!guardCheck(this) || !this.started) {\n      return;\n    }\n    if (this._delayTimeout) {\n      clearTimeout(this._delayTimeout);\n      delete this._delayTimeout;\n    }\n    this._firstStart = true;\n    this.started = false;\n    this._eventListeners.removeListeners();\n    this.pause();\n    this.particles.clear();\n    this.canvas.stop();\n    if (this.interactivity.element instanceof HTMLElement && this._intersectionObserver) {\n      this._intersectionObserver.unobserve(this.interactivity.element);\n    }\n    for (const plugin of this.plugins.values()) {\n      plugin.stop?.();\n    }\n    for (const key of this.plugins.keys()) {\n      this.plugins.delete(key);\n    }\n    this._sourceOptions = this._options;\n    this._engine.dispatchEvent(_Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_8__.EventType.containerStopped, {\n      container: this\n    });\n  }\n  updateActualOptions() {\n    this.actualOptions.responsive = [];\n    const newMaxWidth = this.actualOptions.setResponsive(this.canvas.size.width, this.retina.pixelRatio, this._options);\n    this.actualOptions.setTheme(this._currentTheme);\n    if (this._responsiveMaxWidth === newMaxWidth) {\n      return false;\n    }\n    this._responsiveMaxWidth = newMaxWidth;\n    return true;\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Core/Container.js?");

/***/ }),

/***/ "./dist/browser/Core/Engine.js":
/*!*************************************!*\
  !*** ./dist/browser/Core/Engine.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Engine: () => (/* binding */ Engine)\n/* harmony export */ });\n/* harmony import */ var _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils/Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Container_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Container.js */ \"./dist/browser/Core/Container.js\");\n/* harmony import */ var _Utils_EventDispatcher_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Utils/EventDispatcher.js */ \"./dist/browser/Utils/EventDispatcher.js\");\n/* harmony import */ var _Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../Enums/Types/EventType.js */ \"./dist/browser/Enums/Types/EventType.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n\n\n\n\n\n\nasync function getItemsFromInitializer(container, map, initializers, force = false) {\n  let res = map.get(container);\n  if (!res || force) {\n    res = await Promise.all([...initializers.values()].map(t => t(container)));\n    map.set(container, res);\n  }\n  return res;\n}\nasync function getDataFromUrl(data) {\n  const url = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__.itemFromSingleOrMultiple)(data.url, data.index);\n  if (!url) {\n    return data.fallback;\n  }\n  const response = await fetch(url);\n  if (response.ok) {\n    return await response.json();\n  }\n  (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__.getLogger)().error(`${_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.errorPrefix} ${response.status} while retrieving config file`);\n  return data.fallback;\n}\nconst getCanvasFromContainer = domContainer => {\n    let canvasEl;\n    if (domContainer instanceof HTMLCanvasElement || domContainer.tagName.toLowerCase() === _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.canvasTag) {\n      canvasEl = domContainer;\n      if (!canvasEl.dataset[_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.generatedAttribute]) {\n        canvasEl.dataset[_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.generatedAttribute] = _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.generatedFalse;\n      }\n    } else {\n      const existingCanvases = domContainer.getElementsByTagName(_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.canvasTag);\n      if (existingCanvases.length) {\n        canvasEl = existingCanvases[_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.canvasFirstIndex];\n        canvasEl.dataset[_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.generatedAttribute] = _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.generatedFalse;\n      } else {\n        canvasEl = document.createElement(_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.canvasTag);\n        canvasEl.dataset[_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.generatedAttribute] = _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.generatedTrue;\n        domContainer.appendChild(canvasEl);\n      }\n    }\n    const fullPercent = \"100%\";\n    if (!canvasEl.style.width) {\n      canvasEl.style.width = fullPercent;\n    }\n    if (!canvasEl.style.height) {\n      canvasEl.style.height = fullPercent;\n    }\n    return canvasEl;\n  },\n  getDomContainer = (id, source) => {\n    let domContainer = source ?? document.getElementById(id);\n    if (domContainer) {\n      return domContainer;\n    }\n    domContainer = document.createElement(\"div\");\n    domContainer.id = id;\n    domContainer.dataset[_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.generatedAttribute] = _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.generatedTrue;\n    document.body.append(domContainer);\n    return domContainer;\n  };\nclass Engine {\n  constructor() {\n    this._configs = new Map();\n    this._domArray = [];\n    this._eventDispatcher = new _Utils_EventDispatcher_js__WEBPACK_IMPORTED_MODULE_2__.EventDispatcher();\n    this._initialized = false;\n    this.plugins = [];\n    this.colorManagers = new Map();\n    this.easingFunctions = new Map();\n    this._initializers = {\n      interactors: new Map(),\n      movers: new Map(),\n      updaters: new Map()\n    };\n    this.interactors = new Map();\n    this.movers = new Map();\n    this.updaters = new Map();\n    this.presets = new Map();\n    this.effectDrawers = new Map();\n    this.shapeDrawers = new Map();\n    this.pathGenerators = new Map();\n  }\n  get configs() {\n    const res = {};\n    for (const [name, config] of this._configs) {\n      res[name] = config;\n    }\n    return res;\n  }\n  get items() {\n    return this._domArray;\n  }\n  get version() {\n    return \"3.8.1\";\n  }\n  async addColorManager(manager, refresh = true) {\n    this.colorManagers.set(manager.key, manager);\n    await this.refresh(refresh);\n  }\n  addConfig(config) {\n    const key = config.key ?? config.name ?? \"default\";\n    this._configs.set(key, config);\n    this._eventDispatcher.dispatchEvent(_Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_3__.EventType.configAdded, {\n      data: {\n        name: key,\n        config\n      }\n    });\n  }\n  async addEasing(name, easing, refresh = true) {\n    if (this.getEasing(name)) {\n      return;\n    }\n    this.easingFunctions.set(name, easing);\n    await this.refresh(refresh);\n  }\n  async addEffect(effect, drawer, refresh = true) {\n    (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__.executeOnSingleOrMultiple)(effect, type => {\n      if (!this.getEffectDrawer(type)) {\n        this.effectDrawers.set(type, drawer);\n      }\n    });\n    await this.refresh(refresh);\n  }\n  addEventListener(type, listener) {\n    this._eventDispatcher.addEventListener(type, listener);\n  }\n  async addInteractor(name, interactorInitializer, refresh = true) {\n    this._initializers.interactors.set(name, interactorInitializer);\n    await this.refresh(refresh);\n  }\n  async addMover(name, moverInitializer, refresh = true) {\n    this._initializers.movers.set(name, moverInitializer);\n    await this.refresh(refresh);\n  }\n  async addParticleUpdater(name, updaterInitializer, refresh = true) {\n    this._initializers.updaters.set(name, updaterInitializer);\n    await this.refresh(refresh);\n  }\n  async addPathGenerator(name, generator, refresh = true) {\n    if (!this.getPathGenerator(name)) {\n      this.pathGenerators.set(name, generator);\n    }\n    await this.refresh(refresh);\n  }\n  async addPlugin(plugin, refresh = true) {\n    if (!this.getPlugin(plugin.id)) {\n      this.plugins.push(plugin);\n    }\n    await this.refresh(refresh);\n  }\n  async addPreset(preset, options, override = false, refresh = true) {\n    if (override || !this.getPreset(preset)) {\n      this.presets.set(preset, options);\n    }\n    await this.refresh(refresh);\n  }\n  async addShape(drawer, refresh = true) {\n    for (const validType of drawer.validTypes) {\n      if (this.getShapeDrawer(validType)) {\n        continue;\n      }\n      this.shapeDrawers.set(validType, drawer);\n    }\n    await this.refresh(refresh);\n  }\n  checkVersion(pluginVersion) {\n    if (this.version === pluginVersion) {\n      return;\n    }\n    throw new Error(`The tsParticles version is different from the loaded plugins version. Engine version: ${this.version}. Plugin version: ${pluginVersion}`);\n  }\n  clearPlugins(container) {\n    this.updaters.delete(container);\n    this.movers.delete(container);\n    this.interactors.delete(container);\n  }\n  dispatchEvent(type, args) {\n    this._eventDispatcher.dispatchEvent(type, args);\n  }\n  dom() {\n    return this.items;\n  }\n  domItem(index) {\n    return this.item(index);\n  }\n  async getAvailablePlugins(container) {\n    const res = new Map();\n    for (const plugin of this.plugins) {\n      if (plugin.needsPlugin(container.actualOptions)) {\n        res.set(plugin.id, await plugin.getPlugin(container));\n      }\n    }\n    return res;\n  }\n  getEasing(name) {\n    return this.easingFunctions.get(name) ?? (value => value);\n  }\n  getEffectDrawer(type) {\n    return this.effectDrawers.get(type);\n  }\n  async getInteractors(container, force = false) {\n    return getItemsFromInitializer(container, this.interactors, this._initializers.interactors, force);\n  }\n  async getMovers(container, force = false) {\n    return getItemsFromInitializer(container, this.movers, this._initializers.movers, force);\n  }\n  getPathGenerator(type) {\n    return this.pathGenerators.get(type);\n  }\n  getPlugin(plugin) {\n    return this.plugins.find(t => t.id === plugin);\n  }\n  getPreset(preset) {\n    return this.presets.get(preset);\n  }\n  getShapeDrawer(type) {\n    return this.shapeDrawers.get(type);\n  }\n  getSupportedEffects() {\n    return this.effectDrawers.keys();\n  }\n  getSupportedShapes() {\n    return this.shapeDrawers.keys();\n  }\n  async getUpdaters(container, force = false) {\n    return getItemsFromInitializer(container, this.updaters, this._initializers.updaters, force);\n  }\n  init() {\n    if (this._initialized) {\n      return;\n    }\n    this._initialized = true;\n  }\n  item(index) {\n    const {\n        items\n      } = this,\n      item = items[index];\n    if (!item || item.destroyed) {\n      items.splice(index, _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.removeDeleteCount);\n      return;\n    }\n    return item;\n  }\n  async load(params) {\n    const id = params.id ?? params.element?.id ?? `tsparticles${Math.floor((0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_4__.getRandom)() * _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.loadRandomFactor)}`,\n      {\n        index,\n        url\n      } = params,\n      options = url ? await getDataFromUrl({\n        fallback: params.options,\n        url,\n        index\n      }) : params.options;\n    const currentOptions = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__.itemFromSingleOrMultiple)(options, index),\n      {\n        items\n      } = this,\n      oldIndex = items.findIndex(v => v.id.description === id),\n      newItem = new _Container_js__WEBPACK_IMPORTED_MODULE_5__.Container(this, id, currentOptions);\n    if (oldIndex >= _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.loadMinIndex) {\n      const old = this.item(oldIndex),\n        deleteCount = old ? _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.one : _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.none;\n      if (old && !old.destroyed) {\n        old.destroy(false);\n      }\n      items.splice(oldIndex, deleteCount, newItem);\n    } else {\n      items.push(newItem);\n    }\n    const domContainer = getDomContainer(id, params.element),\n      canvasEl = getCanvasFromContainer(domContainer);\n    newItem.canvas.loadCanvas(canvasEl);\n    await newItem.start();\n    return newItem;\n  }\n  loadOptions(options, sourceOptions) {\n    this.plugins.forEach(plugin => plugin.loadOptions?.(options, sourceOptions));\n  }\n  loadParticlesOptions(container, options, ...sourceOptions) {\n    const updaters = this.updaters.get(container);\n    if (!updaters) {\n      return;\n    }\n    updaters.forEach(updater => updater.loadOptions?.(options, ...sourceOptions));\n  }\n  async refresh(refresh = true) {\n    if (!refresh) {\n      return;\n    }\n    await Promise.all(this.items.map(t => t.refresh()));\n  }\n  removeEventListener(type, listener) {\n    this._eventDispatcher.removeEventListener(type, listener);\n  }\n  setOnClickHandler(callback) {\n    const {\n      items\n    } = this;\n    if (!items.length) {\n      throw new Error(`${_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.errorPrefix} can only set click handlers after calling tsParticles.load()`);\n    }\n    items.forEach(item => item.addClickHandler(callback));\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Core/Engine.js?");

/***/ }),

/***/ "./dist/browser/Core/Particle.js":
/*!***************************************!*\
  !*** ./dist/browser/Core/Particle.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Particle: () => (/* binding */ Particle)\n/* harmony export */ });\n/* harmony import */ var _Utils_Vectors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Utils/Vectors.js */ \"./dist/browser/Core/Utils/Vectors.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n/* harmony import */ var _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils/Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../Utils/ColorUtils.js */ \"./dist/browser/Utils/ColorUtils.js\");\n/* harmony import */ var _Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../Enums/Types/EventType.js */ \"./dist/browser/Enums/Types/EventType.js\");\n/* harmony import */ var _Options_Classes_Interactivity_Interactivity_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../Options/Classes/Interactivity/Interactivity.js */ \"./dist/browser/Options/Classes/Interactivity/Interactivity.js\");\n/* harmony import */ var _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../Enums/Directions/MoveDirection.js */ \"./dist/browser/Enums/Directions/MoveDirection.js\");\n/* harmony import */ var _Enums_Modes_OutMode_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Enums/Modes/OutMode.js */ \"./dist/browser/Enums/Modes/OutMode.js\");\n/* harmony import */ var _Enums_Types_ParticleOutType_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../Enums/Types/ParticleOutType.js */ \"./dist/browser/Enums/Types/ParticleOutType.js\");\n/* harmony import */ var _Enums_Modes_PixelMode_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../Enums/Modes/PixelMode.js */ \"./dist/browser/Enums/Modes/PixelMode.js\");\n/* harmony import */ var _Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../Utils/CanvasUtils.js */ \"./dist/browser/Utils/CanvasUtils.js\");\n/* harmony import */ var _Utils_OptionsUtils_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../Utils/OptionsUtils.js */ \"./dist/browser/Utils/OptionsUtils.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction loadEffectData(effect, effectOptions, id, reduceDuplicates) {\n  const effectData = effectOptions.options[effect];\n  if (!effectData) {\n    return;\n  }\n  return (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__.deepExtend)({\n    close: effectOptions.close,\n    fill: effectOptions.fill\n  }, (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__.itemFromSingleOrMultiple)(effectData, id, reduceDuplicates));\n}\nfunction loadShapeData(shape, shapeOptions, id, reduceDuplicates) {\n  const shapeData = shapeOptions.options[shape];\n  if (!shapeData) {\n    return;\n  }\n  return (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__.deepExtend)({\n    close: shapeOptions.close,\n    fill: shapeOptions.fill\n  }, (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__.itemFromSingleOrMultiple)(shapeData, id, reduceDuplicates));\n}\nfunction fixOutMode(data) {\n  if (!(0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__.isInArray)(data.outMode, data.checkModes)) {\n    return;\n  }\n  const diameter = data.radius * _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.double;\n  if (data.coord > data.maxCoord - diameter) {\n    data.setCb(-data.radius);\n  } else if (data.coord < diameter) {\n    data.setCb(data.radius);\n  }\n}\nclass Particle {\n  constructor(engine, container) {\n    this.container = container;\n    this._calcPosition = (container, position, zIndex, tryCount = _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.defaultRetryCount) => {\n      for (const plugin of container.plugins.values()) {\n        const pluginPos = plugin.particlePosition !== undefined ? plugin.particlePosition(position, this) : undefined;\n        if (pluginPos) {\n          return _Utils_Vectors_js__WEBPACK_IMPORTED_MODULE_2__.Vector3d.create(pluginPos.x, pluginPos.y, zIndex);\n        }\n      }\n      const canvasSize = container.canvas.size,\n        exactPosition = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.calcExactPositionOrRandomFromSize)({\n          size: canvasSize,\n          position: position\n        }),\n        pos = _Utils_Vectors_js__WEBPACK_IMPORTED_MODULE_2__.Vector3d.create(exactPosition.x, exactPosition.y, zIndex),\n        radius = this.getRadius(),\n        outModes = this.options.move.outModes,\n        fixHorizontal = outMode => {\n          fixOutMode({\n            outMode,\n            checkModes: [_Enums_Modes_OutMode_js__WEBPACK_IMPORTED_MODULE_4__.OutMode.bounce],\n            coord: pos.x,\n            maxCoord: container.canvas.size.width,\n            setCb: value => pos.x += value,\n            radius\n          });\n        },\n        fixVertical = outMode => {\n          fixOutMode({\n            outMode,\n            checkModes: [_Enums_Modes_OutMode_js__WEBPACK_IMPORTED_MODULE_4__.OutMode.bounce],\n            coord: pos.y,\n            maxCoord: container.canvas.size.height,\n            setCb: value => pos.y += value,\n            radius\n          });\n        };\n      fixHorizontal(outModes.left ?? outModes.default);\n      fixHorizontal(outModes.right ?? outModes.default);\n      fixVertical(outModes.top ?? outModes.default);\n      fixVertical(outModes.bottom ?? outModes.default);\n      if (this._checkOverlap(pos, tryCount)) {\n        return this._calcPosition(container, undefined, zIndex, tryCount + _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.tryCountIncrement);\n      }\n      return pos;\n    };\n    this._calculateVelocity = () => {\n      const baseVelocity = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getParticleBaseVelocity)(this.direction),\n        res = baseVelocity.copy(),\n        moveOptions = this.options.move;\n      if (moveOptions.direction === _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_5__.MoveDirection.inside || moveOptions.direction === _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_5__.MoveDirection.outside) {\n        return res;\n      }\n      const rad = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.degToRad)((0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getRangeValue)(moveOptions.angle.value)),\n        radOffset = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.degToRad)((0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getRangeValue)(moveOptions.angle.offset)),\n        range = {\n          left: radOffset - rad * _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.half,\n          right: radOffset + rad * _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.half\n        };\n      if (!moveOptions.straight) {\n        res.angle += (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.randomInRange)((0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.setRangeValue)(range.left, range.right));\n      }\n      if (moveOptions.random && typeof moveOptions.speed === \"number\") {\n        res.length *= (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getRandom)();\n      }\n      return res;\n    };\n    this._checkOverlap = (pos, tryCount = _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.defaultRetryCount) => {\n      const collisionsOptions = this.options.collisions,\n        radius = this.getRadius();\n      if (!collisionsOptions.enable) {\n        return false;\n      }\n      const overlapOptions = collisionsOptions.overlap;\n      if (overlapOptions.enable) {\n        return false;\n      }\n      const retries = overlapOptions.retries;\n      if (retries >= _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.minRetries && tryCount > retries) {\n        throw new Error(`${_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.errorPrefix} particle is overlapping and can't be placed`);\n      }\n      return !!this.container.particles.find(particle => (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getDistance)(pos, particle.position) < radius + particle.getRadius());\n    };\n    this._getRollColor = color => {\n      if (!color || !this.roll || !this.backColor && !this.roll.alter) {\n        return color;\n      }\n      const backFactor = this.roll.horizontal && this.roll.vertical ? _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.double * _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.rollFactor : _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.rollFactor,\n        backSum = this.roll.horizontal ? Math.PI * _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.half : _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.none,\n        rolled = Math.floor(((this.roll.angle ?? _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.none) + backSum) / (Math.PI / backFactor)) % _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.double;\n      if (!rolled) {\n        return color;\n      }\n      if (this.backColor) {\n        return this.backColor;\n      }\n      if (this.roll.alter) {\n        return (0,_Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_6__.alterHsl)(color, this.roll.alter.type, this.roll.alter.value);\n      }\n      return color;\n    };\n    this._initPosition = position => {\n      const container = this.container,\n        zIndexValue = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getRangeValue)(this.options.zIndex.value);\n      this.position = this._calcPosition(container, position, (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.clamp)(zIndexValue, _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.minZ, container.zLayers));\n      this.initialPosition = this.position.copy();\n      const canvasSize = container.canvas.size;\n      this.moveCenter = {\n        ...(0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__.getPosition)(this.options.move.center, canvasSize),\n        radius: this.options.move.center.radius ?? _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.defaultRadius,\n        mode: this.options.move.center.mode ?? _Enums_Modes_PixelMode_js__WEBPACK_IMPORTED_MODULE_7__.PixelMode.percent\n      };\n      this.direction = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getParticleDirectionAngle)(this.options.move.direction, this.position, this.moveCenter);\n      switch (this.options.move.direction) {\n        case _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_5__.MoveDirection.inside:\n          this.outType = _Enums_Types_ParticleOutType_js__WEBPACK_IMPORTED_MODULE_8__.ParticleOutType.inside;\n          break;\n        case _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_5__.MoveDirection.outside:\n          this.outType = _Enums_Types_ParticleOutType_js__WEBPACK_IMPORTED_MODULE_8__.ParticleOutType.outside;\n          break;\n      }\n      this.offset = _Utils_Vectors_js__WEBPACK_IMPORTED_MODULE_2__.Vector.origin;\n    };\n    this._engine = engine;\n  }\n  destroy(override) {\n    if (this.unbreakable || this.destroyed) {\n      return;\n    }\n    this.destroyed = true;\n    this.bubble.inRange = false;\n    this.slow.inRange = false;\n    const container = this.container,\n      pathGenerator = this.pathGenerator,\n      shapeDrawer = container.shapeDrawers.get(this.shape);\n    shapeDrawer?.particleDestroy?.(this);\n    for (const plugin of container.plugins.values()) {\n      plugin.particleDestroyed?.(this, override);\n    }\n    for (const updater of container.particles.updaters) {\n      updater.particleDestroyed?.(this, override);\n    }\n    pathGenerator?.reset(this);\n    this._engine.dispatchEvent(_Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_9__.EventType.particleDestroyed, {\n      container: this.container,\n      data: {\n        particle: this\n      }\n    });\n  }\n  draw(delta) {\n    const container = this.container,\n      canvas = container.canvas;\n    for (const plugin of container.plugins.values()) {\n      canvas.drawParticlePlugin(plugin, this, delta);\n    }\n    canvas.drawParticle(this, delta);\n  }\n  getFillColor() {\n    return this._getRollColor(this.bubble.color ?? (0,_Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_10__.getHslFromAnimation)(this.color));\n  }\n  getMass() {\n    return this.getRadius() ** _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.squareExp * Math.PI * _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.half;\n  }\n  getPosition() {\n    return {\n      x: this.position.x + this.offset.x,\n      y: this.position.y + this.offset.y,\n      z: this.position.z\n    };\n  }\n  getRadius() {\n    return this.bubble.radius ?? this.size.value;\n  }\n  getStrokeColor() {\n    return this._getRollColor(this.bubble.color ?? (0,_Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_10__.getHslFromAnimation)(this.strokeColor));\n  }\n  init(id, position, overrideOptions, group) {\n    const container = this.container,\n      engine = this._engine;\n    this.id = id;\n    this.group = group;\n    this.effectClose = true;\n    this.effectFill = true;\n    this.shapeClose = true;\n    this.shapeFill = true;\n    this.pathRotation = false;\n    this.lastPathTime = 0;\n    this.destroyed = false;\n    this.unbreakable = false;\n    this.isRotating = false;\n    this.rotation = 0;\n    this.misplaced = false;\n    this.retina = {\n      maxDistance: {}\n    };\n    this.outType = _Enums_Types_ParticleOutType_js__WEBPACK_IMPORTED_MODULE_8__.ParticleOutType.normal;\n    this.ignoresResizeRatio = true;\n    const pxRatio = container.retina.pixelRatio,\n      mainOptions = container.actualOptions,\n      particlesOptions = (0,_Utils_OptionsUtils_js__WEBPACK_IMPORTED_MODULE_11__.loadParticlesOptions)(this._engine, container, mainOptions.particles),\n      {\n        reduceDuplicates\n      } = particlesOptions,\n      effectType = particlesOptions.effect.type,\n      shapeType = particlesOptions.shape.type;\n    this.effect = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__.itemFromSingleOrMultiple)(effectType, this.id, reduceDuplicates);\n    this.shape = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__.itemFromSingleOrMultiple)(shapeType, this.id, reduceDuplicates);\n    const effectOptions = particlesOptions.effect,\n      shapeOptions = particlesOptions.shape;\n    if (overrideOptions) {\n      if (overrideOptions.effect?.type) {\n        const overrideEffectType = overrideOptions.effect.type,\n          effect = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__.itemFromSingleOrMultiple)(overrideEffectType, this.id, reduceDuplicates);\n        if (effect) {\n          this.effect = effect;\n          effectOptions.load(overrideOptions.effect);\n        }\n      }\n      if (overrideOptions.shape?.type) {\n        const overrideShapeType = overrideOptions.shape.type,\n          shape = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__.itemFromSingleOrMultiple)(overrideShapeType, this.id, reduceDuplicates);\n        if (shape) {\n          this.shape = shape;\n          shapeOptions.load(overrideOptions.shape);\n        }\n      }\n    }\n    if (this.effect === _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.randomColorValue) {\n      const availableEffects = [...this.container.effectDrawers.keys()];\n      this.effect = availableEffects[Math.floor(Math.random() * availableEffects.length)];\n    }\n    if (this.shape === _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.randomColorValue) {\n      const availableShapes = [...this.container.shapeDrawers.keys()];\n      this.shape = availableShapes[Math.floor(Math.random() * availableShapes.length)];\n    }\n    this.effectData = loadEffectData(this.effect, effectOptions, this.id, reduceDuplicates);\n    this.shapeData = loadShapeData(this.shape, shapeOptions, this.id, reduceDuplicates);\n    particlesOptions.load(overrideOptions);\n    const effectData = this.effectData;\n    if (effectData) {\n      particlesOptions.load(effectData.particles);\n    }\n    const shapeData = this.shapeData;\n    if (shapeData) {\n      particlesOptions.load(shapeData.particles);\n    }\n    const interactivity = new _Options_Classes_Interactivity_Interactivity_js__WEBPACK_IMPORTED_MODULE_12__.Interactivity(engine, container);\n    interactivity.load(container.actualOptions.interactivity);\n    interactivity.load(particlesOptions.interactivity);\n    this.interactivity = interactivity;\n    this.effectFill = effectData?.fill ?? particlesOptions.effect.fill;\n    this.effectClose = effectData?.close ?? particlesOptions.effect.close;\n    this.shapeFill = shapeData?.fill ?? particlesOptions.shape.fill;\n    this.shapeClose = shapeData?.close ?? particlesOptions.shape.close;\n    this.options = particlesOptions;\n    const pathOptions = this.options.move.path;\n    this.pathDelay = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getRangeValue)(pathOptions.delay.value) * _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.millisecondsToSeconds;\n    if (pathOptions.generator) {\n      this.pathGenerator = this._engine.getPathGenerator(pathOptions.generator);\n      if (this.pathGenerator && container.addPath(pathOptions.generator, this.pathGenerator)) {\n        this.pathGenerator.init(container);\n      }\n    }\n    container.retina.initParticle(this);\n    this.size = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_0__.initParticleNumericAnimationValue)(this.options.size, pxRatio);\n    this.bubble = {\n      inRange: false\n    };\n    this.slow = {\n      inRange: false,\n      factor: 1\n    };\n    this._initPosition(position);\n    this.initialVelocity = this._calculateVelocity();\n    this.velocity = this.initialVelocity.copy();\n    this.moveDecay = _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.decayOffset - (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getRangeValue)(this.options.move.decay);\n    const particles = container.particles;\n    particles.setLastZIndex(this.position.z);\n    this.zIndexFactor = this.position.z / container.zLayers;\n    this.sides = 24;\n    let effectDrawer = container.effectDrawers.get(this.effect);\n    if (!effectDrawer) {\n      effectDrawer = this._engine.getEffectDrawer(this.effect);\n      if (effectDrawer) {\n        container.effectDrawers.set(this.effect, effectDrawer);\n      }\n    }\n    if (effectDrawer?.loadEffect) {\n      effectDrawer.loadEffect(this);\n    }\n    let shapeDrawer = container.shapeDrawers.get(this.shape);\n    if (!shapeDrawer) {\n      shapeDrawer = this._engine.getShapeDrawer(this.shape);\n      if (shapeDrawer) {\n        container.shapeDrawers.set(this.shape, shapeDrawer);\n      }\n    }\n    if (shapeDrawer?.loadShape) {\n      shapeDrawer.loadShape(this);\n    }\n    const sideCountFunc = shapeDrawer?.getSidesCount;\n    if (sideCountFunc) {\n      this.sides = sideCountFunc(this);\n    }\n    this.spawning = false;\n    this.shadowColor = (0,_Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_10__.rangeColorToRgb)(this._engine, this.options.shadow.color);\n    for (const updater of particles.updaters) {\n      updater.init(this);\n    }\n    for (const mover of particles.movers) {\n      mover.init?.(this);\n    }\n    effectDrawer?.particleInit?.(container, this);\n    shapeDrawer?.particleInit?.(container, this);\n    for (const plugin of container.plugins.values()) {\n      plugin.particleCreated?.(this);\n    }\n  }\n  isInsideCanvas() {\n    const radius = this.getRadius(),\n      canvasSize = this.container.canvas.size,\n      position = this.position;\n    return position.x >= -radius && position.y >= -radius && position.y <= canvasSize.height + radius && position.x <= canvasSize.width + radius;\n  }\n  isVisible() {\n    return !this.destroyed && !this.spawning && this.isInsideCanvas();\n  }\n  reset() {\n    for (const updater of this.container.particles.updaters) {\n      updater.reset?.(this);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Core/Particle.js?");

/***/ }),

/***/ "./dist/browser/Core/Particles.js":
/*!****************************************!*\
  !*** ./dist/browser/Core/Particles.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Particles: () => (/* binding */ Particles)\n/* harmony export */ });\n/* harmony import */ var _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils/Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../Enums/Types/EventType.js */ \"./dist/browser/Enums/Types/EventType.js\");\n/* harmony import */ var _Utils_InteractionManager_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Utils/InteractionManager.js */ \"./dist/browser/Core/Utils/InteractionManager.js\");\n/* harmony import */ var _Enums_Modes_LimitMode_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../Enums/Modes/LimitMode.js */ \"./dist/browser/Enums/Modes/LimitMode.js\");\n/* harmony import */ var _Particle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Particle.js */ \"./dist/browser/Core/Particle.js\");\n/* harmony import */ var _Utils_Point_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Utils/Point.js */ \"./dist/browser/Core/Utils/Point.js\");\n/* harmony import */ var _Utils_QuadTree_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Utils/QuadTree.js */ \"./dist/browser/Core/Utils/QuadTree.js\");\n/* harmony import */ var _Utils_Ranges_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Utils/Ranges.js */ \"./dist/browser/Core/Utils/Ranges.js\");\n\n\n\n\n\n\n\n\n\nconst qTreeRectangle = canvasSize => {\n  const {\n    height,\n    width\n  } = canvasSize;\n  return new _Utils_Ranges_js__WEBPACK_IMPORTED_MODULE_0__.Rectangle(_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.posOffset * width, _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.posOffset * height, _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.sizeFactor * width, _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.sizeFactor * height);\n};\nclass Particles {\n  constructor(engine, container) {\n    this._addToPool = (...particles) => {\n      this._pool.push(...particles);\n    };\n    this._applyDensity = (options, manualCount, group) => {\n      const numberOptions = options.number;\n      if (!options.number.density?.enable) {\n        if (group === undefined) {\n          this._limit = numberOptions.limit.value;\n        } else if (numberOptions.limit) {\n          this._groupLimits.set(group, numberOptions.limit.value);\n        }\n        return;\n      }\n      const densityFactor = this._initDensityFactor(numberOptions.density),\n        optParticlesNumber = numberOptions.value,\n        optParticlesLimit = numberOptions.limit.value > _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.minLimit ? numberOptions.limit.value : optParticlesNumber,\n        particlesNumber = Math.min(optParticlesNumber, optParticlesLimit) * densityFactor + manualCount,\n        particlesCount = Math.min(this.count, this.filter(t => t.group === group).length);\n      if (group === undefined) {\n        this._limit = numberOptions.limit.value * densityFactor;\n      } else {\n        this._groupLimits.set(group, numberOptions.limit.value * densityFactor);\n      }\n      if (particlesCount < particlesNumber) {\n        this.push(Math.abs(particlesNumber - particlesCount), undefined, options, group);\n      } else if (particlesCount > particlesNumber) {\n        this.removeQuantity(particlesCount - particlesNumber, group);\n      }\n    };\n    this._initDensityFactor = densityOptions => {\n      const container = this._container;\n      if (!container.canvas.element || !densityOptions.enable) {\n        return _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.defaultDensityFactor;\n      }\n      const canvas = container.canvas.element,\n        pxRatio = container.retina.pixelRatio;\n      return canvas.width * canvas.height / (densityOptions.height * densityOptions.width * pxRatio ** _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.squareExp);\n    };\n    this._pushParticle = (position, overrideOptions, group, initializer) => {\n      try {\n        let particle = this._pool.pop();\n        if (!particle) {\n          particle = new _Particle_js__WEBPACK_IMPORTED_MODULE_2__.Particle(this._engine, this._container);\n        }\n        particle.init(this._nextId, position, overrideOptions, group);\n        let canAdd = true;\n        if (initializer) {\n          canAdd = initializer(particle);\n        }\n        if (!canAdd) {\n          return;\n        }\n        this._array.push(particle);\n        this._zArray.push(particle);\n        this._nextId++;\n        this._engine.dispatchEvent(_Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_3__.EventType.particleAdded, {\n          container: this._container,\n          data: {\n            particle\n          }\n        });\n        return particle;\n      } catch (e) {\n        (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_4__.getLogger)().warning(`${_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.errorPrefix} adding particle: ${e}`);\n      }\n    };\n    this._removeParticle = (index, group, override) => {\n      const particle = this._array[index];\n      if (!particle || particle.group !== group) {\n        return false;\n      }\n      const zIdx = this._zArray.indexOf(particle);\n      this._array.splice(index, _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.deleteCount);\n      this._zArray.splice(zIdx, _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.deleteCount);\n      particle.destroy(override);\n      this._engine.dispatchEvent(_Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_3__.EventType.particleRemoved, {\n        container: this._container,\n        data: {\n          particle\n        }\n      });\n      this._addToPool(particle);\n      return true;\n    };\n    this._engine = engine;\n    this._container = container;\n    this._nextId = 0;\n    this._array = [];\n    this._zArray = [];\n    this._pool = [];\n    this._limit = 0;\n    this._groupLimits = new Map();\n    this._needsSort = false;\n    this._lastZIndex = 0;\n    this._interactionManager = new _Utils_InteractionManager_js__WEBPACK_IMPORTED_MODULE_5__.InteractionManager(engine, container);\n    this._pluginsInitialized = false;\n    const canvasSize = container.canvas.size;\n    this.quadTree = new _Utils_QuadTree_js__WEBPACK_IMPORTED_MODULE_6__.QuadTree(qTreeRectangle(canvasSize), _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.qTreeCapacity);\n    this.movers = [];\n    this.updaters = [];\n  }\n  get count() {\n    return this._array.length;\n  }\n  addManualParticles() {\n    const container = this._container,\n      options = container.actualOptions;\n    options.manualParticles.forEach(p => this.addParticle(p.position ? (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_4__.getPosition)(p.position, container.canvas.size) : undefined, p.options));\n  }\n  addParticle(position, overrideOptions, group, initializer) {\n    const limitMode = this._container.actualOptions.particles.number.limit.mode,\n      limit = group === undefined ? this._limit : this._groupLimits.get(group) ?? this._limit,\n      currentCount = this.count;\n    if (limit > _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.minLimit) {\n      switch (limitMode) {\n        case _Enums_Modes_LimitMode_js__WEBPACK_IMPORTED_MODULE_7__.LimitMode.delete:\n          {\n            const countToRemove = currentCount + _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.countOffset - limit;\n            if (countToRemove > _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.minCount) {\n              this.removeQuantity(countToRemove);\n            }\n            break;\n          }\n        case _Enums_Modes_LimitMode_js__WEBPACK_IMPORTED_MODULE_7__.LimitMode.wait:\n          if (currentCount >= limit) {\n            return;\n          }\n          break;\n      }\n    }\n    return this._pushParticle(position, overrideOptions, group, initializer);\n  }\n  clear() {\n    this._array = [];\n    this._zArray = [];\n    this._pluginsInitialized = false;\n  }\n  destroy() {\n    this._array = [];\n    this._zArray = [];\n    this.movers = [];\n    this.updaters = [];\n  }\n  draw(delta) {\n    const container = this._container,\n      canvas = container.canvas;\n    canvas.clear();\n    this.update(delta);\n    for (const plugin of container.plugins.values()) {\n      canvas.drawPlugin(plugin, delta);\n    }\n    for (const p of this._zArray) {\n      p.draw(delta);\n    }\n  }\n  filter(condition) {\n    return this._array.filter(condition);\n  }\n  find(condition) {\n    return this._array.find(condition);\n  }\n  get(index) {\n    return this._array[index];\n  }\n  handleClickMode(mode) {\n    this._interactionManager.handleClickMode(mode);\n  }\n  async init() {\n    const container = this._container,\n      options = container.actualOptions;\n    this._lastZIndex = 0;\n    this._needsSort = false;\n    await this.initPlugins();\n    let handled = false;\n    for (const plugin of container.plugins.values()) {\n      handled = plugin.particlesInitialization?.() ?? handled;\n      if (handled) {\n        break;\n      }\n    }\n    this.addManualParticles();\n    if (!handled) {\n      const particlesOptions = options.particles,\n        groups = particlesOptions.groups;\n      for (const group in groups) {\n        const groupOptions = groups[group];\n        for (let i = this.count, j = 0; j < groupOptions.number?.value && i < particlesOptions.number.value; i++, j++) {\n          this.addParticle(undefined, groupOptions, group);\n        }\n      }\n      for (let i = this.count; i < particlesOptions.number.value; i++) {\n        this.addParticle();\n      }\n    }\n  }\n  async initPlugins() {\n    if (this._pluginsInitialized) {\n      return;\n    }\n    const container = this._container;\n    this.movers = await this._engine.getMovers(container, true);\n    this.updaters = await this._engine.getUpdaters(container, true);\n    await this._interactionManager.init();\n    for (const pathGenerator of container.pathGenerators.values()) {\n      pathGenerator.init(container);\n    }\n  }\n  push(nb, mouse, overrideOptions, group) {\n    for (let i = 0; i < nb; i++) {\n      this.addParticle(mouse?.position, overrideOptions, group);\n    }\n  }\n  async redraw() {\n    this.clear();\n    await this.init();\n    this.draw({\n      value: 0,\n      factor: 0\n    });\n  }\n  remove(particle, group, override) {\n    this.removeAt(this._array.indexOf(particle), undefined, group, override);\n  }\n  removeAt(index, quantity = _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.defaultRemoveQuantity, group, override) {\n    if (index < _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.minIndex || index > this.count) {\n      return;\n    }\n    let deleted = 0;\n    for (let i = index; deleted < quantity && i < this.count; i++) {\n      if (this._removeParticle(i, group, override)) {\n        i--;\n        deleted++;\n      }\n    }\n  }\n  removeQuantity(quantity, group) {\n    this.removeAt(_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.minIndex, quantity, group);\n  }\n  setDensity() {\n    const options = this._container.actualOptions,\n      groups = options.particles.groups;\n    for (const group in groups) {\n      this._applyDensity(groups[group], _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.manualCount, group);\n    }\n    this._applyDensity(options.particles, options.manualParticles.length);\n  }\n  setLastZIndex(zIndex) {\n    this._lastZIndex = zIndex;\n    this._needsSort = this._needsSort || this._lastZIndex < zIndex;\n  }\n  setResizeFactor(factor) {\n    this._resizeFactor = factor;\n  }\n  update(delta) {\n    const container = this._container,\n      particlesToDelete = new Set();\n    this.quadTree = new _Utils_QuadTree_js__WEBPACK_IMPORTED_MODULE_6__.QuadTree(qTreeRectangle(container.canvas.size), _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.qTreeCapacity);\n    for (const pathGenerator of container.pathGenerators.values()) {\n      pathGenerator.update();\n    }\n    for (const plugin of container.plugins.values()) {\n      plugin.update?.(delta);\n    }\n    const resizeFactor = this._resizeFactor;\n    for (const particle of this._array) {\n      if (resizeFactor && !particle.ignoresResizeRatio) {\n        particle.position.x *= resizeFactor.width;\n        particle.position.y *= resizeFactor.height;\n        particle.initialPosition.x *= resizeFactor.width;\n        particle.initialPosition.y *= resizeFactor.height;\n      }\n      particle.ignoresResizeRatio = false;\n      this._interactionManager.reset(particle);\n      for (const plugin of this._container.plugins.values()) {\n        if (particle.destroyed) {\n          break;\n        }\n        plugin.particleUpdate?.(particle, delta);\n      }\n      for (const mover of this.movers) {\n        if (mover.isEnabled(particle)) {\n          mover.move(particle, delta);\n        }\n      }\n      if (particle.destroyed) {\n        particlesToDelete.add(particle);\n        continue;\n      }\n      this.quadTree.insert(new _Utils_Point_js__WEBPACK_IMPORTED_MODULE_8__.Point(particle.getPosition(), particle));\n    }\n    if (particlesToDelete.size) {\n      const checkDelete = p => !particlesToDelete.has(p);\n      this._array = this.filter(checkDelete);\n      this._zArray = this._zArray.filter(checkDelete);\n      for (const particle of particlesToDelete) {\n        this._engine.dispatchEvent(_Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_3__.EventType.particleRemoved, {\n          container: this._container,\n          data: {\n            particle\n          }\n        });\n      }\n      this._addToPool(...particlesToDelete);\n    }\n    this._interactionManager.externalInteract(delta);\n    for (const particle of this._array) {\n      for (const updater of this.updaters) {\n        updater.update(particle, delta);\n      }\n      if (!particle.destroyed && !particle.spawning) {\n        this._interactionManager.particlesInteract(particle, delta);\n      }\n    }\n    delete this._resizeFactor;\n    if (this._needsSort) {\n      const zArray = this._zArray;\n      zArray.sort((a, b) => b.position.z - a.position.z || a.id - b.id);\n      this._lastZIndex = zArray[zArray.length - _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.lengthOffset].position.z;\n      this._needsSort = false;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Core/Particles.js?");

/***/ }),

/***/ "./dist/browser/Core/Retina.js":
/*!*************************************!*\
  !*** ./dist/browser/Core/Retina.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Retina: () => (/* binding */ Retina)\n/* harmony export */ });\n/* harmony import */ var _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Utils/Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n\n\n\nclass Retina {\n  constructor(container) {\n    this.container = container;\n    this.pixelRatio = _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultRatio;\n    this.reduceFactor = _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultReduceFactor;\n  }\n  init() {\n    const container = this.container,\n      options = container.actualOptions;\n    this.pixelRatio = !options.detectRetina || (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_1__.isSsr)() ? _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultRatio : window.devicePixelRatio;\n    this.reduceFactor = _Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultReduceFactor;\n    const ratio = this.pixelRatio,\n      canvas = container.canvas;\n    if (canvas.element) {\n      const element = canvas.element;\n      canvas.size.width = element.offsetWidth * ratio;\n      canvas.size.height = element.offsetHeight * ratio;\n    }\n    const particles = options.particles,\n      moveOptions = particles.move;\n    this.maxSpeed = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeValue)(moveOptions.gravity.maxSpeed) * ratio;\n    this.sizeAnimationSpeed = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeValue)(particles.size.animation.speed) * ratio;\n  }\n  initParticle(particle) {\n    const options = particle.options,\n      ratio = this.pixelRatio,\n      moveOptions = options.move,\n      moveDistance = moveOptions.distance,\n      props = particle.retina;\n    props.moveDrift = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeValue)(moveOptions.drift) * ratio;\n    props.moveSpeed = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeValue)(moveOptions.speed) * ratio;\n    props.sizeAnimationSpeed = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeValue)(options.size.animation.speed) * ratio;\n    const maxDistance = props.maxDistance;\n    maxDistance.horizontal = moveDistance.horizontal !== undefined ? moveDistance.horizontal * ratio : undefined;\n    maxDistance.vertical = moveDistance.vertical !== undefined ? moveDistance.vertical * ratio : undefined;\n    props.maxSpeed = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeValue)(moveOptions.gravity.maxSpeed) * ratio;\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Core/Retina.js?");

/***/ }),

/***/ "./dist/browser/Core/Utils/Constants.js":
/*!**********************************************!*\
  !*** ./dist/browser/Core/Utils/Constants.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canvasFirstIndex: () => (/* binding */ canvasFirstIndex),\n/* harmony export */   canvasTag: () => (/* binding */ canvasTag),\n/* harmony export */   clickRadius: () => (/* binding */ clickRadius),\n/* harmony export */   countOffset: () => (/* binding */ countOffset),\n/* harmony export */   decayOffset: () => (/* binding */ decayOffset),\n/* harmony export */   defaultAlpha: () => (/* binding */ defaultAlpha),\n/* harmony export */   defaultAngle: () => (/* binding */ defaultAngle),\n/* harmony export */   defaultDensityFactor: () => (/* binding */ defaultDensityFactor),\n/* harmony export */   defaultFps: () => (/* binding */ defaultFps),\n/* harmony export */   defaultFpsLimit: () => (/* binding */ defaultFpsLimit),\n/* harmony export */   defaultLoops: () => (/* binding */ defaultLoops),\n/* harmony export */   defaultOpacity: () => (/* binding */ defaultOpacity),\n/* harmony export */   defaultRadius: () => (/* binding */ defaultRadius),\n/* harmony export */   defaultRatio: () => (/* binding */ defaultRatio),\n/* harmony export */   defaultReduceFactor: () => (/* binding */ defaultReduceFactor),\n/* harmony export */   defaultRemoveQuantity: () => (/* binding */ defaultRemoveQuantity),\n/* harmony export */   defaultRetryCount: () => (/* binding */ defaultRetryCount),\n/* harmony export */   defaultRgbMin: () => (/* binding */ defaultRgbMin),\n/* harmony export */   defaultTime: () => (/* binding */ defaultTime),\n/* harmony export */   defaultTransform: () => (/* binding */ defaultTransform),\n/* harmony export */   defaultTransformValue: () => (/* binding */ defaultTransformValue),\n/* harmony export */   defaultVelocity: () => (/* binding */ defaultVelocity),\n/* harmony export */   deleteCount: () => (/* binding */ deleteCount),\n/* harmony export */   double: () => (/* binding */ double),\n/* harmony export */   doublePI: () => (/* binding */ doublePI),\n/* harmony export */   empty: () => (/* binding */ empty),\n/* harmony export */   errorPrefix: () => (/* binding */ errorPrefix),\n/* harmony export */   generatedAttribute: () => (/* binding */ generatedAttribute),\n/* harmony export */   generatedFalse: () => (/* binding */ generatedFalse),\n/* harmony export */   generatedTrue: () => (/* binding */ generatedTrue),\n/* harmony export */   hMax: () => (/* binding */ hMax),\n/* harmony export */   hMin: () => (/* binding */ hMin),\n/* harmony export */   hPhase: () => (/* binding */ hPhase),\n/* harmony export */   half: () => (/* binding */ half),\n/* harmony export */   identity: () => (/* binding */ identity),\n/* harmony export */   inverseFactorNumerator: () => (/* binding */ inverseFactorNumerator),\n/* harmony export */   lFactor: () => (/* binding */ lFactor),\n/* harmony export */   lMax: () => (/* binding */ lMax),\n/* harmony export */   lMin: () => (/* binding */ lMin),\n/* harmony export */   lengthOffset: () => (/* binding */ lengthOffset),\n/* harmony export */   loadMinIndex: () => (/* binding */ loadMinIndex),\n/* harmony export */   loadRandomFactor: () => (/* binding */ loadRandomFactor),\n/* harmony export */   manualCount: () => (/* binding */ manualCount),\n/* harmony export */   manualDefaultPosition: () => (/* binding */ manualDefaultPosition),\n/* harmony export */   midColorValue: () => (/* binding */ midColorValue),\n/* harmony export */   millisecondsToSeconds: () => (/* binding */ millisecondsToSeconds),\n/* harmony export */   minCoordinate: () => (/* binding */ minCoordinate),\n/* harmony export */   minCount: () => (/* binding */ minCount),\n/* harmony export */   minFpsLimit: () => (/* binding */ minFpsLimit),\n/* harmony export */   minIndex: () => (/* binding */ minIndex),\n/* harmony export */   minLimit: () => (/* binding */ minLimit),\n/* harmony export */   minRetries: () => (/* binding */ minRetries),\n/* harmony export */   minStrokeWidth: () => (/* binding */ minStrokeWidth),\n/* harmony export */   minVelocity: () => (/* binding */ minVelocity),\n/* harmony export */   minZ: () => (/* binding */ minZ),\n/* harmony export */   minimumLength: () => (/* binding */ minimumLength),\n/* harmony export */   minimumSize: () => (/* binding */ minimumSize),\n/* harmony export */   mouseDownEvent: () => (/* binding */ mouseDownEvent),\n/* harmony export */   mouseLeaveEvent: () => (/* binding */ mouseLeaveEvent),\n/* harmony export */   mouseMoveEvent: () => (/* binding */ mouseMoveEvent),\n/* harmony export */   mouseOutEvent: () => (/* binding */ mouseOutEvent),\n/* harmony export */   mouseUpEvent: () => (/* binding */ mouseUpEvent),\n/* harmony export */   none: () => (/* binding */ none),\n/* harmony export */   one: () => (/* binding */ one),\n/* harmony export */   originPoint: () => (/* binding */ originPoint),\n/* harmony export */   percentDenominator: () => (/* binding */ percentDenominator),\n/* harmony export */   phaseNumerator: () => (/* binding */ phaseNumerator),\n/* harmony export */   posOffset: () => (/* binding */ posOffset),\n/* harmony export */   qTreeCapacity: () => (/* binding */ qTreeCapacity),\n/* harmony export */   quarter: () => (/* binding */ quarter),\n/* harmony export */   randomColorValue: () => (/* binding */ randomColorValue),\n/* harmony export */   removeDeleteCount: () => (/* binding */ removeDeleteCount),\n/* harmony export */   removeMinIndex: () => (/* binding */ removeMinIndex),\n/* harmony export */   resizeEvent: () => (/* binding */ resizeEvent),\n/* harmony export */   rgbFactor: () => (/* binding */ rgbFactor),\n/* harmony export */   rgbMax: () => (/* binding */ rgbMax),\n/* harmony export */   rollFactor: () => (/* binding */ rollFactor),\n/* harmony export */   sMax: () => (/* binding */ sMax),\n/* harmony export */   sMin: () => (/* binding */ sMin),\n/* harmony export */   sNormalizedOffset: () => (/* binding */ sNormalizedOffset),\n/* harmony export */   sextuple: () => (/* binding */ sextuple),\n/* harmony export */   sizeFactor: () => (/* binding */ sizeFactor),\n/* harmony export */   squareExp: () => (/* binding */ squareExp),\n/* harmony export */   subdivideCount: () => (/* binding */ subdivideCount),\n/* harmony export */   threeQuarter: () => (/* binding */ threeQuarter),\n/* harmony export */   touchCancelEvent: () => (/* binding */ touchCancelEvent),\n/* harmony export */   touchDelay: () => (/* binding */ touchDelay),\n/* harmony export */   touchEndEvent: () => (/* binding */ touchEndEvent),\n/* harmony export */   touchEndLengthOffset: () => (/* binding */ touchEndLengthOffset),\n/* harmony export */   touchMoveEvent: () => (/* binding */ touchMoveEvent),\n/* harmony export */   touchStartEvent: () => (/* binding */ touchStartEvent),\n/* harmony export */   triple: () => (/* binding */ triple),\n/* harmony export */   tryCountIncrement: () => (/* binding */ tryCountIncrement),\n/* harmony export */   visibilityChangeEvent: () => (/* binding */ visibilityChangeEvent),\n/* harmony export */   zIndexFactorOffset: () => (/* binding */ zIndexFactorOffset)\n/* harmony export */ });\nconst generatedAttribute = \"generated\",\n  mouseDownEvent = \"pointerdown\",\n  mouseUpEvent = \"pointerup\",\n  mouseLeaveEvent = \"pointerleave\",\n  mouseOutEvent = \"pointerout\",\n  mouseMoveEvent = \"pointermove\",\n  touchStartEvent = \"touchstart\",\n  touchEndEvent = \"touchend\",\n  touchMoveEvent = \"touchmove\",\n  touchCancelEvent = \"touchcancel\",\n  resizeEvent = \"resize\",\n  visibilityChangeEvent = \"visibilitychange\",\n  errorPrefix = \"tsParticles - Error\",\n  percentDenominator = 100,\n  half = 0.5,\n  millisecondsToSeconds = 1000,\n  originPoint = {\n    x: 0,\n    y: 0,\n    z: 0\n  },\n  defaultTransform = {\n    a: 1,\n    b: 0,\n    c: 0,\n    d: 1\n  },\n  randomColorValue = \"random\",\n  midColorValue = \"mid\",\n  double = 2,\n  doublePI = Math.PI * double,\n  defaultFps = 60,\n  defaultAlpha = 1,\n  generatedTrue = \"true\",\n  generatedFalse = \"false\",\n  canvasTag = \"canvas\",\n  defaultRetryCount = 0,\n  squareExp = 2,\n  qTreeCapacity = 4,\n  defaultRemoveQuantity = 1,\n  defaultRatio = 1,\n  defaultReduceFactor = 1,\n  subdivideCount = 4,\n  inverseFactorNumerator = 1.0,\n  rgbMax = 255,\n  hMax = 360,\n  sMax = 100,\n  lMax = 100,\n  hMin = 0,\n  sMin = 0,\n  hPhase = 60,\n  empty = 0,\n  quarter = 0.25,\n  threeQuarter = half + quarter,\n  minVelocity = 0,\n  defaultTransformValue = 1,\n  minimumSize = 0,\n  minimumLength = 0,\n  zIndexFactorOffset = 1,\n  defaultOpacity = 1,\n  clickRadius = 1,\n  touchEndLengthOffset = 1,\n  minCoordinate = 0,\n  removeDeleteCount = 1,\n  removeMinIndex = 0,\n  defaultFpsLimit = 120,\n  minFpsLimit = 0,\n  canvasFirstIndex = 0,\n  loadRandomFactor = 10000,\n  loadMinIndex = 0,\n  one = 1,\n  none = 0,\n  decayOffset = 1,\n  tryCountIncrement = 1,\n  minRetries = 0,\n  rollFactor = 1,\n  minZ = 0,\n  defaultRadius = 0,\n  posOffset = -quarter,\n  sizeFactor = 1.5,\n  minLimit = 0,\n  countOffset = 1,\n  minCount = 0,\n  minIndex = 0,\n  manualCount = 0,\n  lengthOffset = 1,\n  defaultDensityFactor = 1,\n  deleteCount = 1,\n  touchDelay = 500,\n  manualDefaultPosition = 50,\n  defaultAngle = 0,\n  identity = 1,\n  minStrokeWidth = 0,\n  lFactor = 1,\n  lMin = 0,\n  rgbFactor = 255,\n  triple = 3,\n  sextuple = 6,\n  sNormalizedOffset = 1,\n  phaseNumerator = 1,\n  defaultRgbMin = 0,\n  defaultVelocity = 0,\n  defaultLoops = 0,\n  defaultTime = 0;\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Core/Utils/Constants.js?");

/***/ }),

/***/ "./dist/browser/Core/Utils/EventListeners.js":
/*!***************************************************!*\
  !*** ./dist/browser/Core/Utils/EventListeners.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventListeners: () => (/* binding */ EventListeners)\n/* harmony export */ });\n/* harmony import */ var _Constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Enums_InteractivityDetect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../Enums/InteractivityDetect.js */ \"./dist/browser/Enums/InteractivityDetect.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\n\n\nfunction manageListener(element, event, handler, add, options) {\n  if (add) {\n    let addOptions = {\n      passive: true\n    };\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isBoolean)(options)) {\n      addOptions.capture = options;\n    } else if (options !== undefined) {\n      addOptions = options;\n    }\n    element.addEventListener(event, handler, addOptions);\n  } else {\n    const removeOptions = options;\n    element.removeEventListener(event, handler, removeOptions);\n  }\n}\nclass EventListeners {\n  constructor(container) {\n    this.container = container;\n    this._doMouseTouchClick = e => {\n      const container = this.container,\n        options = container.actualOptions;\n      if (this._canPush) {\n        const mouseInteractivity = container.interactivity.mouse,\n          mousePos = mouseInteractivity.position;\n        if (!mousePos) {\n          return;\n        }\n        mouseInteractivity.clickPosition = {\n          ...mousePos\n        };\n        mouseInteractivity.clickTime = new Date().getTime();\n        const onClick = options.interactivity.events.onClick;\n        (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_1__.executeOnSingleOrMultiple)(onClick.mode, mode => this.container.handleClickMode(mode));\n      }\n      if (e.type === \"touchend\") {\n        setTimeout(() => this._mouseTouchFinish(), _Constants_js__WEBPACK_IMPORTED_MODULE_2__.touchDelay);\n      }\n    };\n    this._handleThemeChange = e => {\n      const mediaEvent = e,\n        container = this.container,\n        options = container.options,\n        defaultThemes = options.defaultThemes,\n        themeName = mediaEvent.matches ? defaultThemes.dark : defaultThemes.light,\n        theme = options.themes.find(theme => theme.name === themeName);\n      if (theme?.default.auto) {\n        void container.loadTheme(themeName);\n      }\n    };\n    this._handleVisibilityChange = () => {\n      const container = this.container,\n        options = container.actualOptions;\n      this._mouseTouchFinish();\n      if (!options.pauseOnBlur) {\n        return;\n      }\n      if (document?.hidden) {\n        container.pageHidden = true;\n        container.pause();\n      } else {\n        container.pageHidden = false;\n        if (container.animationStatus) {\n          void container.play(true);\n        } else {\n          void container.draw(true);\n        }\n      }\n    };\n    this._handleWindowResize = () => {\n      if (this._resizeTimeout) {\n        clearTimeout(this._resizeTimeout);\n        delete this._resizeTimeout;\n      }\n      const handleResize = async () => {\n        const canvas = this.container.canvas;\n        await canvas?.windowResize();\n      };\n      this._resizeTimeout = setTimeout(() => void handleResize(), this.container.actualOptions.interactivity.events.resize.delay * _Constants_js__WEBPACK_IMPORTED_MODULE_2__.millisecondsToSeconds);\n    };\n    this._manageInteractivityListeners = (mouseLeaveTmpEvent, add) => {\n      const handlers = this._handlers,\n        container = this.container,\n        options = container.actualOptions,\n        interactivityEl = container.interactivity.element;\n      if (!interactivityEl) {\n        return;\n      }\n      const html = interactivityEl,\n        canvasEl = container.canvas.element;\n      if (canvasEl) {\n        canvasEl.style.pointerEvents = html === canvasEl ? \"initial\" : \"none\";\n      }\n      if (!(options.interactivity.events.onHover.enable || options.interactivity.events.onClick.enable)) {\n        return;\n      }\n      manageListener(interactivityEl, _Constants_js__WEBPACK_IMPORTED_MODULE_2__.mouseMoveEvent, handlers.mouseMove, add);\n      manageListener(interactivityEl, _Constants_js__WEBPACK_IMPORTED_MODULE_2__.touchStartEvent, handlers.touchStart, add);\n      manageListener(interactivityEl, _Constants_js__WEBPACK_IMPORTED_MODULE_2__.touchMoveEvent, handlers.touchMove, add);\n      if (!options.interactivity.events.onClick.enable) {\n        manageListener(interactivityEl, _Constants_js__WEBPACK_IMPORTED_MODULE_2__.touchEndEvent, handlers.touchEnd, add);\n      } else {\n        manageListener(interactivityEl, _Constants_js__WEBPACK_IMPORTED_MODULE_2__.touchEndEvent, handlers.touchEndClick, add);\n        manageListener(interactivityEl, _Constants_js__WEBPACK_IMPORTED_MODULE_2__.mouseUpEvent, handlers.mouseUp, add);\n        manageListener(interactivityEl, _Constants_js__WEBPACK_IMPORTED_MODULE_2__.mouseDownEvent, handlers.mouseDown, add);\n      }\n      manageListener(interactivityEl, mouseLeaveTmpEvent, handlers.mouseLeave, add);\n      manageListener(interactivityEl, _Constants_js__WEBPACK_IMPORTED_MODULE_2__.touchCancelEvent, handlers.touchCancel, add);\n    };\n    this._manageListeners = add => {\n      const handlers = this._handlers,\n        container = this.container,\n        options = container.actualOptions,\n        detectType = options.interactivity.detectsOn,\n        canvasEl = container.canvas.element;\n      let mouseLeaveTmpEvent = _Constants_js__WEBPACK_IMPORTED_MODULE_2__.mouseLeaveEvent;\n      if (detectType === _Enums_InteractivityDetect_js__WEBPACK_IMPORTED_MODULE_3__.InteractivityDetect.window) {\n        container.interactivity.element = window;\n        mouseLeaveTmpEvent = _Constants_js__WEBPACK_IMPORTED_MODULE_2__.mouseOutEvent;\n      } else if (detectType === _Enums_InteractivityDetect_js__WEBPACK_IMPORTED_MODULE_3__.InteractivityDetect.parent && canvasEl) {\n        container.interactivity.element = canvasEl.parentElement ?? canvasEl.parentNode;\n      } else {\n        container.interactivity.element = canvasEl;\n      }\n      this._manageMediaMatch(add);\n      this._manageResize(add);\n      this._manageInteractivityListeners(mouseLeaveTmpEvent, add);\n      if (document) {\n        manageListener(document, _Constants_js__WEBPACK_IMPORTED_MODULE_2__.visibilityChangeEvent, handlers.visibilityChange, add, false);\n      }\n    };\n    this._manageMediaMatch = add => {\n      const handlers = this._handlers,\n        mediaMatch = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_1__.safeMatchMedia)(\"(prefers-color-scheme: dark)\");\n      if (!mediaMatch) {\n        return;\n      }\n      if (mediaMatch.addEventListener !== undefined) {\n        manageListener(mediaMatch, \"change\", handlers.themeChange, add);\n        return;\n      }\n      if (mediaMatch.addListener === undefined) {\n        return;\n      }\n      if (add) {\n        mediaMatch.addListener(handlers.oldThemeChange);\n      } else {\n        mediaMatch.removeListener(handlers.oldThemeChange);\n      }\n    };\n    this._manageResize = add => {\n      const handlers = this._handlers,\n        container = this.container,\n        options = container.actualOptions;\n      if (!options.interactivity.events.resize) {\n        return;\n      }\n      if (typeof ResizeObserver === \"undefined\") {\n        manageListener(window, _Constants_js__WEBPACK_IMPORTED_MODULE_2__.resizeEvent, handlers.resize, add);\n        return;\n      }\n      const canvasEl = container.canvas.element;\n      if (this._resizeObserver && !add) {\n        if (canvasEl) {\n          this._resizeObserver.unobserve(canvasEl);\n        }\n        this._resizeObserver.disconnect();\n        delete this._resizeObserver;\n      } else if (!this._resizeObserver && add && canvasEl) {\n        this._resizeObserver = new ResizeObserver(entries => {\n          const entry = entries.find(e => e.target === canvasEl);\n          if (!entry) {\n            return;\n          }\n          this._handleWindowResize();\n        });\n        this._resizeObserver.observe(canvasEl);\n      }\n    };\n    this._mouseDown = () => {\n      const {\n        interactivity\n      } = this.container;\n      if (!interactivity) {\n        return;\n      }\n      const {\n        mouse\n      } = interactivity;\n      mouse.clicking = true;\n      mouse.downPosition = mouse.position;\n    };\n    this._mouseTouchClick = e => {\n      const container = this.container,\n        options = container.actualOptions,\n        {\n          mouse\n        } = container.interactivity;\n      mouse.inside = true;\n      let handled = false;\n      const mousePosition = mouse.position;\n      if (!mousePosition || !options.interactivity.events.onClick.enable) {\n        return;\n      }\n      for (const plugin of container.plugins.values()) {\n        if (!plugin.clickPositionValid) {\n          continue;\n        }\n        handled = plugin.clickPositionValid(mousePosition);\n        if (handled) {\n          break;\n        }\n      }\n      if (!handled) {\n        this._doMouseTouchClick(e);\n      }\n      mouse.clicking = false;\n    };\n    this._mouseTouchFinish = () => {\n      const interactivity = this.container.interactivity;\n      if (!interactivity) {\n        return;\n      }\n      const mouse = interactivity.mouse;\n      delete mouse.position;\n      delete mouse.clickPosition;\n      delete mouse.downPosition;\n      interactivity.status = _Constants_js__WEBPACK_IMPORTED_MODULE_2__.mouseLeaveEvent;\n      mouse.inside = false;\n      mouse.clicking = false;\n    };\n    this._mouseTouchMove = e => {\n      const container = this.container,\n        options = container.actualOptions,\n        interactivity = container.interactivity,\n        canvasEl = container.canvas.element;\n      if (!interactivity?.element) {\n        return;\n      }\n      interactivity.mouse.inside = true;\n      let pos;\n      if (e.type.startsWith(\"pointer\")) {\n        this._canPush = true;\n        const mouseEvent = e;\n        if (interactivity.element === window) {\n          if (canvasEl) {\n            const clientRect = canvasEl.getBoundingClientRect();\n            pos = {\n              x: mouseEvent.clientX - clientRect.left,\n              y: mouseEvent.clientY - clientRect.top\n            };\n          }\n        } else if (options.interactivity.detectsOn === _Enums_InteractivityDetect_js__WEBPACK_IMPORTED_MODULE_3__.InteractivityDetect.parent) {\n          const source = mouseEvent.target,\n            target = mouseEvent.currentTarget;\n          if (source && target && canvasEl) {\n            const sourceRect = source.getBoundingClientRect(),\n              targetRect = target.getBoundingClientRect(),\n              canvasRect = canvasEl.getBoundingClientRect();\n            pos = {\n              x: mouseEvent.offsetX + _Constants_js__WEBPACK_IMPORTED_MODULE_2__.double * sourceRect.left - (targetRect.left + canvasRect.left),\n              y: mouseEvent.offsetY + _Constants_js__WEBPACK_IMPORTED_MODULE_2__.double * sourceRect.top - (targetRect.top + canvasRect.top)\n            };\n          } else {\n            pos = {\n              x: mouseEvent.offsetX ?? mouseEvent.clientX,\n              y: mouseEvent.offsetY ?? mouseEvent.clientY\n            };\n          }\n        } else if (mouseEvent.target === canvasEl) {\n          pos = {\n            x: mouseEvent.offsetX ?? mouseEvent.clientX,\n            y: mouseEvent.offsetY ?? mouseEvent.clientY\n          };\n        }\n      } else {\n        this._canPush = e.type !== \"touchmove\";\n        if (canvasEl) {\n          const touchEvent = e,\n            lastTouch = touchEvent.touches[touchEvent.touches.length - _Constants_js__WEBPACK_IMPORTED_MODULE_2__.lengthOffset],\n            canvasRect = canvasEl.getBoundingClientRect();\n          pos = {\n            x: lastTouch.clientX - (canvasRect.left ?? _Constants_js__WEBPACK_IMPORTED_MODULE_2__.minCoordinate),\n            y: lastTouch.clientY - (canvasRect.top ?? _Constants_js__WEBPACK_IMPORTED_MODULE_2__.minCoordinate)\n          };\n        }\n      }\n      const pxRatio = container.retina.pixelRatio;\n      if (pos) {\n        pos.x *= pxRatio;\n        pos.y *= pxRatio;\n      }\n      interactivity.mouse.position = pos;\n      interactivity.status = _Constants_js__WEBPACK_IMPORTED_MODULE_2__.mouseMoveEvent;\n    };\n    this._touchEnd = e => {\n      const evt = e,\n        touches = Array.from(evt.changedTouches);\n      for (const touch of touches) {\n        this._touches.delete(touch.identifier);\n      }\n      this._mouseTouchFinish();\n    };\n    this._touchEndClick = e => {\n      const evt = e,\n        touches = Array.from(evt.changedTouches);\n      for (const touch of touches) {\n        this._touches.delete(touch.identifier);\n      }\n      this._mouseTouchClick(e);\n    };\n    this._touchStart = e => {\n      const evt = e,\n        touches = Array.from(evt.changedTouches);\n      for (const touch of touches) {\n        this._touches.set(touch.identifier, performance.now());\n      }\n      this._mouseTouchMove(e);\n    };\n    this._canPush = true;\n    this._touches = new Map();\n    this._handlers = {\n      mouseDown: () => this._mouseDown(),\n      mouseLeave: () => this._mouseTouchFinish(),\n      mouseMove: e => this._mouseTouchMove(e),\n      mouseUp: e => this._mouseTouchClick(e),\n      touchStart: e => this._touchStart(e),\n      touchMove: e => this._mouseTouchMove(e),\n      touchEnd: e => this._touchEnd(e),\n      touchCancel: e => this._touchEnd(e),\n      touchEndClick: e => this._touchEndClick(e),\n      visibilityChange: () => this._handleVisibilityChange(),\n      themeChange: e => this._handleThemeChange(e),\n      oldThemeChange: e => this._handleThemeChange(e),\n      resize: () => {\n        this._handleWindowResize();\n      }\n    };\n  }\n  addListeners() {\n    this._manageListeners(true);\n  }\n  removeListeners() {\n    this._manageListeners(false);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Core/Utils/EventListeners.js?");

/***/ }),

/***/ "./dist/browser/Core/Utils/ExternalInteractorBase.js":
/*!***********************************************************!*\
  !*** ./dist/browser/Core/Utils/ExternalInteractorBase.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExternalInteractorBase: () => (/* binding */ ExternalInteractorBase)\n/* harmony export */ });\n/* harmony import */ var _Enums_Types_InteractorType_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../Enums/Types/InteractorType.js */ \"./dist/browser/Enums/Types/InteractorType.js\");\n\nclass ExternalInteractorBase {\n  constructor(container) {\n    this.type = _Enums_Types_InteractorType_js__WEBPACK_IMPORTED_MODULE_0__.InteractorType.external;\n    this.container = container;\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Core/Utils/ExternalInteractorBase.js?");

/***/ }),

/***/ "./dist/browser/Core/Utils/InteractionManager.js":
/*!*******************************************************!*\
  !*** ./dist/browser/Core/Utils/InteractionManager.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InteractionManager: () => (/* binding */ InteractionManager)\n/* harmony export */ });\n/* harmony import */ var _Enums_Types_InteractorType_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../Enums/Types/InteractorType.js */ \"./dist/browser/Enums/Types/InteractorType.js\");\n\nclass InteractionManager {\n  constructor(engine, container) {\n    this.container = container;\n    this._engine = engine;\n    this._interactors = [];\n    this._externalInteractors = [];\n    this._particleInteractors = [];\n  }\n  externalInteract(delta) {\n    for (const interactor of this._externalInteractors) {\n      if (interactor.isEnabled()) {\n        interactor.interact(delta);\n      }\n    }\n  }\n  handleClickMode(mode) {\n    for (const interactor of this._externalInteractors) {\n      interactor.handleClickMode?.(mode);\n    }\n  }\n  async init() {\n    this._interactors = await this._engine.getInteractors(this.container, true);\n    this._externalInteractors = [];\n    this._particleInteractors = [];\n    for (const interactor of this._interactors) {\n      switch (interactor.type) {\n        case _Enums_Types_InteractorType_js__WEBPACK_IMPORTED_MODULE_0__.InteractorType.external:\n          this._externalInteractors.push(interactor);\n          break;\n        case _Enums_Types_InteractorType_js__WEBPACK_IMPORTED_MODULE_0__.InteractorType.particles:\n          this._particleInteractors.push(interactor);\n          break;\n      }\n      interactor.init();\n    }\n  }\n  particlesInteract(particle, delta) {\n    for (const interactor of this._externalInteractors) {\n      interactor.clear(particle, delta);\n    }\n    for (const interactor of this._particleInteractors) {\n      if (interactor.isEnabled(particle)) {\n        interactor.interact(particle, delta);\n      }\n    }\n  }\n  reset(particle) {\n    for (const interactor of this._externalInteractors) {\n      if (interactor.isEnabled()) {\n        interactor.reset(particle);\n      }\n    }\n    for (const interactor of this._particleInteractors) {\n      if (interactor.isEnabled(particle)) {\n        interactor.reset(particle);\n      }\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Core/Utils/InteractionManager.js?");

/***/ }),

/***/ "./dist/browser/Core/Utils/ParticlesInteractorBase.js":
/*!************************************************************!*\
  !*** ./dist/browser/Core/Utils/ParticlesInteractorBase.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParticlesInteractorBase: () => (/* binding */ ParticlesInteractorBase)\n/* harmony export */ });\n/* harmony import */ var _Enums_Types_InteractorType_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../Enums/Types/InteractorType.js */ \"./dist/browser/Enums/Types/InteractorType.js\");\n\nclass ParticlesInteractorBase {\n  constructor(container) {\n    this.type = _Enums_Types_InteractorType_js__WEBPACK_IMPORTED_MODULE_0__.InteractorType.particles;\n    this.container = container;\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Core/Utils/ParticlesInteractorBase.js?");

/***/ }),

/***/ "./dist/browser/Core/Utils/Point.js":
/*!******************************************!*\
  !*** ./dist/browser/Core/Utils/Point.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Point: () => (/* binding */ Point)\n/* harmony export */ });\nclass Point {\n  constructor(position, particle) {\n    this.position = position;\n    this.particle = particle;\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Core/Utils/Point.js?");

/***/ }),

/***/ "./dist/browser/Core/Utils/QuadTree.js":
/*!*********************************************!*\
  !*** ./dist/browser/Core/Utils/QuadTree.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuadTree: () => (/* binding */ QuadTree)\n/* harmony export */ });\n/* harmony import */ var _Ranges_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Ranges.js */ \"./dist/browser/Core/Utils/Ranges.js\");\n/* harmony import */ var _Constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n\n\n\nclass QuadTree {\n  constructor(rectangle, capacity) {\n    this.rectangle = rectangle;\n    this.capacity = capacity;\n    this._subdivide = () => {\n      const {\n          x,\n          y\n        } = this.rectangle.position,\n        {\n          width,\n          height\n        } = this.rectangle.size,\n        {\n          capacity\n        } = this;\n      for (let i = 0; i < _Constants_js__WEBPACK_IMPORTED_MODULE_0__.subdivideCount; i++) {\n        const fixedIndex = i % _Constants_js__WEBPACK_IMPORTED_MODULE_0__.double;\n        this._subs.push(new QuadTree(new _Ranges_js__WEBPACK_IMPORTED_MODULE_1__.Rectangle(x + width * _Constants_js__WEBPACK_IMPORTED_MODULE_0__.half * fixedIndex, y + height * _Constants_js__WEBPACK_IMPORTED_MODULE_0__.half * (Math.round(i * _Constants_js__WEBPACK_IMPORTED_MODULE_0__.half) - fixedIndex), width * _Constants_js__WEBPACK_IMPORTED_MODULE_0__.half, height * _Constants_js__WEBPACK_IMPORTED_MODULE_0__.half), capacity));\n      }\n      this._divided = true;\n    };\n    this._points = [];\n    this._divided = false;\n    this._subs = [];\n  }\n  insert(point) {\n    if (!this.rectangle.contains(point.position)) {\n      return false;\n    }\n    if (this._points.length < this.capacity) {\n      this._points.push(point);\n      return true;\n    }\n    if (!this._divided) {\n      this._subdivide();\n    }\n    return this._subs.some(sub => sub.insert(point));\n  }\n  query(range, check) {\n    const res = [];\n    if (!range.intersects(this.rectangle)) {\n      return [];\n    }\n    for (const p of this._points) {\n      if (!range.contains(p.position) && (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getDistance)(range.position, p.position) > p.particle.getRadius() && (!check || check(p.particle))) {\n        continue;\n      }\n      res.push(p.particle);\n    }\n    if (this._divided) {\n      for (const sub of this._subs) {\n        res.push(...sub.query(range, check));\n      }\n    }\n    return res;\n  }\n  queryCircle(position, radius, check) {\n    return this.query(new _Ranges_js__WEBPACK_IMPORTED_MODULE_1__.Circle(position.x, position.y, radius), check);\n  }\n  queryRectangle(position, size, check) {\n    return this.query(new _Ranges_js__WEBPACK_IMPORTED_MODULE_1__.Rectangle(position.x, position.y, size.width, size.height), check);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Core/Utils/QuadTree.js?");

/***/ }),

/***/ "./dist/browser/Core/Utils/Ranges.js":
/*!*******************************************!*\
  !*** ./dist/browser/Core/Utils/Ranges.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseRange: () => (/* binding */ BaseRange),\n/* harmony export */   Circle: () => (/* binding */ Circle),\n/* harmony export */   Rectangle: () => (/* binding */ Rectangle)\n/* harmony export */ });\n/* harmony import */ var _Types_RangeType_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../Types/RangeType.js */ \"./dist/browser/Types/RangeType.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n/* harmony import */ var _Constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n\n\n\nclass BaseRange {\n  constructor(x, y, type) {\n    this.position = {\n      x: x,\n      y: y\n    };\n    this.type = type;\n  }\n}\nclass Circle extends BaseRange {\n  constructor(x, y, radius) {\n    super(x, y, _Types_RangeType_js__WEBPACK_IMPORTED_MODULE_0__.RangeType.circle);\n    this.radius = radius;\n  }\n  contains(point) {\n    return (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__.getDistance)(point, this.position) <= this.radius;\n  }\n  intersects(range) {\n    const pos1 = this.position,\n      pos2 = range.position,\n      distPos = {\n        x: Math.abs(pos2.x - pos1.x),\n        y: Math.abs(pos2.y - pos1.y)\n      },\n      r = this.radius;\n    if (range instanceof Circle || range.type === _Types_RangeType_js__WEBPACK_IMPORTED_MODULE_0__.RangeType.circle) {\n      const circleRange = range,\n        rSum = r + circleRange.radius,\n        dist = Math.sqrt(distPos.x ** _Constants_js__WEBPACK_IMPORTED_MODULE_2__.squareExp + distPos.y ** _Constants_js__WEBPACK_IMPORTED_MODULE_2__.squareExp);\n      return rSum > dist;\n    } else if (range instanceof Rectangle || range.type === _Types_RangeType_js__WEBPACK_IMPORTED_MODULE_0__.RangeType.rectangle) {\n      const rectRange = range,\n        {\n          width,\n          height\n        } = rectRange.size,\n        edges = Math.pow(distPos.x - width, _Constants_js__WEBPACK_IMPORTED_MODULE_2__.squareExp) + Math.pow(distPos.y - height, _Constants_js__WEBPACK_IMPORTED_MODULE_2__.squareExp);\n      return edges <= r ** _Constants_js__WEBPACK_IMPORTED_MODULE_2__.squareExp || distPos.x <= r + width && distPos.y <= r + height || distPos.x <= width || distPos.y <= height;\n    }\n    return false;\n  }\n}\nclass Rectangle extends BaseRange {\n  constructor(x, y, width, height) {\n    super(x, y, _Types_RangeType_js__WEBPACK_IMPORTED_MODULE_0__.RangeType.rectangle);\n    this.size = {\n      height: height,\n      width: width\n    };\n  }\n  contains(point) {\n    const w = this.size.width,\n      h = this.size.height,\n      pos = this.position;\n    return point.x >= pos.x && point.x <= pos.x + w && point.y >= pos.y && point.y <= pos.y + h;\n  }\n  intersects(range) {\n    if (range instanceof Circle) {\n      return range.intersects(this);\n    }\n    const w = this.size.width,\n      h = this.size.height,\n      pos1 = this.position,\n      pos2 = range.position,\n      size2 = range instanceof Rectangle ? range.size : {\n        width: 0,\n        height: 0\n      },\n      w2 = size2.width,\n      h2 = size2.height;\n    return pos2.x < pos1.x + w && pos2.x + w2 > pos1.x && pos2.y < pos1.y + h && pos2.y + h2 > pos1.y;\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Core/Utils/Ranges.js?");

/***/ }),

/***/ "./dist/browser/Core/Utils/Vectors.js":
/*!********************************************!*\
  !*** ./dist/browser/Core/Utils/Vectors.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Vector: () => (/* binding */ Vector),\n/* harmony export */   Vector3d: () => (/* binding */ Vector3d)\n/* harmony export */ });\n/* harmony import */ var _Constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass Vector3d {\n  constructor(xOrCoords, y, z) {\n    this._updateFromAngle = (angle, length) => {\n      this.x = Math.cos(angle) * length;\n      this.y = Math.sin(angle) * length;\n    };\n    if (!(0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNumber)(xOrCoords) && xOrCoords) {\n      this.x = xOrCoords.x;\n      this.y = xOrCoords.y;\n      const coords3d = xOrCoords;\n      this.z = coords3d.z ? coords3d.z : _Constants_js__WEBPACK_IMPORTED_MODULE_1__.originPoint.z;\n    } else if (xOrCoords !== undefined && y !== undefined) {\n      this.x = xOrCoords;\n      this.y = y;\n      this.z = z ?? _Constants_js__WEBPACK_IMPORTED_MODULE_1__.originPoint.z;\n    } else {\n      throw new Error(`${_Constants_js__WEBPACK_IMPORTED_MODULE_1__.errorPrefix} Vector3d not initialized correctly`);\n    }\n  }\n  static get origin() {\n    return Vector3d.create(_Constants_js__WEBPACK_IMPORTED_MODULE_1__.originPoint.x, _Constants_js__WEBPACK_IMPORTED_MODULE_1__.originPoint.y, _Constants_js__WEBPACK_IMPORTED_MODULE_1__.originPoint.z);\n  }\n  get angle() {\n    return Math.atan2(this.y, this.x);\n  }\n  set angle(angle) {\n    this._updateFromAngle(angle, this.length);\n  }\n  get length() {\n    return Math.sqrt(this.getLengthSq());\n  }\n  set length(length) {\n    this._updateFromAngle(this.angle, length);\n  }\n  static clone(source) {\n    return Vector3d.create(source.x, source.y, source.z);\n  }\n  static create(x, y, z) {\n    return new Vector3d(x, y, z);\n  }\n  add(v) {\n    return Vector3d.create(this.x + v.x, this.y + v.y, this.z + v.z);\n  }\n  addTo(v) {\n    this.x += v.x;\n    this.y += v.y;\n    this.z += v.z;\n  }\n  copy() {\n    return Vector3d.clone(this);\n  }\n  distanceTo(v) {\n    return this.sub(v).length;\n  }\n  distanceToSq(v) {\n    return this.sub(v).getLengthSq();\n  }\n  div(n) {\n    return Vector3d.create(this.x / n, this.y / n, this.z / n);\n  }\n  divTo(n) {\n    this.x /= n;\n    this.y /= n;\n    this.z /= n;\n  }\n  getLengthSq() {\n    return this.x ** _Constants_js__WEBPACK_IMPORTED_MODULE_1__.squareExp + this.y ** _Constants_js__WEBPACK_IMPORTED_MODULE_1__.squareExp;\n  }\n  mult(n) {\n    return Vector3d.create(this.x * n, this.y * n, this.z * n);\n  }\n  multTo(n) {\n    this.x *= n;\n    this.y *= n;\n    this.z *= n;\n  }\n  normalize() {\n    const length = this.length;\n    if (length != _Constants_js__WEBPACK_IMPORTED_MODULE_1__.none) {\n      this.multTo(_Constants_js__WEBPACK_IMPORTED_MODULE_1__.inverseFactorNumerator / length);\n    }\n  }\n  rotate(angle) {\n    return Vector3d.create(this.x * Math.cos(angle) - this.y * Math.sin(angle), this.x * Math.sin(angle) + this.y * Math.cos(angle), _Constants_js__WEBPACK_IMPORTED_MODULE_1__.originPoint.z);\n  }\n  setTo(c) {\n    this.x = c.x;\n    this.y = c.y;\n    const v3d = c;\n    this.z = v3d.z ? v3d.z : _Constants_js__WEBPACK_IMPORTED_MODULE_1__.originPoint.z;\n  }\n  sub(v) {\n    return Vector3d.create(this.x - v.x, this.y - v.y, this.z - v.z);\n  }\n  subFrom(v) {\n    this.x -= v.x;\n    this.y -= v.y;\n    this.z -= v.z;\n  }\n}\nclass Vector extends Vector3d {\n  constructor(xOrCoords, y) {\n    super(xOrCoords, y, _Constants_js__WEBPACK_IMPORTED_MODULE_1__.originPoint.z);\n  }\n  static get origin() {\n    return Vector.create(_Constants_js__WEBPACK_IMPORTED_MODULE_1__.originPoint.x, _Constants_js__WEBPACK_IMPORTED_MODULE_1__.originPoint.y);\n  }\n  static clone(source) {\n    return Vector.create(source.x, source.y);\n  }\n  static create(x, y) {\n    return new Vector(x, y);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Core/Utils/Vectors.js?");

/***/ }),

/***/ "./dist/browser/Enums/AnimationStatus.js":
/*!***********************************************!*\
  !*** ./dist/browser/Enums/AnimationStatus.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimationStatus: () => (/* binding */ AnimationStatus)\n/* harmony export */ });\nvar AnimationStatus;\n(function (AnimationStatus) {\n  AnimationStatus[\"increasing\"] = \"increasing\";\n  AnimationStatus[\"decreasing\"] = \"decreasing\";\n})(AnimationStatus || (AnimationStatus = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/AnimationStatus.js?");

/***/ }),

/***/ "./dist/browser/Enums/Directions/MoveDirection.js":
/*!********************************************************!*\
  !*** ./dist/browser/Enums/Directions/MoveDirection.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MoveDirection: () => (/* binding */ MoveDirection)\n/* harmony export */ });\nvar MoveDirection;\n(function (MoveDirection) {\n  MoveDirection[\"bottom\"] = \"bottom\";\n  MoveDirection[\"bottomLeft\"] = \"bottom-left\";\n  MoveDirection[\"bottomRight\"] = \"bottom-right\";\n  MoveDirection[\"left\"] = \"left\";\n  MoveDirection[\"none\"] = \"none\";\n  MoveDirection[\"right\"] = \"right\";\n  MoveDirection[\"top\"] = \"top\";\n  MoveDirection[\"topLeft\"] = \"top-left\";\n  MoveDirection[\"topRight\"] = \"top-right\";\n  MoveDirection[\"outside\"] = \"outside\";\n  MoveDirection[\"inside\"] = \"inside\";\n})(MoveDirection || (MoveDirection = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Directions/MoveDirection.js?");

/***/ }),

/***/ "./dist/browser/Enums/Directions/OutModeDirection.js":
/*!***********************************************************!*\
  !*** ./dist/browser/Enums/Directions/OutModeDirection.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OutModeDirection: () => (/* binding */ OutModeDirection)\n/* harmony export */ });\nvar OutModeDirection;\n(function (OutModeDirection) {\n  OutModeDirection[\"bottom\"] = \"bottom\";\n  OutModeDirection[\"left\"] = \"left\";\n  OutModeDirection[\"right\"] = \"right\";\n  OutModeDirection[\"top\"] = \"top\";\n})(OutModeDirection || (OutModeDirection = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Directions/OutModeDirection.js?");

/***/ }),

/***/ "./dist/browser/Enums/Directions/RotateDirection.js":
/*!**********************************************************!*\
  !*** ./dist/browser/Enums/Directions/RotateDirection.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RotateDirection: () => (/* binding */ RotateDirection)\n/* harmony export */ });\nvar RotateDirection;\n(function (RotateDirection) {\n  RotateDirection[\"clockwise\"] = \"clockwise\";\n  RotateDirection[\"counterClockwise\"] = \"counter-clockwise\";\n  RotateDirection[\"random\"] = \"random\";\n})(RotateDirection || (RotateDirection = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Directions/RotateDirection.js?");

/***/ }),

/***/ "./dist/browser/Enums/InteractivityDetect.js":
/*!***************************************************!*\
  !*** ./dist/browser/Enums/InteractivityDetect.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InteractivityDetect: () => (/* binding */ InteractivityDetect)\n/* harmony export */ });\nvar InteractivityDetect;\n(function (InteractivityDetect) {\n  InteractivityDetect[\"canvas\"] = \"canvas\";\n  InteractivityDetect[\"parent\"] = \"parent\";\n  InteractivityDetect[\"window\"] = \"window\";\n})(InteractivityDetect || (InteractivityDetect = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/InteractivityDetect.js?");

/***/ }),

/***/ "./dist/browser/Enums/Modes/AnimationMode.js":
/*!***************************************************!*\
  !*** ./dist/browser/Enums/Modes/AnimationMode.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimationMode: () => (/* binding */ AnimationMode)\n/* harmony export */ });\nvar AnimationMode;\n(function (AnimationMode) {\n  AnimationMode[\"auto\"] = \"auto\";\n  AnimationMode[\"increase\"] = \"increase\";\n  AnimationMode[\"decrease\"] = \"decrease\";\n  AnimationMode[\"random\"] = \"random\";\n})(AnimationMode || (AnimationMode = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Modes/AnimationMode.js?");

/***/ }),

/***/ "./dist/browser/Enums/Modes/CollisionMode.js":
/*!***************************************************!*\
  !*** ./dist/browser/Enums/Modes/CollisionMode.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CollisionMode: () => (/* binding */ CollisionMode)\n/* harmony export */ });\nvar CollisionMode;\n(function (CollisionMode) {\n  CollisionMode[\"absorb\"] = \"absorb\";\n  CollisionMode[\"bounce\"] = \"bounce\";\n  CollisionMode[\"destroy\"] = \"destroy\";\n})(CollisionMode || (CollisionMode = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Modes/CollisionMode.js?");

/***/ }),

/***/ "./dist/browser/Enums/Modes/LimitMode.js":
/*!***********************************************!*\
  !*** ./dist/browser/Enums/Modes/LimitMode.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LimitMode: () => (/* binding */ LimitMode)\n/* harmony export */ });\nvar LimitMode;\n(function (LimitMode) {\n  LimitMode[\"delete\"] = \"delete\";\n  LimitMode[\"wait\"] = \"wait\";\n})(LimitMode || (LimitMode = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Modes/LimitMode.js?");

/***/ }),

/***/ "./dist/browser/Enums/Modes/OutMode.js":
/*!*********************************************!*\
  !*** ./dist/browser/Enums/Modes/OutMode.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OutMode: () => (/* binding */ OutMode)\n/* harmony export */ });\nvar OutMode;\n(function (OutMode) {\n  OutMode[\"bounce\"] = \"bounce\";\n  OutMode[\"none\"] = \"none\";\n  OutMode[\"out\"] = \"out\";\n  OutMode[\"destroy\"] = \"destroy\";\n  OutMode[\"split\"] = \"split\";\n})(OutMode || (OutMode = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Modes/OutMode.js?");

/***/ }),

/***/ "./dist/browser/Enums/Modes/PixelMode.js":
/*!***********************************************!*\
  !*** ./dist/browser/Enums/Modes/PixelMode.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PixelMode: () => (/* binding */ PixelMode)\n/* harmony export */ });\nvar PixelMode;\n(function (PixelMode) {\n  PixelMode[\"precise\"] = \"precise\";\n  PixelMode[\"percent\"] = \"percent\";\n})(PixelMode || (PixelMode = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Modes/PixelMode.js?");

/***/ }),

/***/ "./dist/browser/Enums/Modes/ResponsiveMode.js":
/*!****************************************************!*\
  !*** ./dist/browser/Enums/Modes/ResponsiveMode.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResponsiveMode: () => (/* binding */ ResponsiveMode)\n/* harmony export */ });\nvar ResponsiveMode;\n(function (ResponsiveMode) {\n  ResponsiveMode[\"screen\"] = \"screen\";\n  ResponsiveMode[\"canvas\"] = \"canvas\";\n})(ResponsiveMode || (ResponsiveMode = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Modes/ResponsiveMode.js?");

/***/ }),

/***/ "./dist/browser/Enums/Modes/ThemeMode.js":
/*!***********************************************!*\
  !*** ./dist/browser/Enums/Modes/ThemeMode.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeMode: () => (/* binding */ ThemeMode)\n/* harmony export */ });\nvar ThemeMode;\n(function (ThemeMode) {\n  ThemeMode[\"any\"] = \"any\";\n  ThemeMode[\"dark\"] = \"dark\";\n  ThemeMode[\"light\"] = \"light\";\n})(ThemeMode || (ThemeMode = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Modes/ThemeMode.js?");

/***/ }),

/***/ "./dist/browser/Enums/Types/AlterType.js":
/*!***********************************************!*\
  !*** ./dist/browser/Enums/Types/AlterType.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlterType: () => (/* binding */ AlterType)\n/* harmony export */ });\nvar AlterType;\n(function (AlterType) {\n  AlterType[\"darken\"] = \"darken\";\n  AlterType[\"enlighten\"] = \"enlighten\";\n})(AlterType || (AlterType = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Types/AlterType.js?");

/***/ }),

/***/ "./dist/browser/Enums/Types/DestroyType.js":
/*!*************************************************!*\
  !*** ./dist/browser/Enums/Types/DestroyType.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DestroyType: () => (/* binding */ DestroyType)\n/* harmony export */ });\nvar DestroyType;\n(function (DestroyType) {\n  DestroyType[\"none\"] = \"none\";\n  DestroyType[\"max\"] = \"max\";\n  DestroyType[\"min\"] = \"min\";\n})(DestroyType || (DestroyType = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Types/DestroyType.js?");

/***/ }),

/***/ "./dist/browser/Enums/Types/DivType.js":
/*!*********************************************!*\
  !*** ./dist/browser/Enums/Types/DivType.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DivType: () => (/* binding */ DivType)\n/* harmony export */ });\nvar DivType;\n(function (DivType) {\n  DivType[\"circle\"] = \"circle\";\n  DivType[\"rectangle\"] = \"rectangle\";\n})(DivType || (DivType = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Types/DivType.js?");

/***/ }),

/***/ "./dist/browser/Enums/Types/EasingType.js":
/*!************************************************!*\
  !*** ./dist/browser/Enums/Types/EasingType.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EasingType: () => (/* binding */ EasingType)\n/* harmony export */ });\nvar EasingType;\n(function (EasingType) {\n  EasingType[\"easeInBack\"] = \"ease-in-back\";\n  EasingType[\"easeInCirc\"] = \"ease-in-circ\";\n  EasingType[\"easeInCubic\"] = \"ease-in-cubic\";\n  EasingType[\"easeInLinear\"] = \"ease-in-linear\";\n  EasingType[\"easeInQuad\"] = \"ease-in-quad\";\n  EasingType[\"easeInQuart\"] = \"ease-in-quart\";\n  EasingType[\"easeInQuint\"] = \"ease-in-quint\";\n  EasingType[\"easeInExpo\"] = \"ease-in-expo\";\n  EasingType[\"easeInSine\"] = \"ease-in-sine\";\n  EasingType[\"easeOutBack\"] = \"ease-out-back\";\n  EasingType[\"easeOutCirc\"] = \"ease-out-circ\";\n  EasingType[\"easeOutCubic\"] = \"ease-out-cubic\";\n  EasingType[\"easeOutLinear\"] = \"ease-out-linear\";\n  EasingType[\"easeOutQuad\"] = \"ease-out-quad\";\n  EasingType[\"easeOutQuart\"] = \"ease-out-quart\";\n  EasingType[\"easeOutQuint\"] = \"ease-out-quint\";\n  EasingType[\"easeOutExpo\"] = \"ease-out-expo\";\n  EasingType[\"easeOutSine\"] = \"ease-out-sine\";\n  EasingType[\"easeInOutBack\"] = \"ease-in-out-back\";\n  EasingType[\"easeInOutCirc\"] = \"ease-in-out-circ\";\n  EasingType[\"easeInOutCubic\"] = \"ease-in-out-cubic\";\n  EasingType[\"easeInOutLinear\"] = \"ease-in-out-linear\";\n  EasingType[\"easeInOutQuad\"] = \"ease-in-out-quad\";\n  EasingType[\"easeInOutQuart\"] = \"ease-in-out-quart\";\n  EasingType[\"easeInOutQuint\"] = \"ease-in-out-quint\";\n  EasingType[\"easeInOutExpo\"] = \"ease-in-out-expo\";\n  EasingType[\"easeInOutSine\"] = \"ease-in-out-sine\";\n})(EasingType || (EasingType = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Types/EasingType.js?");

/***/ }),

/***/ "./dist/browser/Enums/Types/EventType.js":
/*!***********************************************!*\
  !*** ./dist/browser/Enums/Types/EventType.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventType: () => (/* binding */ EventType)\n/* harmony export */ });\nvar EventType;\n(function (EventType) {\n  EventType[\"configAdded\"] = \"configAdded\";\n  EventType[\"containerInit\"] = \"containerInit\";\n  EventType[\"particlesSetup\"] = \"particlesSetup\";\n  EventType[\"containerStarted\"] = \"containerStarted\";\n  EventType[\"containerStopped\"] = \"containerStopped\";\n  EventType[\"containerDestroyed\"] = \"containerDestroyed\";\n  EventType[\"containerPaused\"] = \"containerPaused\";\n  EventType[\"containerPlay\"] = \"containerPlay\";\n  EventType[\"containerBuilt\"] = \"containerBuilt\";\n  EventType[\"particleAdded\"] = \"particleAdded\";\n  EventType[\"particleDestroyed\"] = \"particleDestroyed\";\n  EventType[\"particleRemoved\"] = \"particleRemoved\";\n})(EventType || (EventType = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Types/EventType.js?");

/***/ }),

/***/ "./dist/browser/Enums/Types/GradientType.js":
/*!**************************************************!*\
  !*** ./dist/browser/Enums/Types/GradientType.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GradientType: () => (/* binding */ GradientType)\n/* harmony export */ });\nvar GradientType;\n(function (GradientType) {\n  GradientType[\"linear\"] = \"linear\";\n  GradientType[\"radial\"] = \"radial\";\n  GradientType[\"random\"] = \"random\";\n})(GradientType || (GradientType = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Types/GradientType.js?");

/***/ }),

/***/ "./dist/browser/Enums/Types/InteractorType.js":
/*!****************************************************!*\
  !*** ./dist/browser/Enums/Types/InteractorType.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InteractorType: () => (/* binding */ InteractorType)\n/* harmony export */ });\nvar InteractorType;\n(function (InteractorType) {\n  InteractorType[\"external\"] = \"external\";\n  InteractorType[\"particles\"] = \"particles\";\n})(InteractorType || (InteractorType = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Types/InteractorType.js?");

/***/ }),

/***/ "./dist/browser/Enums/Types/ParticleOutType.js":
/*!*****************************************************!*\
  !*** ./dist/browser/Enums/Types/ParticleOutType.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParticleOutType: () => (/* binding */ ParticleOutType)\n/* harmony export */ });\nvar ParticleOutType;\n(function (ParticleOutType) {\n  ParticleOutType[\"normal\"] = \"normal\";\n  ParticleOutType[\"inside\"] = \"inside\";\n  ParticleOutType[\"outside\"] = \"outside\";\n})(ParticleOutType || (ParticleOutType = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Types/ParticleOutType.js?");

/***/ }),

/***/ "./dist/browser/Enums/Types/StartValueType.js":
/*!****************************************************!*\
  !*** ./dist/browser/Enums/Types/StartValueType.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StartValueType: () => (/* binding */ StartValueType)\n/* harmony export */ });\nvar StartValueType;\n(function (StartValueType) {\n  StartValueType[\"max\"] = \"max\";\n  StartValueType[\"min\"] = \"min\";\n  StartValueType[\"random\"] = \"random\";\n})(StartValueType || (StartValueType = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Enums/Types/StartValueType.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/AnimatableColor.js":
/*!*********************************************************!*\
  !*** ./dist/browser/Options/Classes/AnimatableColor.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatableColor: () => (/* binding */ AnimatableColor)\n/* harmony export */ });\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _HslAnimation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./HslAnimation.js */ \"./dist/browser/Options/Classes/HslAnimation.js\");\n/* harmony import */ var _OptionsColor_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./OptionsColor.js */ \"./dist/browser/Options/Classes/OptionsColor.js\");\n\n\n\nclass AnimatableColor extends _OptionsColor_js__WEBPACK_IMPORTED_MODULE_0__.OptionsColor {\n  constructor() {\n    super();\n    this.animation = new _HslAnimation_js__WEBPACK_IMPORTED_MODULE_1__.HslAnimation();\n  }\n  static create(source, data) {\n    const color = new AnimatableColor();\n    color.load(source);\n    if (data !== undefined) {\n      if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(data) || (0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_2__.isArray)(data)) {\n        color.load({\n          value: data\n        });\n      } else {\n        color.load(data);\n      }\n    }\n    return color;\n  }\n  load(data) {\n    super.load(data);\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_2__.isNull)(data)) {\n      return;\n    }\n    const colorAnimation = data.animation;\n    if (colorAnimation !== undefined) {\n      if (colorAnimation.enable !== undefined) {\n        this.animation.h.load(colorAnimation);\n      } else {\n        this.animation.load(data.animation);\n      }\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/AnimatableColor.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/AnimationOptions.js":
/*!**********************************************************!*\
  !*** ./dist/browser/Options/Classes/AnimationOptions.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimationOptions: () => (/* binding */ AnimationOptions),\n/* harmony export */   RangedAnimationOptions: () => (/* binding */ RangedAnimationOptions)\n/* harmony export */ });\n/* harmony import */ var _Enums_Modes_AnimationMode_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../Enums/Modes/AnimationMode.js */ \"./dist/browser/Enums/Modes/AnimationMode.js\");\n/* harmony import */ var _Enums_Types_StartValueType_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../Enums/Types/StartValueType.js */ \"./dist/browser/Enums/Types/StartValueType.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n\n\n\n\nclass AnimationOptions {\n  constructor() {\n    this.count = 0;\n    this.enable = false;\n    this.speed = 1;\n    this.decay = 0;\n    this.delay = 0;\n    this.sync = false;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.count !== undefined) {\n      this.count = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__.setRangeValue)(data.count);\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.speed !== undefined) {\n      this.speed = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__.setRangeValue)(data.speed);\n    }\n    if (data.decay !== undefined) {\n      this.decay = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__.setRangeValue)(data.decay);\n    }\n    if (data.delay !== undefined) {\n      this.delay = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__.setRangeValue)(data.delay);\n    }\n    if (data.sync !== undefined) {\n      this.sync = data.sync;\n    }\n  }\n}\nclass RangedAnimationOptions extends AnimationOptions {\n  constructor() {\n    super();\n    this.mode = _Enums_Modes_AnimationMode_js__WEBPACK_IMPORTED_MODULE_2__.AnimationMode.auto;\n    this.startValue = _Enums_Types_StartValueType_js__WEBPACK_IMPORTED_MODULE_3__.StartValueType.random;\n  }\n  load(data) {\n    super.load(data);\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    if (data.startValue !== undefined) {\n      this.startValue = data.startValue;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/AnimationOptions.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Background/Background.js":
/*!***************************************************************!*\
  !*** ./dist/browser/Options/Classes/Background/Background.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Background: () => (/* binding */ Background)\n/* harmony export */ });\n/* harmony import */ var _OptionsColor_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../OptionsColor.js */ \"./dist/browser/Options/Classes/OptionsColor.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass Background {\n  constructor() {\n    this.color = new _OptionsColor_js__WEBPACK_IMPORTED_MODULE_0__.OptionsColor();\n    this.color.value = \"\";\n    this.image = \"\";\n    this.position = \"\";\n    this.repeat = \"\";\n    this.size = \"\";\n    this.opacity = 1;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    if (data.color !== undefined) {\n      this.color = _OptionsColor_js__WEBPACK_IMPORTED_MODULE_0__.OptionsColor.create(this.color, data.color);\n    }\n    if (data.image !== undefined) {\n      this.image = data.image;\n    }\n    if (data.position !== undefined) {\n      this.position = data.position;\n    }\n    if (data.repeat !== undefined) {\n      this.repeat = data.repeat;\n    }\n    if (data.size !== undefined) {\n      this.size = data.size;\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Background/Background.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/BackgroundMask/BackgroundMask.js":
/*!***********************************************************************!*\
  !*** ./dist/browser/Options/Classes/BackgroundMask/BackgroundMask.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackgroundMask: () => (/* binding */ BackgroundMask)\n/* harmony export */ });\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _BackgroundMaskCover_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BackgroundMaskCover.js */ \"./dist/browser/Options/Classes/BackgroundMask/BackgroundMaskCover.js\");\n\n\nclass BackgroundMask {\n  constructor() {\n    this.composite = \"destination-out\";\n    this.cover = new _BackgroundMaskCover_js__WEBPACK_IMPORTED_MODULE_0__.BackgroundMaskCover();\n    this.enable = false;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    if (data.composite !== undefined) {\n      this.composite = data.composite;\n    }\n    if (data.cover !== undefined) {\n      const cover = data.cover,\n        color = (0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isString)(data.cover) ? {\n          color: data.cover\n        } : data.cover;\n      this.cover.load(cover.color !== undefined || cover.image !== undefined ? cover : {\n        color: color\n      });\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/BackgroundMask/BackgroundMask.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/BackgroundMask/BackgroundMaskCover.js":
/*!****************************************************************************!*\
  !*** ./dist/browser/Options/Classes/BackgroundMask/BackgroundMaskCover.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackgroundMaskCover: () => (/* binding */ BackgroundMaskCover)\n/* harmony export */ });\n/* harmony import */ var _OptionsColor_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../OptionsColor.js */ \"./dist/browser/Options/Classes/OptionsColor.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass BackgroundMaskCover {\n  constructor() {\n    this.opacity = 1;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.color !== undefined) {\n      this.color = _OptionsColor_js__WEBPACK_IMPORTED_MODULE_1__.OptionsColor.create(this.color, data.color);\n    }\n    if (data.image !== undefined) {\n      this.image = data.image;\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/BackgroundMask/BackgroundMaskCover.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/ColorAnimation.js":
/*!********************************************************!*\
  !*** ./dist/browser/Options/Classes/ColorAnimation.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColorAnimation: () => (/* binding */ ColorAnimation)\n/* harmony export */ });\n/* harmony import */ var _AnimationOptions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AnimationOptions.js */ \"./dist/browser/Options/Classes/AnimationOptions.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n\n\n\nclass ColorAnimation extends _AnimationOptions_js__WEBPACK_IMPORTED_MODULE_0__.AnimationOptions {\n  constructor() {\n    super();\n    this.offset = 0;\n    this.sync = true;\n  }\n  load(data) {\n    super.load(data);\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    if (data.offset !== undefined) {\n      this.offset = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.setRangeValue)(data.offset);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/ColorAnimation.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/FullScreen/FullScreen.js":
/*!***************************************************************!*\
  !*** ./dist/browser/Options/Classes/FullScreen/FullScreen.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FullScreen: () => (/* binding */ FullScreen)\n/* harmony export */ });\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\nclass FullScreen {\n  constructor() {\n    this.enable = true;\n    this.zIndex = 0;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.zIndex !== undefined) {\n      this.zIndex = data.zIndex;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/FullScreen/FullScreen.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/HslAnimation.js":
/*!******************************************************!*\
  !*** ./dist/browser/Options/Classes/HslAnimation.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HslAnimation: () => (/* binding */ HslAnimation)\n/* harmony export */ });\n/* harmony import */ var _ColorAnimation_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ColorAnimation.js */ \"./dist/browser/Options/Classes/ColorAnimation.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass HslAnimation {\n  constructor() {\n    this.h = new _ColorAnimation_js__WEBPACK_IMPORTED_MODULE_0__.ColorAnimation();\n    this.s = new _ColorAnimation_js__WEBPACK_IMPORTED_MODULE_0__.ColorAnimation();\n    this.l = new _ColorAnimation_js__WEBPACK_IMPORTED_MODULE_0__.ColorAnimation();\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    this.h.load(data.h);\n    this.s.load(data.s);\n    this.l.load(data.l);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/HslAnimation.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Interactivity/Events/ClickEvent.js":
/*!*************************************************************************!*\
  !*** ./dist/browser/Options/Classes/Interactivity/Events/ClickEvent.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClickEvent: () => (/* binding */ ClickEvent)\n/* harmony export */ });\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\nclass ClickEvent {\n  constructor() {\n    this.enable = false;\n    this.mode = [];\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Interactivity/Events/ClickEvent.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Interactivity/Events/DivEvent.js":
/*!***********************************************************************!*\
  !*** ./dist/browser/Options/Classes/Interactivity/Events/DivEvent.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DivEvent: () => (/* binding */ DivEvent)\n/* harmony export */ });\n/* harmony import */ var _Enums_Types_DivType_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Enums/Types/DivType.js */ \"./dist/browser/Enums/Types/DivType.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass DivEvent {\n  constructor() {\n    this.selectors = [];\n    this.enable = false;\n    this.mode = [];\n    this.type = _Enums_Types_DivType_js__WEBPACK_IMPORTED_MODULE_0__.DivType.circle;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    if (data.selectors !== undefined) {\n      this.selectors = data.selectors;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    if (data.type !== undefined) {\n      this.type = data.type;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Interactivity/Events/DivEvent.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Interactivity/Events/Events.js":
/*!*********************************************************************!*\
  !*** ./dist/browser/Options/Classes/Interactivity/Events/Events.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Events: () => (/* binding */ Events)\n/* harmony export */ });\n/* harmony import */ var _ClickEvent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ClickEvent.js */ \"./dist/browser/Options/Classes/Interactivity/Events/ClickEvent.js\");\n/* harmony import */ var _DivEvent_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DivEvent.js */ \"./dist/browser/Options/Classes/Interactivity/Events/DivEvent.js\");\n/* harmony import */ var _HoverEvent_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HoverEvent.js */ \"./dist/browser/Options/Classes/Interactivity/Events/HoverEvent.js\");\n/* harmony import */ var _ResizeEvent_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ResizeEvent.js */ \"./dist/browser/Options/Classes/Interactivity/Events/ResizeEvent.js\");\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\n\n\n\n\nclass Events {\n  constructor() {\n    this.onClick = new _ClickEvent_js__WEBPACK_IMPORTED_MODULE_0__.ClickEvent();\n    this.onDiv = new _DivEvent_js__WEBPACK_IMPORTED_MODULE_1__.DivEvent();\n    this.onHover = new _HoverEvent_js__WEBPACK_IMPORTED_MODULE_2__.HoverEvent();\n    this.resize = new _ResizeEvent_js__WEBPACK_IMPORTED_MODULE_3__.ResizeEvent();\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_4__.isNull)(data)) {\n      return;\n    }\n    this.onClick.load(data.onClick);\n    const onDiv = data.onDiv;\n    if (onDiv !== undefined) {\n      this.onDiv = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_5__.executeOnSingleOrMultiple)(onDiv, t => {\n        const tmp = new _DivEvent_js__WEBPACK_IMPORTED_MODULE_1__.DivEvent();\n        tmp.load(t);\n        return tmp;\n      });\n    }\n    this.onHover.load(data.onHover);\n    this.resize.load(data.resize);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Interactivity/Events/Events.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Interactivity/Events/HoverEvent.js":
/*!*************************************************************************!*\
  !*** ./dist/browser/Options/Classes/Interactivity/Events/HoverEvent.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HoverEvent: () => (/* binding */ HoverEvent)\n/* harmony export */ });\n/* harmony import */ var _Parallax_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Parallax.js */ \"./dist/browser/Options/Classes/Interactivity/Events/Parallax.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass HoverEvent {\n  constructor() {\n    this.enable = false;\n    this.mode = [];\n    this.parallax = new _Parallax_js__WEBPACK_IMPORTED_MODULE_0__.Parallax();\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    this.parallax.load(data.parallax);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Interactivity/Events/HoverEvent.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Interactivity/Events/Parallax.js":
/*!***********************************************************************!*\
  !*** ./dist/browser/Options/Classes/Interactivity/Events/Parallax.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parallax: () => (/* binding */ Parallax)\n/* harmony export */ });\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\nclass Parallax {\n  constructor() {\n    this.enable = false;\n    this.force = 2;\n    this.smooth = 10;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.force !== undefined) {\n      this.force = data.force;\n    }\n    if (data.smooth !== undefined) {\n      this.smooth = data.smooth;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Interactivity/Events/Parallax.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Interactivity/Events/ResizeEvent.js":
/*!**************************************************************************!*\
  !*** ./dist/browser/Options/Classes/Interactivity/Events/ResizeEvent.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResizeEvent: () => (/* binding */ ResizeEvent)\n/* harmony export */ });\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\nclass ResizeEvent {\n  constructor() {\n    this.delay = 0.5;\n    this.enable = true;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.delay !== undefined) {\n      this.delay = data.delay;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Interactivity/Events/ResizeEvent.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Interactivity/Interactivity.js":
/*!*********************************************************************!*\
  !*** ./dist/browser/Options/Classes/Interactivity/Interactivity.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Interactivity: () => (/* binding */ Interactivity)\n/* harmony export */ });\n/* harmony import */ var _Events_Events_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Events/Events.js */ \"./dist/browser/Options/Classes/Interactivity/Events/Events.js\");\n/* harmony import */ var _Enums_InteractivityDetect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../Enums/InteractivityDetect.js */ \"./dist/browser/Enums/InteractivityDetect.js\");\n/* harmony import */ var _Modes_Modes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Modes/Modes.js */ \"./dist/browser/Options/Classes/Interactivity/Modes/Modes.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\n\n\nclass Interactivity {\n  constructor(engine, container) {\n    this.detectsOn = _Enums_InteractivityDetect_js__WEBPACK_IMPORTED_MODULE_0__.InteractivityDetect.window;\n    this.events = new _Events_Events_js__WEBPACK_IMPORTED_MODULE_1__.Events();\n    this.modes = new _Modes_Modes_js__WEBPACK_IMPORTED_MODULE_2__.Modes(engine, container);\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_3__.isNull)(data)) {\n      return;\n    }\n    const detectsOn = data.detectsOn;\n    if (detectsOn !== undefined) {\n      this.detectsOn = detectsOn;\n    }\n    this.events.load(data.events);\n    this.modes.load(data.modes);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Interactivity/Interactivity.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Interactivity/Modes/Modes.js":
/*!*******************************************************************!*\
  !*** ./dist/browser/Options/Classes/Interactivity/Modes/Modes.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Modes: () => (/* binding */ Modes)\n/* harmony export */ });\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\nclass Modes {\n  constructor(engine, container) {\n    this._engine = engine;\n    this._container = container;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (!this._container) {\n      return;\n    }\n    const interactors = this._engine.interactors.get(this._container);\n    if (!interactors) {\n      return;\n    }\n    for (const interactor of interactors) {\n      if (!interactor.loadModeOptions) {\n        continue;\n      }\n      interactor.loadModeOptions(this, data);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Interactivity/Modes/Modes.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/ManualParticle.js":
/*!********************************************************!*\
  !*** ./dist/browser/Options/Classes/ManualParticle.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ManualParticle: () => (/* binding */ ManualParticle)\n/* harmony export */ });\n/* harmony import */ var _Enums_Modes_PixelMode_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../Enums/Modes/PixelMode.js */ \"./dist/browser/Enums/Modes/PixelMode.js\");\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../Core/Utils/Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n\n\n\n\nclass ManualParticle {\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.position) {\n      this.position = {\n        x: data.position.x ?? _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.manualDefaultPosition,\n        y: data.position.y ?? _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_1__.manualDefaultPosition,\n        mode: data.position.mode ?? _Enums_Modes_PixelMode_js__WEBPACK_IMPORTED_MODULE_2__.PixelMode.percent\n      };\n    }\n    if (data.options) {\n      this.options = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_3__.deepExtend)({}, data.options);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/ManualParticle.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Options.js":
/*!*************************************************!*\
  !*** ./dist/browser/Options/Classes/Options.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Options: () => (/* binding */ Options)\n/* harmony export */ });\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _Background_Background_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Background/Background.js */ \"./dist/browser/Options/Classes/Background/Background.js\");\n/* harmony import */ var _BackgroundMask_BackgroundMask_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BackgroundMask/BackgroundMask.js */ \"./dist/browser/Options/Classes/BackgroundMask/BackgroundMask.js\");\n/* harmony import */ var _FullScreen_FullScreen_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FullScreen/FullScreen.js */ \"./dist/browser/Options/Classes/FullScreen/FullScreen.js\");\n/* harmony import */ var _Interactivity_Interactivity_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Interactivity/Interactivity.js */ \"./dist/browser/Options/Classes/Interactivity/Interactivity.js\");\n/* harmony import */ var _ManualParticle_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ManualParticle.js */ \"./dist/browser/Options/Classes/ManualParticle.js\");\n/* harmony import */ var _Responsive_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Responsive.js */ \"./dist/browser/Options/Classes/Responsive.js\");\n/* harmony import */ var _Enums_Modes_ResponsiveMode_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../Enums/Modes/ResponsiveMode.js */ \"./dist/browser/Enums/Modes/ResponsiveMode.js\");\n/* harmony import */ var _Theme_Theme_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./Theme/Theme.js */ \"./dist/browser/Options/Classes/Theme/Theme.js\");\n/* harmony import */ var _Enums_Modes_ThemeMode_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../Enums/Modes/ThemeMode.js */ \"./dist/browser/Enums/Modes/ThemeMode.js\");\n/* harmony import */ var _Utils_OptionsUtils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../Utils/OptionsUtils.js */ \"./dist/browser/Utils/OptionsUtils.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nclass Options {\n  constructor(engine, container) {\n    this._findDefaultTheme = mode => {\n      return this.themes.find(theme => theme.default.value && theme.default.mode === mode) ?? this.themes.find(theme => theme.default.value && theme.default.mode === _Enums_Modes_ThemeMode_js__WEBPACK_IMPORTED_MODULE_0__.ThemeMode.any);\n    };\n    this._importPreset = preset => {\n      this.load(this._engine.getPreset(preset));\n    };\n    this._engine = engine;\n    this._container = container;\n    this.autoPlay = true;\n    this.background = new _Background_Background_js__WEBPACK_IMPORTED_MODULE_1__.Background();\n    this.backgroundMask = new _BackgroundMask_BackgroundMask_js__WEBPACK_IMPORTED_MODULE_2__.BackgroundMask();\n    this.clear = true;\n    this.defaultThemes = {};\n    this.delay = 0;\n    this.fullScreen = new _FullScreen_FullScreen_js__WEBPACK_IMPORTED_MODULE_3__.FullScreen();\n    this.detectRetina = true;\n    this.duration = 0;\n    this.fpsLimit = 120;\n    this.interactivity = new _Interactivity_Interactivity_js__WEBPACK_IMPORTED_MODULE_4__.Interactivity(engine, container);\n    this.manualParticles = [];\n    this.particles = (0,_Utils_OptionsUtils_js__WEBPACK_IMPORTED_MODULE_5__.loadParticlesOptions)(this._engine, this._container);\n    this.pauseOnBlur = true;\n    this.pauseOnOutsideViewport = true;\n    this.responsive = [];\n    this.smooth = false;\n    this.style = {};\n    this.themes = [];\n    this.zLayers = 100;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_6__.isNull)(data)) {\n      return;\n    }\n    if (data.preset !== undefined) {\n      (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_7__.executeOnSingleOrMultiple)(data.preset, preset => this._importPreset(preset));\n    }\n    if (data.autoPlay !== undefined) {\n      this.autoPlay = data.autoPlay;\n    }\n    if (data.clear !== undefined) {\n      this.clear = data.clear;\n    }\n    if (data.key !== undefined) {\n      this.key = data.key;\n    }\n    if (data.name !== undefined) {\n      this.name = data.name;\n    }\n    if (data.delay !== undefined) {\n      this.delay = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_8__.setRangeValue)(data.delay);\n    }\n    const detectRetina = data.detectRetina;\n    if (detectRetina !== undefined) {\n      this.detectRetina = detectRetina;\n    }\n    if (data.duration !== undefined) {\n      this.duration = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_8__.setRangeValue)(data.duration);\n    }\n    const fpsLimit = data.fpsLimit;\n    if (fpsLimit !== undefined) {\n      this.fpsLimit = fpsLimit;\n    }\n    if (data.pauseOnBlur !== undefined) {\n      this.pauseOnBlur = data.pauseOnBlur;\n    }\n    if (data.pauseOnOutsideViewport !== undefined) {\n      this.pauseOnOutsideViewport = data.pauseOnOutsideViewport;\n    }\n    if (data.zLayers !== undefined) {\n      this.zLayers = data.zLayers;\n    }\n    this.background.load(data.background);\n    const fullScreen = data.fullScreen;\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_6__.isBoolean)(fullScreen)) {\n      this.fullScreen.enable = fullScreen;\n    } else {\n      this.fullScreen.load(fullScreen);\n    }\n    this.backgroundMask.load(data.backgroundMask);\n    this.interactivity.load(data.interactivity);\n    if (data.manualParticles) {\n      this.manualParticles = data.manualParticles.map(t => {\n        const tmp = new _ManualParticle_js__WEBPACK_IMPORTED_MODULE_9__.ManualParticle();\n        tmp.load(t);\n        return tmp;\n      });\n    }\n    this.particles.load(data.particles);\n    this.style = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_7__.deepExtend)(this.style, data.style);\n    this._engine.loadOptions(this, data);\n    if (data.smooth !== undefined) {\n      this.smooth = data.smooth;\n    }\n    const interactors = this._engine.interactors.get(this._container);\n    if (interactors) {\n      for (const interactor of interactors) {\n        if (interactor.loadOptions) {\n          interactor.loadOptions(this, data);\n        }\n      }\n    }\n    if (data.responsive !== undefined) {\n      for (const responsive of data.responsive) {\n        const optResponsive = new _Responsive_js__WEBPACK_IMPORTED_MODULE_10__.Responsive();\n        optResponsive.load(responsive);\n        this.responsive.push(optResponsive);\n      }\n    }\n    this.responsive.sort((a, b) => a.maxWidth - b.maxWidth);\n    if (data.themes !== undefined) {\n      for (const theme of data.themes) {\n        const existingTheme = this.themes.find(t => t.name === theme.name);\n        if (!existingTheme) {\n          const optTheme = new _Theme_Theme_js__WEBPACK_IMPORTED_MODULE_11__.Theme();\n          optTheme.load(theme);\n          this.themes.push(optTheme);\n        } else {\n          existingTheme.load(theme);\n        }\n      }\n    }\n    this.defaultThemes.dark = this._findDefaultTheme(_Enums_Modes_ThemeMode_js__WEBPACK_IMPORTED_MODULE_0__.ThemeMode.dark)?.name;\n    this.defaultThemes.light = this._findDefaultTheme(_Enums_Modes_ThemeMode_js__WEBPACK_IMPORTED_MODULE_0__.ThemeMode.light)?.name;\n  }\n  setResponsive(width, pxRatio, defaultOptions) {\n    this.load(defaultOptions);\n    const responsiveOptions = this.responsive.find(t => t.mode === _Enums_Modes_ResponsiveMode_js__WEBPACK_IMPORTED_MODULE_12__.ResponsiveMode.screen && screen ? t.maxWidth > screen.availWidth : t.maxWidth * pxRatio > width);\n    this.load(responsiveOptions?.options);\n    return responsiveOptions?.maxWidth;\n  }\n  setTheme(name) {\n    if (name) {\n      const chosenTheme = this.themes.find(theme => theme.name === name);\n      if (chosenTheme) {\n        this.load(chosenTheme.options);\n      }\n    } else {\n      const mediaMatch = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_7__.safeMatchMedia)(\"(prefers-color-scheme: dark)\"),\n        clientDarkMode = mediaMatch?.matches,\n        defaultTheme = this._findDefaultTheme(clientDarkMode ? _Enums_Modes_ThemeMode_js__WEBPACK_IMPORTED_MODULE_0__.ThemeMode.dark : _Enums_Modes_ThemeMode_js__WEBPACK_IMPORTED_MODULE_0__.ThemeMode.light);\n      if (defaultTheme) {\n        this.load(defaultTheme.options);\n      }\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Options.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/OptionsColor.js":
/*!******************************************************!*\
  !*** ./dist/browser/Options/Classes/OptionsColor.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptionsColor: () => (/* binding */ OptionsColor)\n/* harmony export */ });\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\nclass OptionsColor {\n  constructor() {\n    this.value = \"\";\n  }\n  static create(source, data) {\n    const color = new OptionsColor();\n    color.load(source);\n    if (data !== undefined) {\n      if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isString)(data) || (0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(data)) {\n        color.load({\n          value: data\n        });\n      } else {\n        color.load(data);\n      }\n    }\n    return color;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (!(0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data.value)) {\n      this.value = data.value;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/OptionsColor.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Bounce/ParticlesBounce.js":
/*!**************************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Bounce/ParticlesBounce.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParticlesBounce: () => (/* binding */ ParticlesBounce)\n/* harmony export */ });\n/* harmony import */ var _ParticlesBounceFactor_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ParticlesBounceFactor.js */ \"./dist/browser/Options/Classes/Particles/Bounce/ParticlesBounceFactor.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass ParticlesBounce {\n  constructor() {\n    this.horizontal = new _ParticlesBounceFactor_js__WEBPACK_IMPORTED_MODULE_0__.ParticlesBounceFactor();\n    this.vertical = new _ParticlesBounceFactor_js__WEBPACK_IMPORTED_MODULE_0__.ParticlesBounceFactor();\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    this.horizontal.load(data.horizontal);\n    this.vertical.load(data.vertical);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Bounce/ParticlesBounce.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Bounce/ParticlesBounceFactor.js":
/*!********************************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Bounce/ParticlesBounceFactor.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParticlesBounceFactor: () => (/* binding */ ParticlesBounceFactor)\n/* harmony export */ });\n/* harmony import */ var _ValueWithRandom_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../ValueWithRandom.js */ \"./dist/browser/Options/Classes/ValueWithRandom.js\");\n\nclass ParticlesBounceFactor extends _ValueWithRandom_js__WEBPACK_IMPORTED_MODULE_0__.ValueWithRandom {\n  constructor() {\n    super();\n    this.value = 1;\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Bounce/ParticlesBounceFactor.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Collisions/Collisions.js":
/*!*************************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Collisions/Collisions.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Collisions: () => (/* binding */ Collisions)\n/* harmony export */ });\n/* harmony import */ var _Enums_Modes_CollisionMode_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../Enums/Modes/CollisionMode.js */ \"./dist/browser/Enums/Modes/CollisionMode.js\");\n/* harmony import */ var _CollisionsAbsorb_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CollisionsAbsorb.js */ \"./dist/browser/Options/Classes/Particles/Collisions/CollisionsAbsorb.js\");\n/* harmony import */ var _CollisionsOverlap_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CollisionsOverlap.js */ \"./dist/browser/Options/Classes/Particles/Collisions/CollisionsOverlap.js\");\n/* harmony import */ var _Bounce_ParticlesBounce_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../Bounce/ParticlesBounce.js */ \"./dist/browser/Options/Classes/Particles/Bounce/ParticlesBounce.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n\n\n\n\n\n\nclass Collisions {\n  constructor() {\n    this.absorb = new _CollisionsAbsorb_js__WEBPACK_IMPORTED_MODULE_0__.CollisionsAbsorb();\n    this.bounce = new _Bounce_ParticlesBounce_js__WEBPACK_IMPORTED_MODULE_1__.ParticlesBounce();\n    this.enable = false;\n    this.maxSpeed = 50;\n    this.mode = _Enums_Modes_CollisionMode_js__WEBPACK_IMPORTED_MODULE_2__.CollisionMode.bounce;\n    this.overlap = new _CollisionsOverlap_js__WEBPACK_IMPORTED_MODULE_3__.CollisionsOverlap();\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_4__.isNull)(data)) {\n      return;\n    }\n    this.absorb.load(data.absorb);\n    this.bounce.load(data.bounce);\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.maxSpeed !== undefined) {\n      this.maxSpeed = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_5__.setRangeValue)(data.maxSpeed);\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    this.overlap.load(data.overlap);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Collisions/Collisions.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Collisions/CollisionsAbsorb.js":
/*!*******************************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Collisions/CollisionsAbsorb.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CollisionsAbsorb: () => (/* binding */ CollisionsAbsorb)\n/* harmony export */ });\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\nclass CollisionsAbsorb {\n  constructor() {\n    this.speed = 2;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.speed !== undefined) {\n      this.speed = data.speed;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Collisions/CollisionsAbsorb.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Collisions/CollisionsOverlap.js":
/*!********************************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Collisions/CollisionsOverlap.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CollisionsOverlap: () => (/* binding */ CollisionsOverlap)\n/* harmony export */ });\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\nclass CollisionsOverlap {\n  constructor() {\n    this.enable = true;\n    this.retries = 0;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.retries !== undefined) {\n      this.retries = data.retries;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Collisions/CollisionsOverlap.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Effect/Effect.js":
/*!*****************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Effect/Effect.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Effect: () => (/* binding */ Effect)\n/* harmony export */ });\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass Effect {\n  constructor() {\n    this.close = true;\n    this.fill = true;\n    this.options = {};\n    this.type = [];\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    const options = data.options;\n    if (options !== undefined) {\n      for (const effect in options) {\n        const item = options[effect];\n        if (item) {\n          this.options[effect] = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_1__.deepExtend)(this.options[effect] ?? {}, item);\n        }\n      }\n    }\n    if (data.close !== undefined) {\n      this.close = data.close;\n    }\n    if (data.fill !== undefined) {\n      this.fill = data.fill;\n    }\n    if (data.type !== undefined) {\n      this.type = data.type;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Effect/Effect.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Move/Move.js":
/*!*************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Move/Move.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Move: () => (/* binding */ Move)\n/* harmony export */ });\n/* harmony import */ var _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../Enums/Directions/MoveDirection.js */ \"./dist/browser/Enums/Directions/MoveDirection.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _MoveAngle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./MoveAngle.js */ \"./dist/browser/Options/Classes/Particles/Move/MoveAngle.js\");\n/* harmony import */ var _MoveAttract_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MoveAttract.js */ \"./dist/browser/Options/Classes/Particles/Move/MoveAttract.js\");\n/* harmony import */ var _MoveCenter_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MoveCenter.js */ \"./dist/browser/Options/Classes/Particles/Move/MoveCenter.js\");\n/* harmony import */ var _MoveGravity_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MoveGravity.js */ \"./dist/browser/Options/Classes/Particles/Move/MoveGravity.js\");\n/* harmony import */ var _Path_MovePath_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Path/MovePath.js */ \"./dist/browser/Options/Classes/Particles/Move/Path/MovePath.js\");\n/* harmony import */ var _MoveTrail_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./MoveTrail.js */ \"./dist/browser/Options/Classes/Particles/Move/MoveTrail.js\");\n/* harmony import */ var _OutModes_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./OutModes.js */ \"./dist/browser/Options/Classes/Particles/Move/OutModes.js\");\n/* harmony import */ var _Spin_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Spin.js */ \"./dist/browser/Options/Classes/Particles/Move/Spin.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../../../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n\n\n\n\n\n\n\n\n\n\n\nclass Move {\n  constructor() {\n    this.angle = new _MoveAngle_js__WEBPACK_IMPORTED_MODULE_0__.MoveAngle();\n    this.attract = new _MoveAttract_js__WEBPACK_IMPORTED_MODULE_1__.MoveAttract();\n    this.center = new _MoveCenter_js__WEBPACK_IMPORTED_MODULE_2__.MoveCenter();\n    this.decay = 0;\n    this.distance = {};\n    this.direction = _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_3__.MoveDirection.none;\n    this.drift = 0;\n    this.enable = false;\n    this.gravity = new _MoveGravity_js__WEBPACK_IMPORTED_MODULE_4__.MoveGravity();\n    this.path = new _Path_MovePath_js__WEBPACK_IMPORTED_MODULE_5__.MovePath();\n    this.outModes = new _OutModes_js__WEBPACK_IMPORTED_MODULE_6__.OutModes();\n    this.random = false;\n    this.size = false;\n    this.speed = 2;\n    this.spin = new _Spin_js__WEBPACK_IMPORTED_MODULE_7__.Spin();\n    this.straight = false;\n    this.trail = new _MoveTrail_js__WEBPACK_IMPORTED_MODULE_8__.MoveTrail();\n    this.vibrate = false;\n    this.warp = false;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_9__.isNull)(data)) {\n      return;\n    }\n    this.angle.load((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_9__.isNumber)(data.angle) ? {\n      value: data.angle\n    } : data.angle);\n    this.attract.load(data.attract);\n    this.center.load(data.center);\n    if (data.decay !== undefined) {\n      this.decay = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_10__.setRangeValue)(data.decay);\n    }\n    if (data.direction !== undefined) {\n      this.direction = data.direction;\n    }\n    if (data.distance !== undefined) {\n      this.distance = (0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_9__.isNumber)(data.distance) ? {\n        horizontal: data.distance,\n        vertical: data.distance\n      } : {\n        ...data.distance\n      };\n    }\n    if (data.drift !== undefined) {\n      this.drift = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_10__.setRangeValue)(data.drift);\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    this.gravity.load(data.gravity);\n    const outModes = data.outModes;\n    if (outModes !== undefined) {\n      if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_9__.isObject)(outModes)) {\n        this.outModes.load(outModes);\n      } else {\n        this.outModes.load({\n          default: outModes\n        });\n      }\n    }\n    this.path.load(data.path);\n    if (data.random !== undefined) {\n      this.random = data.random;\n    }\n    if (data.size !== undefined) {\n      this.size = data.size;\n    }\n    if (data.speed !== undefined) {\n      this.speed = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_10__.setRangeValue)(data.speed);\n    }\n    this.spin.load(data.spin);\n    if (data.straight !== undefined) {\n      this.straight = data.straight;\n    }\n    this.trail.load(data.trail);\n    if (data.vibrate !== undefined) {\n      this.vibrate = data.vibrate;\n    }\n    if (data.warp !== undefined) {\n      this.warp = data.warp;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Move/Move.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Move/MoveAngle.js":
/*!******************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Move/MoveAngle.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MoveAngle: () => (/* binding */ MoveAngle)\n/* harmony export */ });\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n\n\nclass MoveAngle {\n  constructor() {\n    this.offset = 0;\n    this.value = 90;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.offset !== undefined) {\n      this.offset = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__.setRangeValue)(data.offset);\n    }\n    if (data.value !== undefined) {\n      this.value = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__.setRangeValue)(data.value);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Move/MoveAngle.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Move/MoveAttract.js":
/*!********************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Move/MoveAttract.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MoveAttract: () => (/* binding */ MoveAttract)\n/* harmony export */ });\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n\n\nclass MoveAttract {\n  constructor() {\n    this.distance = 200;\n    this.enable = false;\n    this.rotate = {\n      x: 3000,\n      y: 3000\n    };\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.distance !== undefined) {\n      this.distance = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__.setRangeValue)(data.distance);\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.rotate) {\n      const rotateX = data.rotate.x;\n      if (rotateX !== undefined) {\n        this.rotate.x = rotateX;\n      }\n      const rotateY = data.rotate.y;\n      if (rotateY !== undefined) {\n        this.rotate.y = rotateY;\n      }\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Move/MoveAttract.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Move/MoveCenter.js":
/*!*******************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Move/MoveCenter.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MoveCenter: () => (/* binding */ MoveCenter)\n/* harmony export */ });\n/* harmony import */ var _Enums_Modes_PixelMode_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Enums/Modes/PixelMode.js */ \"./dist/browser/Enums/Modes/PixelMode.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass MoveCenter {\n  constructor() {\n    this.x = 50;\n    this.y = 50;\n    this.mode = _Enums_Modes_PixelMode_js__WEBPACK_IMPORTED_MODULE_0__.PixelMode.percent;\n    this.radius = 0;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    if (data.x !== undefined) {\n      this.x = data.x;\n    }\n    if (data.y !== undefined) {\n      this.y = data.y;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    if (data.radius !== undefined) {\n      this.radius = data.radius;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Move/MoveCenter.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Move/MoveGravity.js":
/*!********************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Move/MoveGravity.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MoveGravity: () => (/* binding */ MoveGravity)\n/* harmony export */ });\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n\n\nclass MoveGravity {\n  constructor() {\n    this.acceleration = 9.81;\n    this.enable = false;\n    this.inverse = false;\n    this.maxSpeed = 50;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.acceleration !== undefined) {\n      this.acceleration = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__.setRangeValue)(data.acceleration);\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.inverse !== undefined) {\n      this.inverse = data.inverse;\n    }\n    if (data.maxSpeed !== undefined) {\n      this.maxSpeed = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__.setRangeValue)(data.maxSpeed);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Move/MoveGravity.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Move/MoveTrail.js":
/*!******************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Move/MoveTrail.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MoveTrail: () => (/* binding */ MoveTrail)\n/* harmony export */ });\n/* harmony import */ var _MoveTrailFill_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./MoveTrailFill.js */ \"./dist/browser/Options/Classes/Particles/Move/MoveTrailFill.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass MoveTrail {\n  constructor() {\n    this.enable = false;\n    this.length = 10;\n    this.fill = new _MoveTrailFill_js__WEBPACK_IMPORTED_MODULE_0__.MoveTrailFill();\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.fill !== undefined) {\n      this.fill.load(data.fill);\n    }\n    if (data.length !== undefined) {\n      this.length = data.length;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Move/MoveTrail.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Move/MoveTrailFill.js":
/*!**********************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Move/MoveTrailFill.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MoveTrailFill: () => (/* binding */ MoveTrailFill)\n/* harmony export */ });\n/* harmony import */ var _OptionsColor_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../OptionsColor.js */ \"./dist/browser/Options/Classes/OptionsColor.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass MoveTrailFill {\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.color !== undefined) {\n      this.color = _OptionsColor_js__WEBPACK_IMPORTED_MODULE_1__.OptionsColor.create(this.color, data.color);\n    }\n    if (data.image !== undefined) {\n      this.image = data.image;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Move/MoveTrailFill.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Move/OutModes.js":
/*!*****************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Move/OutModes.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OutModes: () => (/* binding */ OutModes)\n/* harmony export */ });\n/* harmony import */ var _Enums_Modes_OutMode_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Enums/Modes/OutMode.js */ \"./dist/browser/Enums/Modes/OutMode.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass OutModes {\n  constructor() {\n    this.default = _Enums_Modes_OutMode_js__WEBPACK_IMPORTED_MODULE_0__.OutMode.out;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    if (data.default !== undefined) {\n      this.default = data.default;\n    }\n    this.bottom = data.bottom ?? data.default;\n    this.left = data.left ?? data.default;\n    this.right = data.right ?? data.default;\n    this.top = data.top ?? data.default;\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Move/OutModes.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Move/Path/MovePath.js":
/*!**********************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Move/Path/MovePath.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MovePath: () => (/* binding */ MovePath)\n/* harmony export */ });\n/* harmony import */ var _ValueWithRandom_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../ValueWithRandom.js */ \"./dist/browser/Options/Classes/ValueWithRandom.js\");\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\n\nclass MovePath {\n  constructor() {\n    this.clamp = true;\n    this.delay = new _ValueWithRandom_js__WEBPACK_IMPORTED_MODULE_0__.ValueWithRandom();\n    this.enable = false;\n    this.options = {};\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    if (data.clamp !== undefined) {\n      this.clamp = data.clamp;\n    }\n    this.delay.load(data.delay);\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    this.generator = data.generator;\n    if (data.options) {\n      this.options = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_2__.deepExtend)(this.options, data.options);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Move/Path/MovePath.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Move/Spin.js":
/*!*************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Move/Spin.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Spin: () => (/* binding */ Spin)\n/* harmony export */ });\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n\n\n\nclass Spin {\n  constructor() {\n    this.acceleration = 0;\n    this.enable = false;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.acceleration !== undefined) {\n      this.acceleration = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__.setRangeValue)(data.acceleration);\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.position) {\n      this.position = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_2__.deepExtend)({}, data.position);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Move/Spin.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Number/ParticlesDensity.js":
/*!***************************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Number/ParticlesDensity.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParticlesDensity: () => (/* binding */ ParticlesDensity)\n/* harmony export */ });\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\nclass ParticlesDensity {\n  constructor() {\n    this.enable = false;\n    this.width = 1920;\n    this.height = 1080;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    const width = data.width;\n    if (width !== undefined) {\n      this.width = width;\n    }\n    const height = data.height;\n    if (height !== undefined) {\n      this.height = height;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Number/ParticlesDensity.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Number/ParticlesNumber.js":
/*!**************************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Number/ParticlesNumber.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParticlesNumber: () => (/* binding */ ParticlesNumber)\n/* harmony export */ });\n/* harmony import */ var _ParticlesDensity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ParticlesDensity.js */ \"./dist/browser/Options/Classes/Particles/Number/ParticlesDensity.js\");\n/* harmony import */ var _ParticlesNumberLimit_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ParticlesNumberLimit.js */ \"./dist/browser/Options/Classes/Particles/Number/ParticlesNumberLimit.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\n\nclass ParticlesNumber {\n  constructor() {\n    this.density = new _ParticlesDensity_js__WEBPACK_IMPORTED_MODULE_0__.ParticlesDensity();\n    this.limit = new _ParticlesNumberLimit_js__WEBPACK_IMPORTED_MODULE_1__.ParticlesNumberLimit();\n    this.value = 0;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_2__.isNull)(data)) {\n      return;\n    }\n    this.density.load(data.density);\n    this.limit.load(data.limit);\n    if (data.value !== undefined) {\n      this.value = data.value;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Number/ParticlesNumber.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Number/ParticlesNumberLimit.js":
/*!*******************************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Number/ParticlesNumberLimit.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParticlesNumberLimit: () => (/* binding */ ParticlesNumberLimit)\n/* harmony export */ });\n/* harmony import */ var _Enums_Modes_LimitMode_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Enums/Modes/LimitMode.js */ \"./dist/browser/Enums/Modes/LimitMode.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass ParticlesNumberLimit {\n  constructor() {\n    this.mode = _Enums_Modes_LimitMode_js__WEBPACK_IMPORTED_MODULE_0__.LimitMode.delete;\n    this.value = 0;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    if (data.value !== undefined) {\n      this.value = data.value;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Number/ParticlesNumberLimit.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Opacity/Opacity.js":
/*!*******************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Opacity/Opacity.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Opacity: () => (/* binding */ Opacity)\n/* harmony export */ });\n/* harmony import */ var _OpacityAnimation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./OpacityAnimation.js */ \"./dist/browser/Options/Classes/Particles/Opacity/OpacityAnimation.js\");\n/* harmony import */ var _ValueWithRandom_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../ValueWithRandom.js */ \"./dist/browser/Options/Classes/ValueWithRandom.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\n\nclass Opacity extends _ValueWithRandom_js__WEBPACK_IMPORTED_MODULE_0__.RangedAnimationValueWithRandom {\n  constructor() {\n    super();\n    this.animation = new _OpacityAnimation_js__WEBPACK_IMPORTED_MODULE_1__.OpacityAnimation();\n    this.value = 1;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_2__.isNull)(data)) {\n      return;\n    }\n    super.load(data);\n    const animation = data.animation;\n    if (animation !== undefined) {\n      this.animation.load(animation);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Opacity/Opacity.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Opacity/OpacityAnimation.js":
/*!****************************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Opacity/OpacityAnimation.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpacityAnimation: () => (/* binding */ OpacityAnimation)\n/* harmony export */ });\n/* harmony import */ var _Enums_Types_DestroyType_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Enums/Types/DestroyType.js */ \"./dist/browser/Enums/Types/DestroyType.js\");\n/* harmony import */ var _AnimationOptions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../AnimationOptions.js */ \"./dist/browser/Options/Classes/AnimationOptions.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\n\nclass OpacityAnimation extends _AnimationOptions_js__WEBPACK_IMPORTED_MODULE_0__.RangedAnimationOptions {\n  constructor() {\n    super();\n    this.destroy = _Enums_Types_DestroyType_js__WEBPACK_IMPORTED_MODULE_1__.DestroyType.none;\n    this.speed = 2;\n  }\n  load(data) {\n    super.load(data);\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_2__.isNull)(data)) {\n      return;\n    }\n    if (data.destroy !== undefined) {\n      this.destroy = data.destroy;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Opacity/OpacityAnimation.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/ParticlesOptions.js":
/*!********************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/ParticlesOptions.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParticlesOptions: () => (/* binding */ ParticlesOptions)\n/* harmony export */ });\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _AnimatableColor_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../AnimatableColor.js */ \"./dist/browser/Options/Classes/AnimatableColor.js\");\n/* harmony import */ var _Collisions_Collisions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Collisions/Collisions.js */ \"./dist/browser/Options/Classes/Particles/Collisions/Collisions.js\");\n/* harmony import */ var _Effect_Effect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Effect/Effect.js */ \"./dist/browser/Options/Classes/Particles/Effect/Effect.js\");\n/* harmony import */ var _Move_Move_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Move/Move.js */ \"./dist/browser/Options/Classes/Particles/Move/Move.js\");\n/* harmony import */ var _Opacity_Opacity_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Opacity/Opacity.js */ \"./dist/browser/Options/Classes/Particles/Opacity/Opacity.js\");\n/* harmony import */ var _Bounce_ParticlesBounce_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Bounce/ParticlesBounce.js */ \"./dist/browser/Options/Classes/Particles/Bounce/ParticlesBounce.js\");\n/* harmony import */ var _Number_ParticlesNumber_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Number/ParticlesNumber.js */ \"./dist/browser/Options/Classes/Particles/Number/ParticlesNumber.js\");\n/* harmony import */ var _Shadow_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Shadow.js */ \"./dist/browser/Options/Classes/Particles/Shadow.js\");\n/* harmony import */ var _Shape_Shape_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Shape/Shape.js */ \"./dist/browser/Options/Classes/Particles/Shape/Shape.js\");\n/* harmony import */ var _Size_Size_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Size/Size.js */ \"./dist/browser/Options/Classes/Particles/Size/Size.js\");\n/* harmony import */ var _Stroke_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Stroke.js */ \"./dist/browser/Options/Classes/Particles/Stroke.js\");\n/* harmony import */ var _ZIndex_ZIndex_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ZIndex/ZIndex.js */ \"./dist/browser/Options/Classes/Particles/ZIndex/ZIndex.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nclass ParticlesOptions {\n  constructor(engine, container) {\n    this._engine = engine;\n    this._container = container;\n    this.bounce = new _Bounce_ParticlesBounce_js__WEBPACK_IMPORTED_MODULE_0__.ParticlesBounce();\n    this.collisions = new _Collisions_Collisions_js__WEBPACK_IMPORTED_MODULE_1__.Collisions();\n    this.color = new _AnimatableColor_js__WEBPACK_IMPORTED_MODULE_2__.AnimatableColor();\n    this.color.value = \"#fff\";\n    this.effect = new _Effect_Effect_js__WEBPACK_IMPORTED_MODULE_3__.Effect();\n    this.groups = {};\n    this.move = new _Move_Move_js__WEBPACK_IMPORTED_MODULE_4__.Move();\n    this.number = new _Number_ParticlesNumber_js__WEBPACK_IMPORTED_MODULE_5__.ParticlesNumber();\n    this.opacity = new _Opacity_Opacity_js__WEBPACK_IMPORTED_MODULE_6__.Opacity();\n    this.reduceDuplicates = false;\n    this.shadow = new _Shadow_js__WEBPACK_IMPORTED_MODULE_7__.Shadow();\n    this.shape = new _Shape_Shape_js__WEBPACK_IMPORTED_MODULE_8__.Shape();\n    this.size = new _Size_Size_js__WEBPACK_IMPORTED_MODULE_9__.Size();\n    this.stroke = new _Stroke_js__WEBPACK_IMPORTED_MODULE_10__.Stroke();\n    this.zIndex = new _ZIndex_ZIndex_js__WEBPACK_IMPORTED_MODULE_11__.ZIndex();\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_12__.isNull)(data)) {\n      return;\n    }\n    if (data.groups !== undefined) {\n      for (const group of Object.keys(data.groups)) {\n        if (!Object.hasOwn(data.groups, group)) {\n          continue;\n        }\n        const item = data.groups[group];\n        if (item !== undefined) {\n          this.groups[group] = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_13__.deepExtend)(this.groups[group] ?? {}, item);\n        }\n      }\n    }\n    if (data.reduceDuplicates !== undefined) {\n      this.reduceDuplicates = data.reduceDuplicates;\n    }\n    this.bounce.load(data.bounce);\n    this.color.load(_AnimatableColor_js__WEBPACK_IMPORTED_MODULE_2__.AnimatableColor.create(this.color, data.color));\n    this.effect.load(data.effect);\n    this.move.load(data.move);\n    this.number.load(data.number);\n    this.opacity.load(data.opacity);\n    this.shape.load(data.shape);\n    this.size.load(data.size);\n    this.shadow.load(data.shadow);\n    this.zIndex.load(data.zIndex);\n    this.collisions.load(data.collisions);\n    if (data.interactivity !== undefined) {\n      this.interactivity = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_13__.deepExtend)({}, data.interactivity);\n    }\n    const strokeToLoad = data.stroke;\n    if (strokeToLoad) {\n      this.stroke = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_13__.executeOnSingleOrMultiple)(strokeToLoad, t => {\n        const tmp = new _Stroke_js__WEBPACK_IMPORTED_MODULE_10__.Stroke();\n        tmp.load(t);\n        return tmp;\n      });\n    }\n    if (this._container) {\n      const updaters = this._engine.updaters.get(this._container);\n      if (updaters) {\n        for (const updater of updaters) {\n          if (updater.loadOptions) {\n            updater.loadOptions(this, data);\n          }\n        }\n      }\n      const interactors = this._engine.interactors.get(this._container);\n      if (interactors) {\n        for (const interactor of interactors) {\n          if (interactor.loadParticlesOptions) {\n            interactor.loadParticlesOptions(this, data);\n          }\n        }\n      }\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/ParticlesOptions.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Shadow.js":
/*!**********************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Shadow.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Shadow: () => (/* binding */ Shadow)\n/* harmony export */ });\n/* harmony import */ var _OptionsColor_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../OptionsColor.js */ \"./dist/browser/Options/Classes/OptionsColor.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass Shadow {\n  constructor() {\n    this.blur = 0;\n    this.color = new _OptionsColor_js__WEBPACK_IMPORTED_MODULE_0__.OptionsColor();\n    this.enable = false;\n    this.offset = {\n      x: 0,\n      y: 0\n    };\n    this.color.value = \"#000\";\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    if (data.blur !== undefined) {\n      this.blur = data.blur;\n    }\n    this.color = _OptionsColor_js__WEBPACK_IMPORTED_MODULE_0__.OptionsColor.create(this.color, data.color);\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.offset === undefined) {\n      return;\n    }\n    if (data.offset.x !== undefined) {\n      this.offset.x = data.offset.x;\n    }\n    if (data.offset.y !== undefined) {\n      this.offset.y = data.offset.y;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Shadow.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Shape/Shape.js":
/*!***************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Shape/Shape.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Shape: () => (/* binding */ Shape)\n/* harmony export */ });\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass Shape {\n  constructor() {\n    this.close = true;\n    this.fill = true;\n    this.options = {};\n    this.type = \"circle\";\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    const options = data.options;\n    if (options !== undefined) {\n      for (const shape in options) {\n        const item = options[shape];\n        if (item) {\n          this.options[shape] = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_1__.deepExtend)(this.options[shape] ?? {}, item);\n        }\n      }\n    }\n    if (data.close !== undefined) {\n      this.close = data.close;\n    }\n    if (data.fill !== undefined) {\n      this.fill = data.fill;\n    }\n    if (data.type !== undefined) {\n      this.type = data.type;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Shape/Shape.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Size/Size.js":
/*!*************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Size/Size.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Size: () => (/* binding */ Size)\n/* harmony export */ });\n/* harmony import */ var _ValueWithRandom_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../ValueWithRandom.js */ \"./dist/browser/Options/Classes/ValueWithRandom.js\");\n/* harmony import */ var _SizeAnimation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SizeAnimation.js */ \"./dist/browser/Options/Classes/Particles/Size/SizeAnimation.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\n\nclass Size extends _ValueWithRandom_js__WEBPACK_IMPORTED_MODULE_0__.RangedAnimationValueWithRandom {\n  constructor() {\n    super();\n    this.animation = new _SizeAnimation_js__WEBPACK_IMPORTED_MODULE_1__.SizeAnimation();\n    this.value = 3;\n  }\n  load(data) {\n    super.load(data);\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_2__.isNull)(data)) {\n      return;\n    }\n    const animation = data.animation;\n    if (animation !== undefined) {\n      this.animation.load(animation);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Size/Size.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Size/SizeAnimation.js":
/*!**********************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Size/SizeAnimation.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SizeAnimation: () => (/* binding */ SizeAnimation)\n/* harmony export */ });\n/* harmony import */ var _Enums_Types_DestroyType_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Enums/Types/DestroyType.js */ \"./dist/browser/Enums/Types/DestroyType.js\");\n/* harmony import */ var _AnimationOptions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../AnimationOptions.js */ \"./dist/browser/Options/Classes/AnimationOptions.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\n\nclass SizeAnimation extends _AnimationOptions_js__WEBPACK_IMPORTED_MODULE_0__.RangedAnimationOptions {\n  constructor() {\n    super();\n    this.destroy = _Enums_Types_DestroyType_js__WEBPACK_IMPORTED_MODULE_1__.DestroyType.none;\n    this.speed = 5;\n  }\n  load(data) {\n    super.load(data);\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_2__.isNull)(data)) {\n      return;\n    }\n    if (data.destroy !== undefined) {\n      this.destroy = data.destroy;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Size/SizeAnimation.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/Stroke.js":
/*!**********************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/Stroke.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Stroke: () => (/* binding */ Stroke)\n/* harmony export */ });\n/* harmony import */ var _AnimatableColor_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../AnimatableColor.js */ \"./dist/browser/Options/Classes/AnimatableColor.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n\n\n\nclass Stroke {\n  constructor() {\n    this.width = 0;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.color !== undefined) {\n      this.color = _AnimatableColor_js__WEBPACK_IMPORTED_MODULE_1__.AnimatableColor.create(this.color, data.color);\n    }\n    if (data.width !== undefined) {\n      this.width = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.setRangeValue)(data.width);\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.setRangeValue)(data.opacity);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/Stroke.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Particles/ZIndex/ZIndex.js":
/*!*****************************************************************!*\
  !*** ./dist/browser/Options/Classes/Particles/ZIndex/ZIndex.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ZIndex: () => (/* binding */ ZIndex)\n/* harmony export */ });\n/* harmony import */ var _ValueWithRandom_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../ValueWithRandom.js */ \"./dist/browser/Options/Classes/ValueWithRandom.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass ZIndex extends _ValueWithRandom_js__WEBPACK_IMPORTED_MODULE_0__.ValueWithRandom {\n  constructor() {\n    super();\n    this.opacityRate = 1;\n    this.sizeRate = 1;\n    this.velocityRate = 1;\n  }\n  load(data) {\n    super.load(data);\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    if (data.opacityRate !== undefined) {\n      this.opacityRate = data.opacityRate;\n    }\n    if (data.sizeRate !== undefined) {\n      this.sizeRate = data.sizeRate;\n    }\n    if (data.velocityRate !== undefined) {\n      this.velocityRate = data.velocityRate;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Particles/ZIndex/ZIndex.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Responsive.js":
/*!****************************************************!*\
  !*** ./dist/browser/Options/Classes/Responsive.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Responsive: () => (/* binding */ Responsive)\n/* harmony export */ });\n/* harmony import */ var _Enums_Modes_ResponsiveMode_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../Enums/Modes/ResponsiveMode.js */ \"./dist/browser/Enums/Modes/ResponsiveMode.js\");\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\n\nclass Responsive {\n  constructor() {\n    this.maxWidth = Infinity;\n    this.options = {};\n    this.mode = _Enums_Modes_ResponsiveMode_js__WEBPACK_IMPORTED_MODULE_0__.ResponsiveMode.canvas;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    if (!(0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data.maxWidth)) {\n      this.maxWidth = data.maxWidth;\n    }\n    if (!(0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data.mode)) {\n      if (data.mode === _Enums_Modes_ResponsiveMode_js__WEBPACK_IMPORTED_MODULE_0__.ResponsiveMode.screen) {\n        this.mode = _Enums_Modes_ResponsiveMode_js__WEBPACK_IMPORTED_MODULE_0__.ResponsiveMode.screen;\n      } else {\n        this.mode = _Enums_Modes_ResponsiveMode_js__WEBPACK_IMPORTED_MODULE_0__.ResponsiveMode.canvas;\n      }\n    }\n    if (!(0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data.options)) {\n      this.options = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_2__.deepExtend)({}, data.options);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Responsive.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Theme/Theme.js":
/*!*****************************************************!*\
  !*** ./dist/browser/Options/Classes/Theme/Theme.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Theme: () => (/* binding */ Theme)\n/* harmony export */ });\n/* harmony import */ var _ThemeDefault_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ThemeDefault.js */ \"./dist/browser/Options/Classes/Theme/ThemeDefault.js\");\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\n\nclass Theme {\n  constructor() {\n    this.name = \"\";\n    this.default = new _ThemeDefault_js__WEBPACK_IMPORTED_MODULE_0__.ThemeDefault();\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    if (data.name !== undefined) {\n      this.name = data.name;\n    }\n    this.default.load(data.default);\n    if (data.options !== undefined) {\n      this.options = (0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_2__.deepExtend)({}, data.options);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Theme/Theme.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Theme/ThemeDefault.js":
/*!************************************************************!*\
  !*** ./dist/browser/Options/Classes/Theme/ThemeDefault.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeDefault: () => (/* binding */ ThemeDefault)\n/* harmony export */ });\n/* harmony import */ var _Enums_Modes_ThemeMode_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../Enums/Modes/ThemeMode.js */ \"./dist/browser/Enums/Modes/ThemeMode.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\nclass ThemeDefault {\n  constructor() {\n    this.auto = false;\n    this.mode = _Enums_Modes_ThemeMode_js__WEBPACK_IMPORTED_MODULE_0__.ThemeMode.any;\n    this.value = false;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(data)) {\n      return;\n    }\n    if (data.auto !== undefined) {\n      this.auto = data.auto;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    if (data.value !== undefined) {\n      this.value = data.value;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/Theme/ThemeDefault.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/ValueWithRandom.js":
/*!*********************************************************!*\
  !*** ./dist/browser/Options/Classes/ValueWithRandom.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimationValueWithRandom: () => (/* binding */ AnimationValueWithRandom),\n/* harmony export */   RangedAnimationValueWithRandom: () => (/* binding */ RangedAnimationValueWithRandom),\n/* harmony export */   ValueWithRandom: () => (/* binding */ ValueWithRandom)\n/* harmony export */ });\n/* harmony import */ var _AnimationOptions_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AnimationOptions.js */ \"./dist/browser/Options/Classes/AnimationOptions.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n\n\n\nclass ValueWithRandom {\n  constructor() {\n    this.value = 0;\n  }\n  load(data) {\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (!(0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data.value)) {\n      this.value = (0,_Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_1__.setRangeValue)(data.value);\n    }\n  }\n}\nclass AnimationValueWithRandom extends ValueWithRandom {\n  constructor() {\n    super();\n    this.animation = new _AnimationOptions_js__WEBPACK_IMPORTED_MODULE_2__.AnimationOptions();\n  }\n  load(data) {\n    super.load(data);\n    if ((0,_Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    const animation = data.animation;\n    if (animation !== undefined) {\n      this.animation.load(animation);\n    }\n  }\n}\nclass RangedAnimationValueWithRandom extends AnimationValueWithRandom {\n  constructor() {\n    super();\n    this.animation = new _AnimationOptions_js__WEBPACK_IMPORTED_MODULE_2__.RangedAnimationOptions();\n  }\n  load(data) {\n    super.load(data);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Options/Classes/ValueWithRandom.js?");

/***/ }),

/***/ "./dist/browser/Types/RangeType.js":
/*!*****************************************!*\
  !*** ./dist/browser/Types/RangeType.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RangeType: () => (/* binding */ RangeType)\n/* harmony export */ });\nvar RangeType;\n(function (RangeType) {\n  RangeType[\"circle\"] = \"circle\";\n  RangeType[\"rectangle\"] = \"rectangle\";\n})(RangeType || (RangeType = {}));\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Types/RangeType.js?");

/***/ }),

/***/ "./dist/browser/Utils/CanvasUtils.js":
/*!*******************************************!*\
  !*** ./dist/browser/Utils/CanvasUtils.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alterHsl: () => (/* binding */ alterHsl),\n/* harmony export */   clear: () => (/* binding */ clear),\n/* harmony export */   drawEffect: () => (/* binding */ drawEffect),\n/* harmony export */   drawLine: () => (/* binding */ drawLine),\n/* harmony export */   drawParticle: () => (/* binding */ drawParticle),\n/* harmony export */   drawParticlePlugin: () => (/* binding */ drawParticlePlugin),\n/* harmony export */   drawPlugin: () => (/* binding */ drawPlugin),\n/* harmony export */   drawShape: () => (/* binding */ drawShape),\n/* harmony export */   drawShapeAfterDraw: () => (/* binding */ drawShapeAfterDraw),\n/* harmony export */   paintBase: () => (/* binding */ paintBase),\n/* harmony export */   paintImage: () => (/* binding */ paintImage)\n/* harmony export */ });\n/* harmony import */ var _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Core/Utils/Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n/* harmony import */ var _Enums_Types_AlterType_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Enums/Types/AlterType.js */ \"./dist/browser/Enums/Types/AlterType.js\");\n/* harmony import */ var _ColorUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ColorUtils.js */ \"./dist/browser/Utils/ColorUtils.js\");\n\n\n\nfunction drawLine(context, begin, end) {\n  context.beginPath();\n  context.moveTo(begin.x, begin.y);\n  context.lineTo(end.x, end.y);\n  context.closePath();\n}\nfunction paintBase(context, dimension, baseColor) {\n  context.fillStyle = baseColor ?? \"rgba(0,0,0,0)\";\n  context.fillRect(_Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.originPoint.x, _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.originPoint.y, dimension.width, dimension.height);\n}\nfunction paintImage(context, dimension, image, opacity) {\n  if (!image) {\n    return;\n  }\n  context.globalAlpha = opacity;\n  context.drawImage(image, _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.originPoint.x, _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.originPoint.y, dimension.width, dimension.height);\n  context.globalAlpha = 1;\n}\nfunction clear(context, dimension) {\n  context.clearRect(_Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.originPoint.x, _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.originPoint.y, dimension.width, dimension.height);\n}\nfunction drawParticle(data) {\n  const {\n      container,\n      context,\n      particle,\n      delta,\n      colorStyles,\n      backgroundMask,\n      composite,\n      radius,\n      opacity,\n      shadow,\n      transform\n    } = data,\n    pos = particle.getPosition(),\n    angle = particle.rotation + (particle.pathRotation ? particle.velocity.angle : _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultAngle),\n    rotateData = {\n      sin: Math.sin(angle),\n      cos: Math.cos(angle)\n    },\n    rotating = !!angle,\n    transformData = {\n      a: rotateData.cos * (transform.a ?? _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultTransform.a),\n      b: rotating ? rotateData.sin * (transform.b ?? _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.identity) : transform.b ?? _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultTransform.b,\n      c: rotating ? -rotateData.sin * (transform.c ?? _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.identity) : transform.c ?? _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultTransform.c,\n      d: rotateData.cos * (transform.d ?? _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultTransform.d)\n    };\n  context.setTransform(transformData.a, transformData.b, transformData.c, transformData.d, pos.x, pos.y);\n  if (backgroundMask) {\n    context.globalCompositeOperation = composite;\n  }\n  const shadowColor = particle.shadowColor;\n  if (shadow.enable && shadowColor) {\n    context.shadowBlur = shadow.blur;\n    context.shadowColor = (0,_ColorUtils_js__WEBPACK_IMPORTED_MODULE_1__.getStyleFromRgb)(shadowColor);\n    context.shadowOffsetX = shadow.offset.x;\n    context.shadowOffsetY = shadow.offset.y;\n  }\n  if (colorStyles.fill) {\n    context.fillStyle = colorStyles.fill;\n  }\n  const strokeWidth = particle.strokeWidth ?? _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minStrokeWidth;\n  context.lineWidth = strokeWidth;\n  if (colorStyles.stroke) {\n    context.strokeStyle = colorStyles.stroke;\n  }\n  const drawData = {\n    container,\n    context,\n    particle,\n    radius,\n    opacity,\n    delta,\n    transformData,\n    strokeWidth\n  };\n  drawShape(drawData);\n  drawShapeAfterDraw(drawData);\n  drawEffect(drawData);\n  context.globalCompositeOperation = \"source-over\";\n  context.resetTransform();\n}\nfunction drawEffect(data) {\n  const {\n    container,\n    context,\n    particle,\n    radius,\n    opacity,\n    delta,\n    transformData\n  } = data;\n  if (!particle.effect) {\n    return;\n  }\n  const drawer = container.effectDrawers.get(particle.effect);\n  if (!drawer) {\n    return;\n  }\n  drawer.draw({\n    context,\n    particle,\n    radius,\n    opacity,\n    delta,\n    pixelRatio: container.retina.pixelRatio,\n    transformData: {\n      ...transformData\n    }\n  });\n}\nfunction drawShape(data) {\n  const {\n    container,\n    context,\n    particle,\n    radius,\n    opacity,\n    delta,\n    strokeWidth,\n    transformData\n  } = data;\n  if (!particle.shape) {\n    return;\n  }\n  const drawer = container.shapeDrawers.get(particle.shape);\n  if (!drawer) {\n    return;\n  }\n  context.beginPath();\n  drawer.draw({\n    context,\n    particle,\n    radius,\n    opacity,\n    delta,\n    pixelRatio: container.retina.pixelRatio,\n    transformData: {\n      ...transformData\n    }\n  });\n  if (particle.shapeClose) {\n    context.closePath();\n  }\n  if (strokeWidth > _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minStrokeWidth) {\n    context.stroke();\n  }\n  if (particle.shapeFill) {\n    context.fill();\n  }\n}\nfunction drawShapeAfterDraw(data) {\n  const {\n    container,\n    context,\n    particle,\n    radius,\n    opacity,\n    delta,\n    transformData\n  } = data;\n  if (!particle.shape) {\n    return;\n  }\n  const drawer = container.shapeDrawers.get(particle.shape);\n  if (!drawer?.afterDraw) {\n    return;\n  }\n  drawer.afterDraw({\n    context,\n    particle,\n    radius,\n    opacity,\n    delta,\n    pixelRatio: container.retina.pixelRatio,\n    transformData: {\n      ...transformData\n    }\n  });\n}\nfunction drawPlugin(context, plugin, delta) {\n  if (!plugin.draw) {\n    return;\n  }\n  plugin.draw(context, delta);\n}\nfunction drawParticlePlugin(context, plugin, particle, delta) {\n  if (!plugin.drawParticle) {\n    return;\n  }\n  plugin.drawParticle(context, particle, delta);\n}\nfunction alterHsl(color, type, value) {\n  return {\n    h: color.h,\n    s: color.s,\n    l: color.l + (type === _Enums_Types_AlterType_js__WEBPACK_IMPORTED_MODULE_2__.AlterType.darken ? -_Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.lFactor : _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.lFactor) * value\n  };\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Utils/CanvasUtils.js?");

/***/ }),

/***/ "./dist/browser/Utils/ColorUtils.js":
/*!******************************************!*\
  !*** ./dist/browser/Utils/ColorUtils.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   colorMix: () => (/* binding */ colorMix),\n/* harmony export */   colorToHsl: () => (/* binding */ colorToHsl),\n/* harmony export */   colorToRgb: () => (/* binding */ colorToRgb),\n/* harmony export */   getHslAnimationFromHsl: () => (/* binding */ getHslAnimationFromHsl),\n/* harmony export */   getHslFromAnimation: () => (/* binding */ getHslFromAnimation),\n/* harmony export */   getLinkColor: () => (/* binding */ getLinkColor),\n/* harmony export */   getLinkRandomColor: () => (/* binding */ getLinkRandomColor),\n/* harmony export */   getRandomRgbColor: () => (/* binding */ getRandomRgbColor),\n/* harmony export */   getStyleFromHsl: () => (/* binding */ getStyleFromHsl),\n/* harmony export */   getStyleFromRgb: () => (/* binding */ getStyleFromRgb),\n/* harmony export */   hslToRgb: () => (/* binding */ hslToRgb),\n/* harmony export */   hslaToRgba: () => (/* binding */ hslaToRgba),\n/* harmony export */   rangeColorToHsl: () => (/* binding */ rangeColorToHsl),\n/* harmony export */   rangeColorToRgb: () => (/* binding */ rangeColorToRgb),\n/* harmony export */   rgbToHsl: () => (/* binding */ rgbToHsl),\n/* harmony export */   stringToAlpha: () => (/* binding */ stringToAlpha),\n/* harmony export */   stringToRgb: () => (/* binding */ stringToRgb),\n/* harmony export */   updateColor: () => (/* binding */ updateColor),\n/* harmony export */   updateColorValue: () => (/* binding */ updateColorValue)\n/* harmony export */ });\n/* harmony import */ var _NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n/* harmony import */ var _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Core/Utils/Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n/* harmony import */ var _TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Enums/AnimationStatus.js */ \"./dist/browser/Enums/AnimationStatus.js\");\n/* harmony import */ var _Utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils.js */ \"./dist/browser/Utils/Utils.js\");\n\n\n\n\n\nfunction stringToRgba(engine, input) {\n  if (!input) {\n    return;\n  }\n  for (const manager of engine.colorManagers.values()) {\n    if (input.startsWith(manager.stringPrefix)) {\n      return manager.parseString(input);\n    }\n  }\n}\nfunction rangeColorToRgb(engine, input, index, useIndex = true) {\n  if (!input) {\n    return;\n  }\n  const color = (0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isString)(input) ? {\n    value: input\n  } : input;\n  if ((0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isString)(color.value)) {\n    return colorToRgb(engine, color.value, index, useIndex);\n  }\n  if ((0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(color.value)) {\n    return rangeColorToRgb(engine, {\n      value: (0,_Utils_js__WEBPACK_IMPORTED_MODULE_1__.itemFromArray)(color.value, index, useIndex)\n    });\n  }\n  for (const manager of engine.colorManagers.values()) {\n    const res = manager.handleRangeColor(color);\n    if (res) {\n      return res;\n    }\n  }\n}\nfunction colorToRgb(engine, input, index, useIndex = true) {\n  if (!input) {\n    return;\n  }\n  const color = (0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isString)(input) ? {\n    value: input\n  } : input;\n  if ((0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isString)(color.value)) {\n    return color.value === _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.randomColorValue ? getRandomRgbColor() : stringToRgb(engine, color.value);\n  }\n  if ((0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(color.value)) {\n    return colorToRgb(engine, {\n      value: (0,_Utils_js__WEBPACK_IMPORTED_MODULE_1__.itemFromArray)(color.value, index, useIndex)\n    });\n  }\n  for (const manager of engine.colorManagers.values()) {\n    const res = manager.handleColor(color);\n    if (res) {\n      return res;\n    }\n  }\n}\nfunction colorToHsl(engine, color, index, useIndex = true) {\n  const rgb = colorToRgb(engine, color, index, useIndex);\n  return rgb ? rgbToHsl(rgb) : undefined;\n}\nfunction rangeColorToHsl(engine, color, index, useIndex = true) {\n  const rgb = rangeColorToRgb(engine, color, index, useIndex);\n  return rgb ? rgbToHsl(rgb) : undefined;\n}\nfunction rgbToHsl(color) {\n  const r1 = color.r / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.rgbMax,\n    g1 = color.g / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.rgbMax,\n    b1 = color.b / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.rgbMax,\n    max = Math.max(r1, g1, b1),\n    min = Math.min(r1, g1, b1),\n    res = {\n      h: _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.hMin,\n      l: (max + min) * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.half,\n      s: _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.sMin\n    };\n  if (max !== min) {\n    res.s = res.l < _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.half ? (max - min) / (max + min) : (max - min) / (_Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.double - max - min);\n    res.h = r1 === max ? (g1 - b1) / (max - min) : res.h = g1 === max ? _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.double + (b1 - r1) / (max - min) : _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.double * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.double + (r1 - g1) / (max - min);\n  }\n  res.l *= _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.lMax;\n  res.s *= _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.sMax;\n  res.h *= _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.hPhase;\n  if (res.h < _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.hMin) {\n    res.h += _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.hMax;\n  }\n  if (res.h >= _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.hMax) {\n    res.h -= _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.hMax;\n  }\n  return res;\n}\nfunction stringToAlpha(engine, input) {\n  return stringToRgba(engine, input)?.a;\n}\nfunction stringToRgb(engine, input) {\n  return stringToRgba(engine, input);\n}\nfunction hslToRgb(hsl) {\n  const h = (hsl.h % _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.hMax + _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.hMax) % _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.hMax,\n    s = Math.max(_Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.sMin, Math.min(_Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.sMax, hsl.s)),\n    l = Math.max(_Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.lMin, Math.min(_Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.lMax, hsl.l)),\n    hNormalized = h / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.hMax,\n    sNormalized = s / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.sMax,\n    lNormalized = l / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.lMax;\n  if (s === _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.sMin) {\n    const grayscaleValue = Math.round(lNormalized * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.rgbFactor);\n    return {\n      r: grayscaleValue,\n      g: grayscaleValue,\n      b: grayscaleValue\n    };\n  }\n  const channel = (temp1, temp2, temp3) => {\n      const temp3Min = 0,\n        temp3Max = 1;\n      if (temp3 < temp3Min) {\n        temp3++;\n      }\n      if (temp3 > temp3Max) {\n        temp3--;\n      }\n      if (temp3 * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.sextuple < temp3Max) {\n        return temp1 + (temp2 - temp1) * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.sextuple * temp3;\n      }\n      if (temp3 * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.double < temp3Max) {\n        return temp2;\n      }\n      if (temp3 * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.triple < temp3Max * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.double) {\n        const temp3Offset = _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.double / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.triple;\n        return temp1 + (temp2 - temp1) * (temp3Offset - temp3) * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.sextuple;\n      }\n      return temp1;\n    },\n    temp1 = lNormalized < _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.half ? lNormalized * (_Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.sNormalizedOffset + sNormalized) : lNormalized + sNormalized - lNormalized * sNormalized,\n    temp2 = _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.double * lNormalized - temp1,\n    phaseThird = _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.phaseNumerator / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.triple,\n    red = Math.min(_Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.rgbFactor, _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.rgbFactor * channel(temp2, temp1, hNormalized + phaseThird)),\n    green = Math.min(_Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.rgbFactor, _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.rgbFactor * channel(temp2, temp1, hNormalized)),\n    blue = Math.min(_Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.rgbFactor, _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.rgbFactor * channel(temp2, temp1, hNormalized - phaseThird));\n  return {\n    r: Math.round(red),\n    g: Math.round(green),\n    b: Math.round(blue)\n  };\n}\nfunction hslaToRgba(hsla) {\n  const rgbResult = hslToRgb(hsla);\n  return {\n    a: hsla.a,\n    b: rgbResult.b,\n    g: rgbResult.g,\n    r: rgbResult.r\n  };\n}\nfunction getRandomRgbColor(min) {\n  const fixedMin = min ?? _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.defaultRgbMin,\n    fixedMax = _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.rgbMax + _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.identity;\n  return {\n    b: Math.floor((0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.randomInRange)((0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.setRangeValue)(fixedMin, fixedMax))),\n    g: Math.floor((0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.randomInRange)((0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.setRangeValue)(fixedMin, fixedMax))),\n    r: Math.floor((0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.randomInRange)((0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.setRangeValue)(fixedMin, fixedMax)))\n  };\n}\nfunction getStyleFromRgb(color, opacity) {\n  return `rgba(${color.r}, ${color.g}, ${color.b}, ${opacity ?? _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.defaultOpacity})`;\n}\nfunction getStyleFromHsl(color, opacity) {\n  return `hsla(${color.h}, ${color.s}%, ${color.l}%, ${opacity ?? _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.defaultOpacity})`;\n}\nfunction colorMix(color1, color2, size1, size2) {\n  let rgb1 = color1,\n    rgb2 = color2;\n  if (rgb1.r === undefined) {\n    rgb1 = hslToRgb(color1);\n  }\n  if (rgb2.r === undefined) {\n    rgb2 = hslToRgb(color2);\n  }\n  return {\n    b: (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.mix)(rgb1.b, rgb2.b, size1, size2),\n    g: (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.mix)(rgb1.g, rgb2.g, size1, size2),\n    r: (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.mix)(rgb1.r, rgb2.r, size1, size2)\n  };\n}\nfunction getLinkColor(p1, p2, linkColor) {\n  if (linkColor === _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.randomColorValue) {\n    return getRandomRgbColor();\n  } else if (linkColor === _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.midColorValue) {\n    const sourceColor = p1.getFillColor() ?? p1.getStrokeColor(),\n      destColor = p2?.getFillColor() ?? p2?.getStrokeColor();\n    if (sourceColor && destColor && p2) {\n      return colorMix(sourceColor, destColor, p1.getRadius(), p2.getRadius());\n    } else {\n      const hslColor = sourceColor ?? destColor;\n      if (hslColor) {\n        return hslToRgb(hslColor);\n      }\n    }\n  } else {\n    return linkColor;\n  }\n}\nfunction getLinkRandomColor(engine, optColor, blink, consent) {\n  const color = (0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isString)(optColor) ? optColor : optColor.value;\n  if (color === _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.randomColorValue) {\n    if (consent) {\n      return rangeColorToRgb(engine, {\n        value: color\n      });\n    }\n    if (blink) {\n      return _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.randomColorValue;\n    }\n    return _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.midColorValue;\n  } else if (color === _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.midColorValue) {\n    return _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.midColorValue;\n  } else {\n    return rangeColorToRgb(engine, {\n      value: color\n    });\n  }\n}\nfunction getHslFromAnimation(animation) {\n  return animation !== undefined ? {\n    h: animation.h.value,\n    s: animation.s.value,\n    l: animation.l.value\n  } : undefined;\n}\nfunction getHslAnimationFromHsl(hsl, animationOptions, reduceFactor) {\n  const resColor = {\n    h: {\n      enable: false,\n      value: hsl.h\n    },\n    s: {\n      enable: false,\n      value: hsl.s\n    },\n    l: {\n      enable: false,\n      value: hsl.l\n    }\n  };\n  if (animationOptions) {\n    setColorAnimation(resColor.h, animationOptions.h, reduceFactor);\n    setColorAnimation(resColor.s, animationOptions.s, reduceFactor);\n    setColorAnimation(resColor.l, animationOptions.l, reduceFactor);\n  }\n  return resColor;\n}\nfunction setColorAnimation(colorValue, colorAnimation, reduceFactor) {\n  colorValue.enable = colorAnimation.enable;\n  if (colorValue.enable) {\n    colorValue.velocity = (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getRangeValue)(colorAnimation.speed) / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.percentDenominator * reduceFactor;\n    colorValue.decay = _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.decayOffset - (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getRangeValue)(colorAnimation.decay);\n    colorValue.status = _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_4__.AnimationStatus.increasing;\n    colorValue.loops = _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.defaultLoops;\n    colorValue.maxLoops = (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getRangeValue)(colorAnimation.count);\n    colorValue.time = _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.defaultTime;\n    colorValue.delayTime = (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getRangeValue)(colorAnimation.delay) * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.millisecondsToSeconds;\n    if (!colorAnimation.sync) {\n      colorValue.velocity *= (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getRandom)();\n      colorValue.value *= (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getRandom)();\n    }\n    colorValue.initialValue = colorValue.value;\n    colorValue.offset = (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.setRangeValue)(colorAnimation.offset);\n  } else {\n    colorValue.velocity = _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.defaultVelocity;\n  }\n}\nfunction updateColorValue(data, range, decrease, delta) {\n  const minLoops = 0,\n    minDelay = 0,\n    identity = 1,\n    minVelocity = 0,\n    minOffset = 0,\n    velocityFactor = 3.6;\n  if (!data || !data.enable || (data.maxLoops ?? minLoops) > minLoops && (data.loops ?? minLoops) > (data.maxLoops ?? minLoops)) {\n    return;\n  }\n  if (!data.time) {\n    data.time = 0;\n  }\n  if ((data.delayTime ?? minDelay) > minDelay && data.time < (data.delayTime ?? minDelay)) {\n    data.time += delta.value;\n  }\n  if ((data.delayTime ?? minDelay) > minDelay && data.time < (data.delayTime ?? minDelay)) {\n    return;\n  }\n  const offset = data.offset ? (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.randomInRange)(data.offset) : minOffset,\n    velocity = (data.velocity ?? minVelocity) * delta.factor + offset * velocityFactor,\n    decay = data.decay ?? identity,\n    max = (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getRangeMax)(range),\n    min = (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.getRangeMin)(range);\n  if (!decrease || data.status === _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_4__.AnimationStatus.increasing) {\n    data.value += velocity;\n    if (data.value > max) {\n      if (!data.loops) {\n        data.loops = 0;\n      }\n      data.loops++;\n      if (decrease) {\n        data.status = _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_4__.AnimationStatus.decreasing;\n      } else {\n        data.value -= max;\n      }\n    }\n  } else {\n    data.value -= velocity;\n    const minValue = 0;\n    if (data.value < minValue) {\n      if (!data.loops) {\n        data.loops = 0;\n      }\n      data.loops++;\n      data.status = _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_4__.AnimationStatus.increasing;\n    }\n  }\n  if (data.velocity && decay !== identity) {\n    data.velocity *= decay;\n  }\n  data.value = (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_3__.clamp)(data.value, min, max);\n}\nfunction updateColor(color, delta) {\n  if (!color) {\n    return;\n  }\n  const {\n      h,\n      s,\n      l\n    } = color,\n    ranges = {\n      h: {\n        min: 0,\n        max: 360\n      },\n      s: {\n        min: 0,\n        max: 100\n      },\n      l: {\n        min: 0,\n        max: 100\n      }\n    };\n  if (h) {\n    updateColorValue(h, ranges.h, false, delta);\n  }\n  if (s) {\n    updateColorValue(s, ranges.s, true, delta);\n  }\n  if (l) {\n    updateColorValue(l, ranges.l, true, delta);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Utils/ColorUtils.js?");

/***/ }),

/***/ "./dist/browser/Utils/EventDispatcher.js":
/*!***********************************************!*\
  !*** ./dist/browser/Utils/EventDispatcher.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventDispatcher: () => (/* binding */ EventDispatcher)\n/* harmony export */ });\n/* harmony import */ var _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Core/Utils/Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n\nclass EventDispatcher {\n  constructor() {\n    this._listeners = new Map();\n  }\n  addEventListener(type, listener) {\n    this.removeEventListener(type, listener);\n    let arr = this._listeners.get(type);\n    if (!arr) {\n      arr = [];\n      this._listeners.set(type, arr);\n    }\n    arr.push(listener);\n  }\n  dispatchEvent(type, args) {\n    const listeners = this._listeners.get(type);\n    listeners?.forEach(handler => handler(args));\n  }\n  hasEventListener(type) {\n    return !!this._listeners.get(type);\n  }\n  removeAllEventListeners(type) {\n    if (!type) {\n      this._listeners = new Map();\n    } else {\n      this._listeners.delete(type);\n    }\n  }\n  removeEventListener(type, listener) {\n    const arr = this._listeners.get(type);\n    if (!arr) {\n      return;\n    }\n    const length = arr.length,\n      idx = arr.indexOf(listener);\n    if (idx < _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minIndex) {\n      return;\n    }\n    if (length === _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.deleteCount) {\n      this._listeners.delete(type);\n    } else {\n      arr.splice(idx, _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.deleteCount);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Utils/EventDispatcher.js?");

/***/ }),

/***/ "./dist/browser/Utils/NumberUtils.js":
/*!*******************************************!*\
  !*** ./dist/browser/Utils/NumberUtils.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animate: () => (/* binding */ animate),\n/* harmony export */   calcExactPositionOrRandomFromSize: () => (/* binding */ calcExactPositionOrRandomFromSize),\n/* harmony export */   calcExactPositionOrRandomFromSizeRanged: () => (/* binding */ calcExactPositionOrRandomFromSizeRanged),\n/* harmony export */   calcPositionFromSize: () => (/* binding */ calcPositionFromSize),\n/* harmony export */   calcPositionOrRandomFromSize: () => (/* binding */ calcPositionOrRandomFromSize),\n/* harmony export */   calcPositionOrRandomFromSizeRanged: () => (/* binding */ calcPositionOrRandomFromSizeRanged),\n/* harmony export */   cancelAnimation: () => (/* binding */ cancelAnimation),\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   collisionVelocity: () => (/* binding */ collisionVelocity),\n/* harmony export */   degToRad: () => (/* binding */ degToRad),\n/* harmony export */   getDistance: () => (/* binding */ getDistance),\n/* harmony export */   getDistances: () => (/* binding */ getDistances),\n/* harmony export */   getParticleBaseVelocity: () => (/* binding */ getParticleBaseVelocity),\n/* harmony export */   getParticleDirectionAngle: () => (/* binding */ getParticleDirectionAngle),\n/* harmony export */   getRandom: () => (/* binding */ getRandom),\n/* harmony export */   getRangeMax: () => (/* binding */ getRangeMax),\n/* harmony export */   getRangeMin: () => (/* binding */ getRangeMin),\n/* harmony export */   getRangeValue: () => (/* binding */ getRangeValue),\n/* harmony export */   mix: () => (/* binding */ mix),\n/* harmony export */   parseAlpha: () => (/* binding */ parseAlpha),\n/* harmony export */   randomInRange: () => (/* binding */ randomInRange),\n/* harmony export */   setAnimationFunctions: () => (/* binding */ setAnimationFunctions),\n/* harmony export */   setRandom: () => (/* binding */ setRandom),\n/* harmony export */   setRangeValue: () => (/* binding */ setRangeValue)\n/* harmony export */ });\n/* harmony import */ var _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../Enums/Directions/MoveDirection.js */ \"./dist/browser/Enums/Directions/MoveDirection.js\");\n/* harmony import */ var _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Core/Utils/Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n/* harmony import */ var _Core_Utils_Vectors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../Core/Utils/Vectors.js */ \"./dist/browser/Core/Utils/Vectors.js\");\n/* harmony import */ var _TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\n\n\nlet _random = Math.random;\nconst _animationLoop = {\n  nextFrame: cb => requestAnimationFrame(cb),\n  cancel: idx => cancelAnimationFrame(idx)\n};\nfunction setRandom(rnd = Math.random) {\n  _random = rnd;\n}\nfunction getRandom() {\n  const min = 0,\n    max = 1;\n  return clamp(_random(), min, max - Number.EPSILON);\n}\nfunction setAnimationFunctions(nextFrame, cancel) {\n  _animationLoop.nextFrame = callback => nextFrame(callback);\n  _animationLoop.cancel = handle => cancel(handle);\n}\nfunction animate(fn) {\n  return _animationLoop.nextFrame(fn);\n}\nfunction cancelAnimation(handle) {\n  _animationLoop.cancel(handle);\n}\nfunction clamp(num, min, max) {\n  return Math.min(Math.max(num, min), max);\n}\nfunction mix(comp1, comp2, weight1, weight2) {\n  return Math.floor((comp1 * weight1 + comp2 * weight2) / (weight1 + weight2));\n}\nfunction randomInRange(r) {\n  const max = getRangeMax(r),\n    minOffset = 0;\n  let min = getRangeMin(r);\n  if (max === min) {\n    min = minOffset;\n  }\n  return getRandom() * (max - min) + min;\n}\nfunction getRangeValue(value) {\n  return (0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNumber)(value) ? value : randomInRange(value);\n}\nfunction getRangeMin(value) {\n  return (0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNumber)(value) ? value : value.min;\n}\nfunction getRangeMax(value) {\n  return (0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNumber)(value) ? value : value.max;\n}\nfunction setRangeValue(source, value) {\n  if (source === value || value === undefined && (0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNumber)(source)) {\n    return source;\n  }\n  const min = getRangeMin(source),\n    max = getRangeMax(source);\n  return value !== undefined ? {\n    min: Math.min(min, value),\n    max: Math.max(max, value)\n  } : setRangeValue(min, max);\n}\nfunction getDistances(pointA, pointB) {\n  const dx = pointA.x - pointB.x,\n    dy = pointA.y - pointB.y,\n    squareExp = 2;\n  return {\n    dx: dx,\n    dy: dy,\n    distance: Math.sqrt(dx ** squareExp + dy ** squareExp)\n  };\n}\nfunction getDistance(pointA, pointB) {\n  return getDistances(pointA, pointB).distance;\n}\nfunction degToRad(degrees) {\n  const PIDeg = 180;\n  return degrees * Math.PI / PIDeg;\n}\nfunction getParticleDirectionAngle(direction, position, center) {\n  if ((0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_0__.isNumber)(direction)) {\n    return degToRad(direction);\n  }\n  switch (direction) {\n    case _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_1__.MoveDirection.top:\n      return -Math.PI * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.half;\n    case _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_1__.MoveDirection.topRight:\n      return -Math.PI * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.quarter;\n    case _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_1__.MoveDirection.right:\n      return _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.empty;\n    case _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_1__.MoveDirection.bottomRight:\n      return Math.PI * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.quarter;\n    case _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_1__.MoveDirection.bottom:\n      return Math.PI * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.half;\n    case _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_1__.MoveDirection.bottomLeft:\n      return Math.PI * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.threeQuarter;\n    case _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_1__.MoveDirection.left:\n      return Math.PI;\n    case _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_1__.MoveDirection.topLeft:\n      return -Math.PI * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.threeQuarter;\n    case _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_1__.MoveDirection.inside:\n      return Math.atan2(center.y - position.y, center.x - position.x);\n    case _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_1__.MoveDirection.outside:\n      return Math.atan2(position.y - center.y, position.x - center.x);\n    default:\n      return getRandom() * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.doublePI;\n  }\n}\nfunction getParticleBaseVelocity(direction) {\n  const baseVelocity = _Core_Utils_Vectors_js__WEBPACK_IMPORTED_MODULE_3__.Vector.origin;\n  baseVelocity.length = 1;\n  baseVelocity.angle = direction;\n  return baseVelocity;\n}\nfunction collisionVelocity(v1, v2, m1, m2) {\n  return _Core_Utils_Vectors_js__WEBPACK_IMPORTED_MODULE_3__.Vector.create(v1.x * (m1 - m2) / (m1 + m2) + v2.x * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.double * m2 / (m1 + m2), v1.y);\n}\nfunction calcPositionFromSize(data) {\n  return data.position?.x !== undefined && data.position.y !== undefined ? {\n    x: data.position.x * data.size.width / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.percentDenominator,\n    y: data.position.y * data.size.height / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.percentDenominator\n  } : undefined;\n}\nfunction calcPositionOrRandomFromSize(data) {\n  return {\n    x: (data.position?.x ?? getRandom() * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.percentDenominator) * data.size.width / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.percentDenominator,\n    y: (data.position?.y ?? getRandom() * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.percentDenominator) * data.size.height / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.percentDenominator\n  };\n}\nfunction calcPositionOrRandomFromSizeRanged(data) {\n  const position = {\n    x: data.position?.x !== undefined ? getRangeValue(data.position.x) : undefined,\n    y: data.position?.y !== undefined ? getRangeValue(data.position.y) : undefined\n  };\n  return calcPositionOrRandomFromSize({\n    size: data.size,\n    position\n  });\n}\nfunction calcExactPositionOrRandomFromSize(data) {\n  return {\n    x: data.position?.x ?? getRandom() * data.size.width,\n    y: data.position?.y ?? getRandom() * data.size.height\n  };\n}\nfunction calcExactPositionOrRandomFromSizeRanged(data) {\n  const position = {\n    x: data.position?.x !== undefined ? getRangeValue(data.position.x) : undefined,\n    y: data.position?.y !== undefined ? getRangeValue(data.position.y) : undefined\n  };\n  return calcExactPositionOrRandomFromSize({\n    size: data.size,\n    position\n  });\n}\nfunction parseAlpha(input) {\n  const defaultAlpha = 1;\n  if (!input) {\n    return defaultAlpha;\n  }\n  return input.endsWith(\"%\") ? parseFloat(input) / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_2__.percentDenominator : parseFloat(input);\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Utils/NumberUtils.js?");

/***/ }),

/***/ "./dist/browser/Utils/OptionsUtils.js":
/*!********************************************!*\
  !*** ./dist/browser/Utils/OptionsUtils.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadOptions: () => (/* binding */ loadOptions),\n/* harmony export */   loadParticlesOptions: () => (/* binding */ loadParticlesOptions)\n/* harmony export */ });\n/* harmony import */ var _Options_Classes_Particles_ParticlesOptions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Options/Classes/Particles/ParticlesOptions.js */ \"./dist/browser/Options/Classes/Particles/ParticlesOptions.js\");\n\nfunction loadOptions(options, ...sourceOptionsArr) {\n  for (const sourceOptions of sourceOptionsArr) {\n    options.load(sourceOptions);\n  }\n}\nfunction loadParticlesOptions(engine, container, ...sourceOptionsArr) {\n  const options = new _Options_Classes_Particles_ParticlesOptions_js__WEBPACK_IMPORTED_MODULE_0__.ParticlesOptions(engine, container);\n  loadOptions(options, ...sourceOptionsArr);\n  return options;\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Utils/OptionsUtils.js?");

/***/ }),

/***/ "./dist/browser/Utils/TypeUtils.js":
/*!*****************************************!*\
  !*** ./dist/browser/Utils/TypeUtils.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isArray: () => (/* binding */ isArray),\n/* harmony export */   isBoolean: () => (/* binding */ isBoolean),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isNull: () => (/* binding */ isNull),\n/* harmony export */   isNumber: () => (/* binding */ isNumber),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isString: () => (/* binding */ isString)\n/* harmony export */ });\nfunction isBoolean(arg) {\n  return typeof arg === \"boolean\";\n}\nfunction isString(arg) {\n  return typeof arg === \"string\";\n}\nfunction isNumber(arg) {\n  return typeof arg === \"number\";\n}\nfunction isFunction(arg) {\n  return typeof arg === \"function\";\n}\nfunction isObject(arg) {\n  return typeof arg === \"object\" && arg !== null;\n}\nfunction isArray(arg) {\n  return Array.isArray(arg);\n}\nfunction isNull(arg) {\n  return arg === null || arg === undefined;\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Utils/TypeUtils.js?");

/***/ }),

/***/ "./dist/browser/Utils/Utils.js":
/*!*************************************!*\
  !*** ./dist/browser/Utils/Utils.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   areBoundsInside: () => (/* binding */ areBoundsInside),\n/* harmony export */   arrayRandomIndex: () => (/* binding */ arrayRandomIndex),\n/* harmony export */   calculateBounds: () => (/* binding */ calculateBounds),\n/* harmony export */   circleBounce: () => (/* binding */ circleBounce),\n/* harmony export */   circleBounceDataFromParticle: () => (/* binding */ circleBounceDataFromParticle),\n/* harmony export */   cloneStyle: () => (/* binding */ cloneStyle),\n/* harmony export */   deepExtend: () => (/* binding */ deepExtend),\n/* harmony export */   divMode: () => (/* binding */ divMode),\n/* harmony export */   divModeExecute: () => (/* binding */ divModeExecute),\n/* harmony export */   executeOnSingleOrMultiple: () => (/* binding */ executeOnSingleOrMultiple),\n/* harmony export */   findItemFromSingleOrMultiple: () => (/* binding */ findItemFromSingleOrMultiple),\n/* harmony export */   getFullScreenStyle: () => (/* binding */ getFullScreenStyle),\n/* harmony export */   getLogger: () => (/* binding */ getLogger),\n/* harmony export */   getPosition: () => (/* binding */ getPosition),\n/* harmony export */   getSize: () => (/* binding */ getSize),\n/* harmony export */   hasMatchMedia: () => (/* binding */ hasMatchMedia),\n/* harmony export */   initParticleNumericAnimationValue: () => (/* binding */ initParticleNumericAnimationValue),\n/* harmony export */   isDivModeEnabled: () => (/* binding */ isDivModeEnabled),\n/* harmony export */   isInArray: () => (/* binding */ isInArray),\n/* harmony export */   isPointInside: () => (/* binding */ isPointInside),\n/* harmony export */   isSsr: () => (/* binding */ isSsr),\n/* harmony export */   itemFromArray: () => (/* binding */ itemFromArray),\n/* harmony export */   itemFromSingleOrMultiple: () => (/* binding */ itemFromSingleOrMultiple),\n/* harmony export */   loadFont: () => (/* binding */ loadFont),\n/* harmony export */   rectBounce: () => (/* binding */ rectBounce),\n/* harmony export */   safeIntersectionObserver: () => (/* binding */ safeIntersectionObserver),\n/* harmony export */   safeMatchMedia: () => (/* binding */ safeMatchMedia),\n/* harmony export */   safeMutationObserver: () => (/* binding */ safeMutationObserver),\n/* harmony export */   setLogger: () => (/* binding */ setLogger),\n/* harmony export */   singleDivModeExecute: () => (/* binding */ singleDivModeExecute),\n/* harmony export */   updateAnimation: () => (/* binding */ updateAnimation)\n/* harmony export */ });\n/* harmony import */ var _NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n/* harmony import */ var _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Core/Utils/Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n/* harmony import */ var _TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n/* harmony import */ var _Enums_Modes_AnimationMode_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../Enums/Modes/AnimationMode.js */ \"./dist/browser/Enums/Modes/AnimationMode.js\");\n/* harmony import */ var _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../Enums/AnimationStatus.js */ \"./dist/browser/Enums/AnimationStatus.js\");\n/* harmony import */ var _Enums_Types_DestroyType_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../Enums/Types/DestroyType.js */ \"./dist/browser/Enums/Types/DestroyType.js\");\n/* harmony import */ var _Enums_Directions_OutModeDirection_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../Enums/Directions/OutModeDirection.js */ \"./dist/browser/Enums/Directions/OutModeDirection.js\");\n/* harmony import */ var _Enums_Modes_PixelMode_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../Enums/Modes/PixelMode.js */ \"./dist/browser/Enums/Modes/PixelMode.js\");\n/* harmony import */ var _Enums_Types_StartValueType_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../Enums/Types/StartValueType.js */ \"./dist/browser/Enums/Types/StartValueType.js\");\n/* harmony import */ var _Core_Utils_Vectors_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Core/Utils/Vectors.js */ \"./dist/browser/Core/Utils/Vectors.js\");\n\n\n\n\n\n\n\n\n\n\nconst _logger = {\n  debug: console.debug,\n  error: console.error,\n  info: console.info,\n  log: console.log,\n  verbose: console.log,\n  warning: console.warn\n};\nfunction setLogger(logger) {\n  _logger.debug = logger.debug || _logger.debug;\n  _logger.error = logger.error || _logger.error;\n  _logger.info = logger.info || _logger.info;\n  _logger.log = logger.log || _logger.log;\n  _logger.verbose = logger.verbose || _logger.verbose;\n  _logger.warning = logger.warning || _logger.warning;\n}\nfunction getLogger() {\n  return _logger;\n}\nfunction memoize(fn) {\n  const cache = new Map();\n  return (...args) => {\n    const key = JSON.stringify(args);\n    if (cache.has(key)) {\n      return cache.get(key);\n    }\n    const result = fn(...args);\n    cache.set(key, result);\n    return result;\n  };\n}\nfunction rectSideBounce(data) {\n  const res = {\n      bounced: false\n    },\n    {\n      pSide,\n      pOtherSide,\n      rectSide,\n      rectOtherSide,\n      velocity,\n      factor\n    } = data;\n  if (pOtherSide.min < rectOtherSide.min || pOtherSide.min > rectOtherSide.max || pOtherSide.max < rectOtherSide.min || pOtherSide.max > rectOtherSide.max) {\n    return res;\n  }\n  if (pSide.max >= rectSide.min && pSide.max <= (rectSide.max + rectSide.min) * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.half && velocity > _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minVelocity || pSide.min <= rectSide.max && pSide.min > (rectSide.max + rectSide.min) * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.half && velocity < _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minVelocity) {\n    res.velocity = velocity * -factor;\n    res.bounced = true;\n  }\n  return res;\n}\nfunction checkSelector(element, selectors) {\n  const res = executeOnSingleOrMultiple(selectors, selector => {\n    return element.matches(selector);\n  });\n  return (0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isArray)(res) ? res.some(t => t) : res;\n}\nfunction isSsr() {\n  return typeof window === \"undefined\" || !window || typeof window.document === \"undefined\" || !window.document;\n}\nfunction hasMatchMedia() {\n  return !isSsr() && typeof matchMedia !== \"undefined\";\n}\nfunction safeMatchMedia(query) {\n  if (!hasMatchMedia()) {\n    return;\n  }\n  return matchMedia(query);\n}\nfunction safeIntersectionObserver(callback) {\n  if (isSsr() || typeof IntersectionObserver === \"undefined\") {\n    return;\n  }\n  return new IntersectionObserver(callback);\n}\nfunction safeMutationObserver(callback) {\n  if (isSsr() || typeof MutationObserver === \"undefined\") {\n    return;\n  }\n  return new MutationObserver(callback);\n}\nfunction isInArray(value, array) {\n  const invalidIndex = -1;\n  return value === array || (0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isArray)(array) && array.indexOf(value) > invalidIndex;\n}\nasync function loadFont(font, weight) {\n  try {\n    await document.fonts.load(`${weight ?? \"400\"} 36px '${font ?? \"Verdana\"}'`);\n  } catch {}\n}\nfunction arrayRandomIndex(array) {\n  return Math.floor((0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRandom)() * array.length);\n}\nfunction itemFromArray(array, index, useIndex = true) {\n  return array[index !== undefined && useIndex ? index % array.length : arrayRandomIndex(array)];\n}\nfunction isPointInside(point, size, offset, radius, direction) {\n  const minRadius = 0;\n  return areBoundsInside(calculateBounds(point, radius ?? minRadius), size, offset, direction);\n}\nfunction areBoundsInside(bounds, size, offset, direction) {\n  let inside = true;\n  if (!direction || direction === _Enums_Directions_OutModeDirection_js__WEBPACK_IMPORTED_MODULE_3__.OutModeDirection.bottom) {\n    inside = bounds.top < size.height + offset.x;\n  }\n  if (inside && (!direction || direction === _Enums_Directions_OutModeDirection_js__WEBPACK_IMPORTED_MODULE_3__.OutModeDirection.left)) {\n    inside = bounds.right > offset.x;\n  }\n  if (inside && (!direction || direction === _Enums_Directions_OutModeDirection_js__WEBPACK_IMPORTED_MODULE_3__.OutModeDirection.right)) {\n    inside = bounds.left < size.width + offset.y;\n  }\n  if (inside && (!direction || direction === _Enums_Directions_OutModeDirection_js__WEBPACK_IMPORTED_MODULE_3__.OutModeDirection.top)) {\n    inside = bounds.bottom > offset.y;\n  }\n  return inside;\n}\nfunction calculateBounds(point, radius) {\n  return {\n    bottom: point.y + radius,\n    left: point.x - radius,\n    right: point.x + radius,\n    top: point.y - radius\n  };\n}\nfunction deepExtend(destination, ...sources) {\n  for (const source of sources) {\n    if (source === undefined || source === null) {\n      continue;\n    }\n    if (!(0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isObject)(source)) {\n      destination = source;\n      continue;\n    }\n    const sourceIsArray = Array.isArray(source);\n    if (sourceIsArray && ((0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isObject)(destination) || !destination || !Array.isArray(destination))) {\n      destination = [];\n    } else if (!sourceIsArray && ((0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isObject)(destination) || !destination || Array.isArray(destination))) {\n      destination = {};\n    }\n    for (const key in source) {\n      if (key === \"__proto__\") {\n        continue;\n      }\n      const sourceDict = source,\n        value = sourceDict[key],\n        destDict = destination;\n      destDict[key] = (0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isObject)(value) && Array.isArray(value) ? value.map(v => deepExtend(destDict[key], v)) : deepExtend(destDict[key], value);\n    }\n  }\n  return destination;\n}\nfunction isDivModeEnabled(mode, divs) {\n  return !!findItemFromSingleOrMultiple(divs, t => t.enable && isInArray(mode, t.mode));\n}\nfunction divModeExecute(mode, divs, callback) {\n  executeOnSingleOrMultiple(divs, div => {\n    const divMode = div.mode,\n      divEnabled = div.enable;\n    if (divEnabled && isInArray(mode, divMode)) {\n      singleDivModeExecute(div, callback);\n    }\n  });\n}\nfunction singleDivModeExecute(div, callback) {\n  const selectors = div.selectors;\n  executeOnSingleOrMultiple(selectors, selector => {\n    callback(selector, div);\n  });\n}\nfunction divMode(divs, element) {\n  if (!element || !divs) {\n    return;\n  }\n  return findItemFromSingleOrMultiple(divs, div => {\n    return checkSelector(element, div.selectors);\n  });\n}\nfunction circleBounceDataFromParticle(p) {\n  return {\n    position: p.getPosition(),\n    radius: p.getRadius(),\n    mass: p.getMass(),\n    velocity: p.velocity,\n    factor: _Core_Utils_Vectors_js__WEBPACK_IMPORTED_MODULE_4__.Vector.create((0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeValue)(p.options.bounce.horizontal.value), (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeValue)(p.options.bounce.vertical.value))\n  };\n}\nfunction circleBounce(p1, p2) {\n  const {\n      x: xVelocityDiff,\n      y: yVelocityDiff\n    } = p1.velocity.sub(p2.velocity),\n    [pos1, pos2] = [p1.position, p2.position],\n    {\n      dx: xDist,\n      dy: yDist\n    } = (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getDistances)(pos2, pos1),\n    minimumDistance = 0;\n  if (xVelocityDiff * xDist + yVelocityDiff * yDist < minimumDistance) {\n    return;\n  }\n  const angle = -Math.atan2(yDist, xDist),\n    m1 = p1.mass,\n    m2 = p2.mass,\n    u1 = p1.velocity.rotate(angle),\n    u2 = p2.velocity.rotate(angle),\n    v1 = (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.collisionVelocity)(u1, u2, m1, m2),\n    v2 = (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.collisionVelocity)(u2, u1, m1, m2),\n    vFinal1 = v1.rotate(-angle),\n    vFinal2 = v2.rotate(-angle);\n  p1.velocity.x = vFinal1.x * p1.factor.x;\n  p1.velocity.y = vFinal1.y * p1.factor.y;\n  p2.velocity.x = vFinal2.x * p2.factor.x;\n  p2.velocity.y = vFinal2.y * p2.factor.y;\n}\nfunction rectBounce(particle, divBounds) {\n  const pPos = particle.getPosition(),\n    size = particle.getRadius(),\n    bounds = calculateBounds(pPos, size),\n    bounceOptions = particle.options.bounce,\n    resH = rectSideBounce({\n      pSide: {\n        min: bounds.left,\n        max: bounds.right\n      },\n      pOtherSide: {\n        min: bounds.top,\n        max: bounds.bottom\n      },\n      rectSide: {\n        min: divBounds.left,\n        max: divBounds.right\n      },\n      rectOtherSide: {\n        min: divBounds.top,\n        max: divBounds.bottom\n      },\n      velocity: particle.velocity.x,\n      factor: (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeValue)(bounceOptions.horizontal.value)\n    });\n  if (resH.bounced) {\n    if (resH.velocity !== undefined) {\n      particle.velocity.x = resH.velocity;\n    }\n    if (resH.position !== undefined) {\n      particle.position.x = resH.position;\n    }\n  }\n  const resV = rectSideBounce({\n    pSide: {\n      min: bounds.top,\n      max: bounds.bottom\n    },\n    pOtherSide: {\n      min: bounds.left,\n      max: bounds.right\n    },\n    rectSide: {\n      min: divBounds.top,\n      max: divBounds.bottom\n    },\n    rectOtherSide: {\n      min: divBounds.left,\n      max: divBounds.right\n    },\n    velocity: particle.velocity.y,\n    factor: (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeValue)(bounceOptions.vertical.value)\n  });\n  if (resV.bounced) {\n    if (resV.velocity !== undefined) {\n      particle.velocity.y = resV.velocity;\n    }\n    if (resV.position !== undefined) {\n      particle.position.y = resV.position;\n    }\n  }\n}\nfunction executeOnSingleOrMultiple(obj, callback) {\n  const defaultIndex = 0;\n  return (0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isArray)(obj) ? obj.map((item, index) => callback(item, index)) : callback(obj, defaultIndex);\n}\nfunction itemFromSingleOrMultiple(obj, index, useIndex) {\n  return (0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isArray)(obj) ? itemFromArray(obj, index, useIndex) : obj;\n}\nfunction findItemFromSingleOrMultiple(obj, callback) {\n  if ((0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isArray)(obj)) {\n    return obj.find((t, index) => callback(t, index));\n  }\n  const defaultIndex = 0;\n  return callback(obj, defaultIndex) ? obj : undefined;\n}\nfunction initParticleNumericAnimationValue(options, pxRatio) {\n  const valueRange = options.value,\n    animationOptions = options.animation,\n    res = {\n      delayTime: (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeValue)(animationOptions.delay) * _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds,\n      enable: animationOptions.enable,\n      value: (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeValue)(options.value) * pxRatio,\n      max: (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeMax)(valueRange) * pxRatio,\n      min: (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeMin)(valueRange) * pxRatio,\n      loops: 0,\n      maxLoops: (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeValue)(animationOptions.count),\n      time: 0\n    },\n    decayOffset = 1;\n  if (animationOptions.enable) {\n    res.decay = decayOffset - (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRangeValue)(animationOptions.decay);\n    switch (animationOptions.mode) {\n      case _Enums_Modes_AnimationMode_js__WEBPACK_IMPORTED_MODULE_5__.AnimationMode.increase:\n        res.status = _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_6__.AnimationStatus.increasing;\n        break;\n      case _Enums_Modes_AnimationMode_js__WEBPACK_IMPORTED_MODULE_5__.AnimationMode.decrease:\n        res.status = _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_6__.AnimationStatus.decreasing;\n        break;\n      case _Enums_Modes_AnimationMode_js__WEBPACK_IMPORTED_MODULE_5__.AnimationMode.random:\n        res.status = (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRandom)() >= _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.half ? _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_6__.AnimationStatus.increasing : _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_6__.AnimationStatus.decreasing;\n        break;\n    }\n    const autoStatus = animationOptions.mode === _Enums_Modes_AnimationMode_js__WEBPACK_IMPORTED_MODULE_5__.AnimationMode.auto;\n    switch (animationOptions.startValue) {\n      case _Enums_Types_StartValueType_js__WEBPACK_IMPORTED_MODULE_7__.StartValueType.min:\n        res.value = res.min;\n        if (autoStatus) {\n          res.status = _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_6__.AnimationStatus.increasing;\n        }\n        break;\n      case _Enums_Types_StartValueType_js__WEBPACK_IMPORTED_MODULE_7__.StartValueType.max:\n        res.value = res.max;\n        if (autoStatus) {\n          res.status = _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_6__.AnimationStatus.decreasing;\n        }\n        break;\n      case _Enums_Types_StartValueType_js__WEBPACK_IMPORTED_MODULE_7__.StartValueType.random:\n      default:\n        res.value = (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.randomInRange)(res);\n        if (autoStatus) {\n          res.status = (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.getRandom)() >= _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.half ? _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_6__.AnimationStatus.increasing : _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_6__.AnimationStatus.decreasing;\n        }\n        break;\n    }\n  }\n  res.initialValue = res.value;\n  return res;\n}\nfunction getPositionOrSize(positionOrSize, canvasSize) {\n  const isPercent = positionOrSize.mode === _Enums_Modes_PixelMode_js__WEBPACK_IMPORTED_MODULE_8__.PixelMode.percent;\n  if (!isPercent) {\n    const {\n      mode: _,\n      ...rest\n    } = positionOrSize;\n    return rest;\n  }\n  const isPosition = \"x\" in positionOrSize;\n  if (isPosition) {\n    return {\n      x: positionOrSize.x / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.percentDenominator * canvasSize.width,\n      y: positionOrSize.y / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.percentDenominator * canvasSize.height\n    };\n  } else {\n    return {\n      width: positionOrSize.width / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.percentDenominator * canvasSize.width,\n      height: positionOrSize.height / _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.percentDenominator * canvasSize.height\n    };\n  }\n}\nfunction getPosition(position, canvasSize) {\n  return getPositionOrSize(position, canvasSize);\n}\nfunction getSize(size, canvasSize) {\n  return getPositionOrSize(size, canvasSize);\n}\nfunction checkDestroy(particle, destroyType, value, minValue, maxValue) {\n  switch (destroyType) {\n    case _Enums_Types_DestroyType_js__WEBPACK_IMPORTED_MODULE_9__.DestroyType.max:\n      if (value >= maxValue) {\n        particle.destroy();\n      }\n      break;\n    case _Enums_Types_DestroyType_js__WEBPACK_IMPORTED_MODULE_9__.DestroyType.min:\n      if (value <= minValue) {\n        particle.destroy();\n      }\n      break;\n  }\n}\nfunction updateAnimation(particle, data, changeDirection, destroyType, delta) {\n  const minLoops = 0,\n    minDelay = 0,\n    identity = 1,\n    minVelocity = 0,\n    minDecay = 1;\n  if (particle.destroyed || !data || !data.enable || (data.maxLoops ?? minLoops) > minLoops && (data.loops ?? minLoops) > (data.maxLoops ?? minLoops)) {\n    return;\n  }\n  const velocity = (data.velocity ?? minVelocity) * delta.factor,\n    minValue = data.min,\n    maxValue = data.max,\n    decay = data.decay ?? minDecay;\n  if (!data.time) {\n    data.time = 0;\n  }\n  if ((data.delayTime ?? minDelay) > minDelay && data.time < (data.delayTime ?? minDelay)) {\n    data.time += delta.value;\n  }\n  if ((data.delayTime ?? minDelay) > minDelay && data.time < (data.delayTime ?? minDelay)) {\n    return;\n  }\n  switch (data.status) {\n    case _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_6__.AnimationStatus.increasing:\n      if (data.value >= maxValue) {\n        if (changeDirection) {\n          data.status = _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_6__.AnimationStatus.decreasing;\n        } else {\n          data.value -= maxValue;\n        }\n        if (!data.loops) {\n          data.loops = minLoops;\n        }\n        data.loops++;\n      } else {\n        data.value += velocity;\n      }\n      break;\n    case _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_6__.AnimationStatus.decreasing:\n      if (data.value <= minValue) {\n        if (changeDirection) {\n          data.status = _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_6__.AnimationStatus.increasing;\n        } else {\n          data.value += maxValue;\n        }\n        if (!data.loops) {\n          data.loops = minLoops;\n        }\n        data.loops++;\n      } else {\n        data.value -= velocity;\n      }\n  }\n  if (data.velocity && decay !== identity) {\n    data.velocity *= decay;\n  }\n  checkDestroy(particle, destroyType, data.value, minValue, maxValue);\n  if (!particle.destroyed) {\n    data.value = (0,_NumberUtils_js__WEBPACK_IMPORTED_MODULE_2__.clamp)(data.value, minValue, maxValue);\n  }\n}\nfunction cloneStyle(style) {\n  const clonedStyle = document.createElement(\"div\").style;\n  if (!style) {\n    return clonedStyle;\n  }\n  for (const key in style) {\n    const styleKey = style[key];\n    if (!Object.prototype.hasOwnProperty.call(style, key) || (0,_TypeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isNull)(styleKey)) {\n      continue;\n    }\n    const styleValue = style.getPropertyValue?.(styleKey);\n    if (!styleValue) {\n      continue;\n    }\n    const stylePriority = style.getPropertyPriority?.(styleKey);\n    if (!stylePriority) {\n      clonedStyle.setProperty?.(styleKey, styleValue);\n    } else {\n      clonedStyle.setProperty?.(styleKey, styleValue, stylePriority);\n    }\n  }\n  return clonedStyle;\n}\nfunction computeFullScreenStyle(zIndex) {\n  const fullScreenStyle = document.createElement(\"div\").style,\n    radix = 10,\n    style = {\n      width: \"100%\",\n      height: \"100%\",\n      margin: \"0\",\n      padding: \"0\",\n      borderWidth: \"0\",\n      position: \"fixed\",\n      zIndex: zIndex.toString(radix),\n      \"z-index\": zIndex.toString(radix),\n      top: \"0\",\n      left: \"0\"\n    };\n  for (const key in style) {\n    const value = style[key];\n    fullScreenStyle.setProperty(key, value);\n  }\n  return fullScreenStyle;\n}\nconst getFullScreenStyle = memoize(computeFullScreenStyle);\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/Utils/Utils.js?");

/***/ }),

/***/ "./dist/browser/exports.js":
/*!*********************************!*\
  !*** ./dist/browser/exports.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlterType: () => (/* reexport safe */ _Enums_Types_AlterType_js__WEBPACK_IMPORTED_MODULE_16__.AlterType),\n/* harmony export */   AnimatableColor: () => (/* reexport safe */ _Options_Classes_AnimatableColor_js__WEBPACK_IMPORTED_MODULE_27__.AnimatableColor),\n/* harmony export */   AnimationMode: () => (/* reexport safe */ _Enums_Modes_AnimationMode_js__WEBPACK_IMPORTED_MODULE_9__.AnimationMode),\n/* harmony export */   AnimationOptions: () => (/* reexport safe */ _Options_Classes_AnimationOptions_js__WEBPACK_IMPORTED_MODULE_28__.AnimationOptions),\n/* harmony export */   AnimationStatus: () => (/* reexport safe */ _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_25__.AnimationStatus),\n/* harmony export */   AnimationValueWithRandom: () => (/* reexport safe */ _Options_Classes_ValueWithRandom_js__WEBPACK_IMPORTED_MODULE_75__.AnimationValueWithRandom),\n/* harmony export */   Background: () => (/* reexport safe */ _Options_Classes_Background_Background_js__WEBPACK_IMPORTED_MODULE_29__.Background),\n/* harmony export */   BackgroundMask: () => (/* reexport safe */ _Options_Classes_BackgroundMask_BackgroundMask_js__WEBPACK_IMPORTED_MODULE_30__.BackgroundMask),\n/* harmony export */   BackgroundMaskCover: () => (/* reexport safe */ _Options_Classes_BackgroundMask_BackgroundMaskCover_js__WEBPACK_IMPORTED_MODULE_31__.BackgroundMaskCover),\n/* harmony export */   BaseRange: () => (/* reexport safe */ _Core_Utils_Ranges_js__WEBPACK_IMPORTED_MODULE_4__.BaseRange),\n/* harmony export */   Circle: () => (/* reexport safe */ _Core_Utils_Ranges_js__WEBPACK_IMPORTED_MODULE_4__.Circle),\n/* harmony export */   ClickEvent: () => (/* reexport safe */ _Options_Classes_Interactivity_Events_ClickEvent_js__WEBPACK_IMPORTED_MODULE_35__.ClickEvent),\n/* harmony export */   CollisionMode: () => (/* reexport safe */ _Enums_Modes_CollisionMode_js__WEBPACK_IMPORTED_MODULE_10__.CollisionMode),\n/* harmony export */   Collisions: () => (/* reexport safe */ _Options_Classes_Particles_Collisions_Collisions_js__WEBPACK_IMPORTED_MODULE_48__.Collisions),\n/* harmony export */   CollisionsAbsorb: () => (/* reexport safe */ _Options_Classes_Particles_Collisions_CollisionsAbsorb_js__WEBPACK_IMPORTED_MODULE_49__.CollisionsAbsorb),\n/* harmony export */   CollisionsOverlap: () => (/* reexport safe */ _Options_Classes_Particles_Collisions_CollisionsOverlap_js__WEBPACK_IMPORTED_MODULE_50__.CollisionsOverlap),\n/* harmony export */   ColorAnimation: () => (/* reexport safe */ _Options_Classes_ColorAnimation_js__WEBPACK_IMPORTED_MODULE_32__.ColorAnimation),\n/* harmony export */   DestroyType: () => (/* reexport safe */ _Enums_Types_DestroyType_js__WEBPACK_IMPORTED_MODULE_17__.DestroyType),\n/* harmony export */   DivEvent: () => (/* reexport safe */ _Options_Classes_Interactivity_Events_DivEvent_js__WEBPACK_IMPORTED_MODULE_36__.DivEvent),\n/* harmony export */   DivType: () => (/* reexport safe */ _Enums_Types_DivType_js__WEBPACK_IMPORTED_MODULE_22__.DivType),\n/* harmony export */   EasingType: () => (/* reexport safe */ _Enums_Types_EasingType_js__WEBPACK_IMPORTED_MODULE_23__.EasingType),\n/* harmony export */   EventType: () => (/* reexport safe */ _Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_24__.EventType),\n/* harmony export */   Events: () => (/* reexport safe */ _Options_Classes_Interactivity_Events_Events_js__WEBPACK_IMPORTED_MODULE_37__.Events),\n/* harmony export */   ExternalInteractorBase: () => (/* reexport safe */ _Core_Utils_ExternalInteractorBase_js__WEBPACK_IMPORTED_MODULE_1__.ExternalInteractorBase),\n/* harmony export */   FullScreen: () => (/* reexport safe */ _Options_Classes_FullScreen_FullScreen_js__WEBPACK_IMPORTED_MODULE_33__.FullScreen),\n/* harmony export */   GradientType: () => (/* reexport safe */ _Enums_Types_GradientType_js__WEBPACK_IMPORTED_MODULE_18__.GradientType),\n/* harmony export */   HoverEvent: () => (/* reexport safe */ _Options_Classes_Interactivity_Events_HoverEvent_js__WEBPACK_IMPORTED_MODULE_38__.HoverEvent),\n/* harmony export */   HslAnimation: () => (/* reexport safe */ _Options_Classes_HslAnimation_js__WEBPACK_IMPORTED_MODULE_34__.HslAnimation),\n/* harmony export */   Interactivity: () => (/* reexport safe */ _Options_Classes_Interactivity_Interactivity_js__WEBPACK_IMPORTED_MODULE_41__.Interactivity),\n/* harmony export */   InteractivityDetect: () => (/* reexport safe */ _Enums_InteractivityDetect_js__WEBPACK_IMPORTED_MODULE_26__.InteractivityDetect),\n/* harmony export */   InteractorType: () => (/* reexport safe */ _Enums_Types_InteractorType_js__WEBPACK_IMPORTED_MODULE_19__.InteractorType),\n/* harmony export */   LimitMode: () => (/* reexport safe */ _Enums_Modes_LimitMode_js__WEBPACK_IMPORTED_MODULE_11__.LimitMode),\n/* harmony export */   ManualParticle: () => (/* reexport safe */ _Options_Classes_ManualParticle_js__WEBPACK_IMPORTED_MODULE_43__.ManualParticle),\n/* harmony export */   Modes: () => (/* reexport safe */ _Options_Classes_Interactivity_Modes_Modes_js__WEBPACK_IMPORTED_MODULE_42__.Modes),\n/* harmony export */   Move: () => (/* reexport safe */ _Options_Classes_Particles_Move_Move_js__WEBPACK_IMPORTED_MODULE_55__.Move),\n/* harmony export */   MoveAngle: () => (/* reexport safe */ _Options_Classes_Particles_Move_MoveAngle_js__WEBPACK_IMPORTED_MODULE_56__.MoveAngle),\n/* harmony export */   MoveAttract: () => (/* reexport safe */ _Options_Classes_Particles_Move_MoveAttract_js__WEBPACK_IMPORTED_MODULE_54__.MoveAttract),\n/* harmony export */   MoveCenter: () => (/* reexport safe */ _Options_Classes_Particles_Move_MoveCenter_js__WEBPACK_IMPORTED_MODULE_57__.MoveCenter),\n/* harmony export */   MoveDirection: () => (/* reexport safe */ _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_6__.MoveDirection),\n/* harmony export */   MoveGravity: () => (/* reexport safe */ _Options_Classes_Particles_Move_MoveGravity_js__WEBPACK_IMPORTED_MODULE_58__.MoveGravity),\n/* harmony export */   MovePath: () => (/* reexport safe */ _Options_Classes_Particles_Move_Path_MovePath_js__WEBPACK_IMPORTED_MODULE_60__.MovePath),\n/* harmony export */   MoveTrail: () => (/* reexport safe */ _Options_Classes_Particles_Move_MoveTrail_js__WEBPACK_IMPORTED_MODULE_62__.MoveTrail),\n/* harmony export */   Opacity: () => (/* reexport safe */ _Options_Classes_Particles_Opacity_Opacity_js__WEBPACK_IMPORTED_MODULE_66__.Opacity),\n/* harmony export */   OpacityAnimation: () => (/* reexport safe */ _Options_Classes_Particles_Opacity_OpacityAnimation_js__WEBPACK_IMPORTED_MODULE_67__.OpacityAnimation),\n/* harmony export */   Options: () => (/* reexport safe */ _Options_Classes_Options_js__WEBPACK_IMPORTED_MODULE_44__.Options),\n/* harmony export */   OptionsColor: () => (/* reexport safe */ _Options_Classes_OptionsColor_js__WEBPACK_IMPORTED_MODULE_45__.OptionsColor),\n/* harmony export */   OutMode: () => (/* reexport safe */ _Enums_Modes_OutMode_js__WEBPACK_IMPORTED_MODULE_12__.OutMode),\n/* harmony export */   OutModeDirection: () => (/* reexport safe */ _Enums_Directions_OutModeDirection_js__WEBPACK_IMPORTED_MODULE_8__.OutModeDirection),\n/* harmony export */   OutModes: () => (/* reexport safe */ _Options_Classes_Particles_Move_OutModes_js__WEBPACK_IMPORTED_MODULE_59__.OutModes),\n/* harmony export */   Parallax: () => (/* reexport safe */ _Options_Classes_Interactivity_Events_Parallax_js__WEBPACK_IMPORTED_MODULE_39__.Parallax),\n/* harmony export */   ParticleOutType: () => (/* reexport safe */ _Enums_Types_ParticleOutType_js__WEBPACK_IMPORTED_MODULE_20__.ParticleOutType),\n/* harmony export */   ParticlesBounce: () => (/* reexport safe */ _Options_Classes_Particles_Bounce_ParticlesBounce_js__WEBPACK_IMPORTED_MODULE_46__.ParticlesBounce),\n/* harmony export */   ParticlesBounceFactor: () => (/* reexport safe */ _Options_Classes_Particles_Bounce_ParticlesBounceFactor_js__WEBPACK_IMPORTED_MODULE_47__.ParticlesBounceFactor),\n/* harmony export */   ParticlesDensity: () => (/* reexport safe */ _Options_Classes_Particles_Number_ParticlesDensity_js__WEBPACK_IMPORTED_MODULE_65__.ParticlesDensity),\n/* harmony export */   ParticlesInteractorBase: () => (/* reexport safe */ _Core_Utils_ParticlesInteractorBase_js__WEBPACK_IMPORTED_MODULE_2__.ParticlesInteractorBase),\n/* harmony export */   ParticlesNumber: () => (/* reexport safe */ _Options_Classes_Particles_Number_ParticlesNumber_js__WEBPACK_IMPORTED_MODULE_63__.ParticlesNumber),\n/* harmony export */   ParticlesNumberLimit: () => (/* reexport safe */ _Options_Classes_Particles_Number_ParticlesNumberLimit_js__WEBPACK_IMPORTED_MODULE_64__.ParticlesNumberLimit),\n/* harmony export */   ParticlesOptions: () => (/* reexport safe */ _Options_Classes_Particles_ParticlesOptions_js__WEBPACK_IMPORTED_MODULE_51__.ParticlesOptions),\n/* harmony export */   PixelMode: () => (/* reexport safe */ _Enums_Modes_PixelMode_js__WEBPACK_IMPORTED_MODULE_13__.PixelMode),\n/* harmony export */   Point: () => (/* reexport safe */ _Core_Utils_Point_js__WEBPACK_IMPORTED_MODULE_3__.Point),\n/* harmony export */   RangedAnimationOptions: () => (/* reexport safe */ _Options_Classes_AnimationOptions_js__WEBPACK_IMPORTED_MODULE_28__.RangedAnimationOptions),\n/* harmony export */   RangedAnimationValueWithRandom: () => (/* reexport safe */ _Options_Classes_ValueWithRandom_js__WEBPACK_IMPORTED_MODULE_75__.RangedAnimationValueWithRandom),\n/* harmony export */   Rectangle: () => (/* reexport safe */ _Core_Utils_Ranges_js__WEBPACK_IMPORTED_MODULE_4__.Rectangle),\n/* harmony export */   ResizeEvent: () => (/* reexport safe */ _Options_Classes_Interactivity_Events_ResizeEvent_js__WEBPACK_IMPORTED_MODULE_40__.ResizeEvent),\n/* harmony export */   Responsive: () => (/* reexport safe */ _Options_Classes_Responsive_js__WEBPACK_IMPORTED_MODULE_72__.Responsive),\n/* harmony export */   ResponsiveMode: () => (/* reexport safe */ _Enums_Modes_ResponsiveMode_js__WEBPACK_IMPORTED_MODULE_15__.ResponsiveMode),\n/* harmony export */   RotateDirection: () => (/* reexport safe */ _Enums_Directions_RotateDirection_js__WEBPACK_IMPORTED_MODULE_7__.RotateDirection),\n/* harmony export */   Shadow: () => (/* reexport safe */ _Options_Classes_Particles_Shadow_js__WEBPACK_IMPORTED_MODULE_52__.Shadow),\n/* harmony export */   Shape: () => (/* reexport safe */ _Options_Classes_Particles_Shape_Shape_js__WEBPACK_IMPORTED_MODULE_68__.Shape),\n/* harmony export */   Size: () => (/* reexport safe */ _Options_Classes_Particles_Size_Size_js__WEBPACK_IMPORTED_MODULE_69__.Size),\n/* harmony export */   SizeAnimation: () => (/* reexport safe */ _Options_Classes_Particles_Size_SizeAnimation_js__WEBPACK_IMPORTED_MODULE_70__.SizeAnimation),\n/* harmony export */   Spin: () => (/* reexport safe */ _Options_Classes_Particles_Move_Spin_js__WEBPACK_IMPORTED_MODULE_61__.Spin),\n/* harmony export */   StartValueType: () => (/* reexport safe */ _Enums_Types_StartValueType_js__WEBPACK_IMPORTED_MODULE_21__.StartValueType),\n/* harmony export */   Stroke: () => (/* reexport safe */ _Options_Classes_Particles_Stroke_js__WEBPACK_IMPORTED_MODULE_53__.Stroke),\n/* harmony export */   Theme: () => (/* reexport safe */ _Options_Classes_Theme_Theme_js__WEBPACK_IMPORTED_MODULE_73__.Theme),\n/* harmony export */   ThemeDefault: () => (/* reexport safe */ _Options_Classes_Theme_ThemeDefault_js__WEBPACK_IMPORTED_MODULE_74__.ThemeDefault),\n/* harmony export */   ThemeMode: () => (/* reexport safe */ _Enums_Modes_ThemeMode_js__WEBPACK_IMPORTED_MODULE_14__.ThemeMode),\n/* harmony export */   ValueWithRandom: () => (/* reexport safe */ _Options_Classes_ValueWithRandom_js__WEBPACK_IMPORTED_MODULE_75__.ValueWithRandom),\n/* harmony export */   Vector: () => (/* reexport safe */ _Core_Utils_Vectors_js__WEBPACK_IMPORTED_MODULE_5__.Vector),\n/* harmony export */   Vector3d: () => (/* reexport safe */ _Core_Utils_Vectors_js__WEBPACK_IMPORTED_MODULE_5__.Vector3d),\n/* harmony export */   ZIndex: () => (/* reexport safe */ _Options_Classes_Particles_ZIndex_ZIndex_js__WEBPACK_IMPORTED_MODULE_71__.ZIndex),\n/* harmony export */   alterHsl: () => (/* reexport safe */ _Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_76__.alterHsl),\n/* harmony export */   animate: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.animate),\n/* harmony export */   areBoundsInside: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.areBoundsInside),\n/* harmony export */   arrayRandomIndex: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.arrayRandomIndex),\n/* harmony export */   calcExactPositionOrRandomFromSize: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.calcExactPositionOrRandomFromSize),\n/* harmony export */   calcExactPositionOrRandomFromSizeRanged: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.calcExactPositionOrRandomFromSizeRanged),\n/* harmony export */   calcPositionFromSize: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.calcPositionFromSize),\n/* harmony export */   calcPositionOrRandomFromSize: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.calcPositionOrRandomFromSize),\n/* harmony export */   calcPositionOrRandomFromSizeRanged: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.calcPositionOrRandomFromSizeRanged),\n/* harmony export */   calculateBounds: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.calculateBounds),\n/* harmony export */   cancelAnimation: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.cancelAnimation),\n/* harmony export */   canvasFirstIndex: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.canvasFirstIndex),\n/* harmony export */   canvasTag: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.canvasTag),\n/* harmony export */   circleBounce: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.circleBounce),\n/* harmony export */   circleBounceDataFromParticle: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.circleBounceDataFromParticle),\n/* harmony export */   clamp: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.clamp),\n/* harmony export */   clear: () => (/* reexport safe */ _Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_76__.clear),\n/* harmony export */   clickRadius: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.clickRadius),\n/* harmony export */   cloneStyle: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.cloneStyle),\n/* harmony export */   collisionVelocity: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.collisionVelocity),\n/* harmony export */   colorMix: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.colorMix),\n/* harmony export */   colorToHsl: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.colorToHsl),\n/* harmony export */   colorToRgb: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.colorToRgb),\n/* harmony export */   countOffset: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.countOffset),\n/* harmony export */   decayOffset: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.decayOffset),\n/* harmony export */   deepExtend: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.deepExtend),\n/* harmony export */   defaultAlpha: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultAlpha),\n/* harmony export */   defaultAngle: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultAngle),\n/* harmony export */   defaultDensityFactor: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultDensityFactor),\n/* harmony export */   defaultFps: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultFps),\n/* harmony export */   defaultFpsLimit: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultFpsLimit),\n/* harmony export */   defaultLoops: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultLoops),\n/* harmony export */   defaultOpacity: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultOpacity),\n/* harmony export */   defaultRadius: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultRadius),\n/* harmony export */   defaultRatio: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultRatio),\n/* harmony export */   defaultReduceFactor: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultReduceFactor),\n/* harmony export */   defaultRemoveQuantity: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultRemoveQuantity),\n/* harmony export */   defaultRetryCount: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultRetryCount),\n/* harmony export */   defaultRgbMin: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultRgbMin),\n/* harmony export */   defaultTime: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultTime),\n/* harmony export */   defaultTransform: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultTransform),\n/* harmony export */   defaultTransformValue: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultTransformValue),\n/* harmony export */   defaultVelocity: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.defaultVelocity),\n/* harmony export */   degToRad: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.degToRad),\n/* harmony export */   deleteCount: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.deleteCount),\n/* harmony export */   divMode: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.divMode),\n/* harmony export */   divModeExecute: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.divModeExecute),\n/* harmony export */   double: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.double),\n/* harmony export */   doublePI: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.doublePI),\n/* harmony export */   drawEffect: () => (/* reexport safe */ _Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_76__.drawEffect),\n/* harmony export */   drawLine: () => (/* reexport safe */ _Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_76__.drawLine),\n/* harmony export */   drawParticle: () => (/* reexport safe */ _Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_76__.drawParticle),\n/* harmony export */   drawParticlePlugin: () => (/* reexport safe */ _Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_76__.drawParticlePlugin),\n/* harmony export */   drawPlugin: () => (/* reexport safe */ _Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_76__.drawPlugin),\n/* harmony export */   drawShape: () => (/* reexport safe */ _Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_76__.drawShape),\n/* harmony export */   drawShapeAfterDraw: () => (/* reexport safe */ _Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_76__.drawShapeAfterDraw),\n/* harmony export */   empty: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.empty),\n/* harmony export */   errorPrefix: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.errorPrefix),\n/* harmony export */   executeOnSingleOrMultiple: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.executeOnSingleOrMultiple),\n/* harmony export */   findItemFromSingleOrMultiple: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.findItemFromSingleOrMultiple),\n/* harmony export */   generatedAttribute: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.generatedAttribute),\n/* harmony export */   generatedFalse: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.generatedFalse),\n/* harmony export */   generatedTrue: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.generatedTrue),\n/* harmony export */   getDistance: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.getDistance),\n/* harmony export */   getDistances: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.getDistances),\n/* harmony export */   getFullScreenStyle: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.getFullScreenStyle),\n/* harmony export */   getHslAnimationFromHsl: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.getHslAnimationFromHsl),\n/* harmony export */   getHslFromAnimation: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.getHslFromAnimation),\n/* harmony export */   getLinkColor: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.getLinkColor),\n/* harmony export */   getLinkRandomColor: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.getLinkRandomColor),\n/* harmony export */   getLogger: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.getLogger),\n/* harmony export */   getParticleBaseVelocity: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.getParticleBaseVelocity),\n/* harmony export */   getParticleDirectionAngle: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.getParticleDirectionAngle),\n/* harmony export */   getPosition: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.getPosition),\n/* harmony export */   getRandom: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.getRandom),\n/* harmony export */   getRandomRgbColor: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.getRandomRgbColor),\n/* harmony export */   getRangeMax: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.getRangeMax),\n/* harmony export */   getRangeMin: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.getRangeMin),\n/* harmony export */   getRangeValue: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.getRangeValue),\n/* harmony export */   getSize: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.getSize),\n/* harmony export */   getStyleFromHsl: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.getStyleFromHsl),\n/* harmony export */   getStyleFromRgb: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.getStyleFromRgb),\n/* harmony export */   hMax: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.hMax),\n/* harmony export */   hMin: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.hMin),\n/* harmony export */   hPhase: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.hPhase),\n/* harmony export */   half: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.half),\n/* harmony export */   hasMatchMedia: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.hasMatchMedia),\n/* harmony export */   hslToRgb: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.hslToRgb),\n/* harmony export */   hslaToRgba: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.hslaToRgba),\n/* harmony export */   identity: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.identity),\n/* harmony export */   initParticleNumericAnimationValue: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.initParticleNumericAnimationValue),\n/* harmony export */   inverseFactorNumerator: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.inverseFactorNumerator),\n/* harmony export */   isArray: () => (/* reexport safe */ _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_81__.isArray),\n/* harmony export */   isBoolean: () => (/* reexport safe */ _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_81__.isBoolean),\n/* harmony export */   isDivModeEnabled: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.isDivModeEnabled),\n/* harmony export */   isFunction: () => (/* reexport safe */ _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_81__.isFunction),\n/* harmony export */   isInArray: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.isInArray),\n/* harmony export */   isNull: () => (/* reexport safe */ _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_81__.isNull),\n/* harmony export */   isNumber: () => (/* reexport safe */ _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_81__.isNumber),\n/* harmony export */   isObject: () => (/* reexport safe */ _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_81__.isObject),\n/* harmony export */   isPointInside: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.isPointInside),\n/* harmony export */   isSsr: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.isSsr),\n/* harmony export */   isString: () => (/* reexport safe */ _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_81__.isString),\n/* harmony export */   itemFromArray: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.itemFromArray),\n/* harmony export */   itemFromSingleOrMultiple: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.itemFromSingleOrMultiple),\n/* harmony export */   lFactor: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.lFactor),\n/* harmony export */   lMax: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.lMax),\n/* harmony export */   lMin: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.lMin),\n/* harmony export */   lengthOffset: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.lengthOffset),\n/* harmony export */   loadFont: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.loadFont),\n/* harmony export */   loadMinIndex: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.loadMinIndex),\n/* harmony export */   loadOptions: () => (/* reexport safe */ _Utils_OptionsUtils_js__WEBPACK_IMPORTED_MODULE_79__.loadOptions),\n/* harmony export */   loadParticlesOptions: () => (/* reexport safe */ _Utils_OptionsUtils_js__WEBPACK_IMPORTED_MODULE_79__.loadParticlesOptions),\n/* harmony export */   loadRandomFactor: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.loadRandomFactor),\n/* harmony export */   manualCount: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.manualCount),\n/* harmony export */   manualDefaultPosition: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.manualDefaultPosition),\n/* harmony export */   midColorValue: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.midColorValue),\n/* harmony export */   millisecondsToSeconds: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds),\n/* harmony export */   minCoordinate: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minCoordinate),\n/* harmony export */   minCount: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minCount),\n/* harmony export */   minFpsLimit: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minFpsLimit),\n/* harmony export */   minIndex: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minIndex),\n/* harmony export */   minLimit: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minLimit),\n/* harmony export */   minRetries: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minRetries),\n/* harmony export */   minStrokeWidth: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minStrokeWidth),\n/* harmony export */   minVelocity: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minVelocity),\n/* harmony export */   minZ: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minZ),\n/* harmony export */   minimumLength: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minimumLength),\n/* harmony export */   minimumSize: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.minimumSize),\n/* harmony export */   mix: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.mix),\n/* harmony export */   mouseDownEvent: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.mouseDownEvent),\n/* harmony export */   mouseLeaveEvent: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.mouseLeaveEvent),\n/* harmony export */   mouseMoveEvent: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.mouseMoveEvent),\n/* harmony export */   mouseOutEvent: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.mouseOutEvent),\n/* harmony export */   mouseUpEvent: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.mouseUpEvent),\n/* harmony export */   none: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.none),\n/* harmony export */   one: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.one),\n/* harmony export */   originPoint: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.originPoint),\n/* harmony export */   paintBase: () => (/* reexport safe */ _Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_76__.paintBase),\n/* harmony export */   paintImage: () => (/* reexport safe */ _Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_76__.paintImage),\n/* harmony export */   parseAlpha: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.parseAlpha),\n/* harmony export */   percentDenominator: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.percentDenominator),\n/* harmony export */   phaseNumerator: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.phaseNumerator),\n/* harmony export */   posOffset: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.posOffset),\n/* harmony export */   qTreeCapacity: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.qTreeCapacity),\n/* harmony export */   quarter: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.quarter),\n/* harmony export */   randomColorValue: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.randomColorValue),\n/* harmony export */   randomInRange: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.randomInRange),\n/* harmony export */   rangeColorToHsl: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.rangeColorToHsl),\n/* harmony export */   rangeColorToRgb: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.rangeColorToRgb),\n/* harmony export */   rectBounce: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.rectBounce),\n/* harmony export */   removeDeleteCount: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.removeDeleteCount),\n/* harmony export */   removeMinIndex: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.removeMinIndex),\n/* harmony export */   resizeEvent: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.resizeEvent),\n/* harmony export */   rgbFactor: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.rgbFactor),\n/* harmony export */   rgbMax: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.rgbMax),\n/* harmony export */   rgbToHsl: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.rgbToHsl),\n/* harmony export */   rollFactor: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.rollFactor),\n/* harmony export */   sMax: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.sMax),\n/* harmony export */   sMin: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.sMin),\n/* harmony export */   sNormalizedOffset: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.sNormalizedOffset),\n/* harmony export */   safeIntersectionObserver: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.safeIntersectionObserver),\n/* harmony export */   safeMatchMedia: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.safeMatchMedia),\n/* harmony export */   safeMutationObserver: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.safeMutationObserver),\n/* harmony export */   setAnimationFunctions: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.setAnimationFunctions),\n/* harmony export */   setLogger: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.setLogger),\n/* harmony export */   setRandom: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.setRandom),\n/* harmony export */   setRangeValue: () => (/* reexport safe */ _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__.setRangeValue),\n/* harmony export */   sextuple: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.sextuple),\n/* harmony export */   singleDivModeExecute: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.singleDivModeExecute),\n/* harmony export */   sizeFactor: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.sizeFactor),\n/* harmony export */   squareExp: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.squareExp),\n/* harmony export */   stringToAlpha: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.stringToAlpha),\n/* harmony export */   stringToRgb: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.stringToRgb),\n/* harmony export */   subdivideCount: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.subdivideCount),\n/* harmony export */   threeQuarter: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.threeQuarter),\n/* harmony export */   touchCancelEvent: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.touchCancelEvent),\n/* harmony export */   touchDelay: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.touchDelay),\n/* harmony export */   touchEndEvent: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.touchEndEvent),\n/* harmony export */   touchEndLengthOffset: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.touchEndLengthOffset),\n/* harmony export */   touchMoveEvent: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.touchMoveEvent),\n/* harmony export */   touchStartEvent: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.touchStartEvent),\n/* harmony export */   triple: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.triple),\n/* harmony export */   tryCountIncrement: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.tryCountIncrement),\n/* harmony export */   updateAnimation: () => (/* reexport safe */ _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__.updateAnimation),\n/* harmony export */   updateColor: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.updateColor),\n/* harmony export */   updateColorValue: () => (/* reexport safe */ _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__.updateColorValue),\n/* harmony export */   visibilityChangeEvent: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.visibilityChangeEvent),\n/* harmony export */   zIndexFactorOffset: () => (/* reexport safe */ _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__.zIndexFactorOffset)\n/* harmony export */ });\n/* harmony import */ var _Core_Utils_Constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Core/Utils/Constants.js */ \"./dist/browser/Core/Utils/Constants.js\");\n/* harmony import */ var _Core_Utils_ExternalInteractorBase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Core/Utils/ExternalInteractorBase.js */ \"./dist/browser/Core/Utils/ExternalInteractorBase.js\");\n/* harmony import */ var _Core_Utils_ParticlesInteractorBase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Core/Utils/ParticlesInteractorBase.js */ \"./dist/browser/Core/Utils/ParticlesInteractorBase.js\");\n/* harmony import */ var _Core_Utils_Point_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Core/Utils/Point.js */ \"./dist/browser/Core/Utils/Point.js\");\n/* harmony import */ var _Core_Utils_Ranges_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Core/Utils/Ranges.js */ \"./dist/browser/Core/Utils/Ranges.js\");\n/* harmony import */ var _Core_Utils_Vectors_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Core/Utils/Vectors.js */ \"./dist/browser/Core/Utils/Vectors.js\");\n/* harmony import */ var _Enums_Directions_MoveDirection_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Enums/Directions/MoveDirection.js */ \"./dist/browser/Enums/Directions/MoveDirection.js\");\n/* harmony import */ var _Enums_Directions_RotateDirection_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Enums/Directions/RotateDirection.js */ \"./dist/browser/Enums/Directions/RotateDirection.js\");\n/* harmony import */ var _Enums_Directions_OutModeDirection_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Enums/Directions/OutModeDirection.js */ \"./dist/browser/Enums/Directions/OutModeDirection.js\");\n/* harmony import */ var _Enums_Modes_AnimationMode_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Enums/Modes/AnimationMode.js */ \"./dist/browser/Enums/Modes/AnimationMode.js\");\n/* harmony import */ var _Enums_Modes_CollisionMode_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Enums/Modes/CollisionMode.js */ \"./dist/browser/Enums/Modes/CollisionMode.js\");\n/* harmony import */ var _Enums_Modes_LimitMode_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./Enums/Modes/LimitMode.js */ \"./dist/browser/Enums/Modes/LimitMode.js\");\n/* harmony import */ var _Enums_Modes_OutMode_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Enums/Modes/OutMode.js */ \"./dist/browser/Enums/Modes/OutMode.js\");\n/* harmony import */ var _Enums_Modes_PixelMode_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./Enums/Modes/PixelMode.js */ \"./dist/browser/Enums/Modes/PixelMode.js\");\n/* harmony import */ var _Enums_Modes_ThemeMode_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./Enums/Modes/ThemeMode.js */ \"./dist/browser/Enums/Modes/ThemeMode.js\");\n/* harmony import */ var _Enums_Modes_ResponsiveMode_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./Enums/Modes/ResponsiveMode.js */ \"./dist/browser/Enums/Modes/ResponsiveMode.js\");\n/* harmony import */ var _Enums_Types_AlterType_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./Enums/Types/AlterType.js */ \"./dist/browser/Enums/Types/AlterType.js\");\n/* harmony import */ var _Enums_Types_DestroyType_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./Enums/Types/DestroyType.js */ \"./dist/browser/Enums/Types/DestroyType.js\");\n/* harmony import */ var _Enums_Types_GradientType_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./Enums/Types/GradientType.js */ \"./dist/browser/Enums/Types/GradientType.js\");\n/* harmony import */ var _Enums_Types_InteractorType_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./Enums/Types/InteractorType.js */ \"./dist/browser/Enums/Types/InteractorType.js\");\n/* harmony import */ var _Enums_Types_ParticleOutType_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./Enums/Types/ParticleOutType.js */ \"./dist/browser/Enums/Types/ParticleOutType.js\");\n/* harmony import */ var _Enums_Types_StartValueType_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./Enums/Types/StartValueType.js */ \"./dist/browser/Enums/Types/StartValueType.js\");\n/* harmony import */ var _Enums_Types_DivType_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./Enums/Types/DivType.js */ \"./dist/browser/Enums/Types/DivType.js\");\n/* harmony import */ var _Enums_Types_EasingType_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./Enums/Types/EasingType.js */ \"./dist/browser/Enums/Types/EasingType.js\");\n/* harmony import */ var _Enums_Types_EventType_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./Enums/Types/EventType.js */ \"./dist/browser/Enums/Types/EventType.js\");\n/* harmony import */ var _Enums_AnimationStatus_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./Enums/AnimationStatus.js */ \"./dist/browser/Enums/AnimationStatus.js\");\n/* harmony import */ var _Enums_InteractivityDetect_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./Enums/InteractivityDetect.js */ \"./dist/browser/Enums/InteractivityDetect.js\");\n/* harmony import */ var _Options_Classes_AnimatableColor_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./Options/Classes/AnimatableColor.js */ \"./dist/browser/Options/Classes/AnimatableColor.js\");\n/* harmony import */ var _Options_Classes_AnimationOptions_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./Options/Classes/AnimationOptions.js */ \"./dist/browser/Options/Classes/AnimationOptions.js\");\n/* harmony import */ var _Options_Classes_Background_Background_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./Options/Classes/Background/Background.js */ \"./dist/browser/Options/Classes/Background/Background.js\");\n/* harmony import */ var _Options_Classes_BackgroundMask_BackgroundMask_js__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./Options/Classes/BackgroundMask/BackgroundMask.js */ \"./dist/browser/Options/Classes/BackgroundMask/BackgroundMask.js\");\n/* harmony import */ var _Options_Classes_BackgroundMask_BackgroundMaskCover_js__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./Options/Classes/BackgroundMask/BackgroundMaskCover.js */ \"./dist/browser/Options/Classes/BackgroundMask/BackgroundMaskCover.js\");\n/* harmony import */ var _Options_Classes_ColorAnimation_js__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./Options/Classes/ColorAnimation.js */ \"./dist/browser/Options/Classes/ColorAnimation.js\");\n/* harmony import */ var _Options_Classes_FullScreen_FullScreen_js__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./Options/Classes/FullScreen/FullScreen.js */ \"./dist/browser/Options/Classes/FullScreen/FullScreen.js\");\n/* harmony import */ var _Options_Classes_HslAnimation_js__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! ./Options/Classes/HslAnimation.js */ \"./dist/browser/Options/Classes/HslAnimation.js\");\n/* harmony import */ var _Options_Classes_Interactivity_Events_ClickEvent_js__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! ./Options/Classes/Interactivity/Events/ClickEvent.js */ \"./dist/browser/Options/Classes/Interactivity/Events/ClickEvent.js\");\n/* harmony import */ var _Options_Classes_Interactivity_Events_DivEvent_js__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ./Options/Classes/Interactivity/Events/DivEvent.js */ \"./dist/browser/Options/Classes/Interactivity/Events/DivEvent.js\");\n/* harmony import */ var _Options_Classes_Interactivity_Events_Events_js__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! ./Options/Classes/Interactivity/Events/Events.js */ \"./dist/browser/Options/Classes/Interactivity/Events/Events.js\");\n/* harmony import */ var _Options_Classes_Interactivity_Events_HoverEvent_js__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! ./Options/Classes/Interactivity/Events/HoverEvent.js */ \"./dist/browser/Options/Classes/Interactivity/Events/HoverEvent.js\");\n/* harmony import */ var _Options_Classes_Interactivity_Events_Parallax_js__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! ./Options/Classes/Interactivity/Events/Parallax.js */ \"./dist/browser/Options/Classes/Interactivity/Events/Parallax.js\");\n/* harmony import */ var _Options_Classes_Interactivity_Events_ResizeEvent_js__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! ./Options/Classes/Interactivity/Events/ResizeEvent.js */ \"./dist/browser/Options/Classes/Interactivity/Events/ResizeEvent.js\");\n/* harmony import */ var _Options_Classes_Interactivity_Interactivity_js__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! ./Options/Classes/Interactivity/Interactivity.js */ \"./dist/browser/Options/Classes/Interactivity/Interactivity.js\");\n/* harmony import */ var _Options_Classes_Interactivity_Modes_Modes_js__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! ./Options/Classes/Interactivity/Modes/Modes.js */ \"./dist/browser/Options/Classes/Interactivity/Modes/Modes.js\");\n/* harmony import */ var _Options_Classes_ManualParticle_js__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! ./Options/Classes/ManualParticle.js */ \"./dist/browser/Options/Classes/ManualParticle.js\");\n/* harmony import */ var _Options_Classes_Options_js__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! ./Options/Classes/Options.js */ \"./dist/browser/Options/Classes/Options.js\");\n/* harmony import */ var _Options_Classes_OptionsColor_js__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! ./Options/Classes/OptionsColor.js */ \"./dist/browser/Options/Classes/OptionsColor.js\");\n/* harmony import */ var _Options_Classes_Particles_Bounce_ParticlesBounce_js__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! ./Options/Classes/Particles/Bounce/ParticlesBounce.js */ \"./dist/browser/Options/Classes/Particles/Bounce/ParticlesBounce.js\");\n/* harmony import */ var _Options_Classes_Particles_Bounce_ParticlesBounceFactor_js__WEBPACK_IMPORTED_MODULE_47__ = __webpack_require__(/*! ./Options/Classes/Particles/Bounce/ParticlesBounceFactor.js */ \"./dist/browser/Options/Classes/Particles/Bounce/ParticlesBounceFactor.js\");\n/* harmony import */ var _Options_Classes_Particles_Collisions_Collisions_js__WEBPACK_IMPORTED_MODULE_48__ = __webpack_require__(/*! ./Options/Classes/Particles/Collisions/Collisions.js */ \"./dist/browser/Options/Classes/Particles/Collisions/Collisions.js\");\n/* harmony import */ var _Options_Classes_Particles_Collisions_CollisionsAbsorb_js__WEBPACK_IMPORTED_MODULE_49__ = __webpack_require__(/*! ./Options/Classes/Particles/Collisions/CollisionsAbsorb.js */ \"./dist/browser/Options/Classes/Particles/Collisions/CollisionsAbsorb.js\");\n/* harmony import */ var _Options_Classes_Particles_Collisions_CollisionsOverlap_js__WEBPACK_IMPORTED_MODULE_50__ = __webpack_require__(/*! ./Options/Classes/Particles/Collisions/CollisionsOverlap.js */ \"./dist/browser/Options/Classes/Particles/Collisions/CollisionsOverlap.js\");\n/* harmony import */ var _Options_Classes_Particles_ParticlesOptions_js__WEBPACK_IMPORTED_MODULE_51__ = __webpack_require__(/*! ./Options/Classes/Particles/ParticlesOptions.js */ \"./dist/browser/Options/Classes/Particles/ParticlesOptions.js\");\n/* harmony import */ var _Options_Classes_Particles_Shadow_js__WEBPACK_IMPORTED_MODULE_52__ = __webpack_require__(/*! ./Options/Classes/Particles/Shadow.js */ \"./dist/browser/Options/Classes/Particles/Shadow.js\");\n/* harmony import */ var _Options_Classes_Particles_Stroke_js__WEBPACK_IMPORTED_MODULE_53__ = __webpack_require__(/*! ./Options/Classes/Particles/Stroke.js */ \"./dist/browser/Options/Classes/Particles/Stroke.js\");\n/* harmony import */ var _Options_Classes_Particles_Move_MoveAttract_js__WEBPACK_IMPORTED_MODULE_54__ = __webpack_require__(/*! ./Options/Classes/Particles/Move/MoveAttract.js */ \"./dist/browser/Options/Classes/Particles/Move/MoveAttract.js\");\n/* harmony import */ var _Options_Classes_Particles_Move_Move_js__WEBPACK_IMPORTED_MODULE_55__ = __webpack_require__(/*! ./Options/Classes/Particles/Move/Move.js */ \"./dist/browser/Options/Classes/Particles/Move/Move.js\");\n/* harmony import */ var _Options_Classes_Particles_Move_MoveAngle_js__WEBPACK_IMPORTED_MODULE_56__ = __webpack_require__(/*! ./Options/Classes/Particles/Move/MoveAngle.js */ \"./dist/browser/Options/Classes/Particles/Move/MoveAngle.js\");\n/* harmony import */ var _Options_Classes_Particles_Move_MoveCenter_js__WEBPACK_IMPORTED_MODULE_57__ = __webpack_require__(/*! ./Options/Classes/Particles/Move/MoveCenter.js */ \"./dist/browser/Options/Classes/Particles/Move/MoveCenter.js\");\n/* harmony import */ var _Options_Classes_Particles_Move_MoveGravity_js__WEBPACK_IMPORTED_MODULE_58__ = __webpack_require__(/*! ./Options/Classes/Particles/Move/MoveGravity.js */ \"./dist/browser/Options/Classes/Particles/Move/MoveGravity.js\");\n/* harmony import */ var _Options_Classes_Particles_Move_OutModes_js__WEBPACK_IMPORTED_MODULE_59__ = __webpack_require__(/*! ./Options/Classes/Particles/Move/OutModes.js */ \"./dist/browser/Options/Classes/Particles/Move/OutModes.js\");\n/* harmony import */ var _Options_Classes_Particles_Move_Path_MovePath_js__WEBPACK_IMPORTED_MODULE_60__ = __webpack_require__(/*! ./Options/Classes/Particles/Move/Path/MovePath.js */ \"./dist/browser/Options/Classes/Particles/Move/Path/MovePath.js\");\n/* harmony import */ var _Options_Classes_Particles_Move_Spin_js__WEBPACK_IMPORTED_MODULE_61__ = __webpack_require__(/*! ./Options/Classes/Particles/Move/Spin.js */ \"./dist/browser/Options/Classes/Particles/Move/Spin.js\");\n/* harmony import */ var _Options_Classes_Particles_Move_MoveTrail_js__WEBPACK_IMPORTED_MODULE_62__ = __webpack_require__(/*! ./Options/Classes/Particles/Move/MoveTrail.js */ \"./dist/browser/Options/Classes/Particles/Move/MoveTrail.js\");\n/* harmony import */ var _Options_Classes_Particles_Number_ParticlesNumber_js__WEBPACK_IMPORTED_MODULE_63__ = __webpack_require__(/*! ./Options/Classes/Particles/Number/ParticlesNumber.js */ \"./dist/browser/Options/Classes/Particles/Number/ParticlesNumber.js\");\n/* harmony import */ var _Options_Classes_Particles_Number_ParticlesNumberLimit_js__WEBPACK_IMPORTED_MODULE_64__ = __webpack_require__(/*! ./Options/Classes/Particles/Number/ParticlesNumberLimit.js */ \"./dist/browser/Options/Classes/Particles/Number/ParticlesNumberLimit.js\");\n/* harmony import */ var _Options_Classes_Particles_Number_ParticlesDensity_js__WEBPACK_IMPORTED_MODULE_65__ = __webpack_require__(/*! ./Options/Classes/Particles/Number/ParticlesDensity.js */ \"./dist/browser/Options/Classes/Particles/Number/ParticlesDensity.js\");\n/* harmony import */ var _Options_Classes_Particles_Opacity_Opacity_js__WEBPACK_IMPORTED_MODULE_66__ = __webpack_require__(/*! ./Options/Classes/Particles/Opacity/Opacity.js */ \"./dist/browser/Options/Classes/Particles/Opacity/Opacity.js\");\n/* harmony import */ var _Options_Classes_Particles_Opacity_OpacityAnimation_js__WEBPACK_IMPORTED_MODULE_67__ = __webpack_require__(/*! ./Options/Classes/Particles/Opacity/OpacityAnimation.js */ \"./dist/browser/Options/Classes/Particles/Opacity/OpacityAnimation.js\");\n/* harmony import */ var _Options_Classes_Particles_Shape_Shape_js__WEBPACK_IMPORTED_MODULE_68__ = __webpack_require__(/*! ./Options/Classes/Particles/Shape/Shape.js */ \"./dist/browser/Options/Classes/Particles/Shape/Shape.js\");\n/* harmony import */ var _Options_Classes_Particles_Size_Size_js__WEBPACK_IMPORTED_MODULE_69__ = __webpack_require__(/*! ./Options/Classes/Particles/Size/Size.js */ \"./dist/browser/Options/Classes/Particles/Size/Size.js\");\n/* harmony import */ var _Options_Classes_Particles_Size_SizeAnimation_js__WEBPACK_IMPORTED_MODULE_70__ = __webpack_require__(/*! ./Options/Classes/Particles/Size/SizeAnimation.js */ \"./dist/browser/Options/Classes/Particles/Size/SizeAnimation.js\");\n/* harmony import */ var _Options_Classes_Particles_ZIndex_ZIndex_js__WEBPACK_IMPORTED_MODULE_71__ = __webpack_require__(/*! ./Options/Classes/Particles/ZIndex/ZIndex.js */ \"./dist/browser/Options/Classes/Particles/ZIndex/ZIndex.js\");\n/* harmony import */ var _Options_Classes_Responsive_js__WEBPACK_IMPORTED_MODULE_72__ = __webpack_require__(/*! ./Options/Classes/Responsive.js */ \"./dist/browser/Options/Classes/Responsive.js\");\n/* harmony import */ var _Options_Classes_Theme_Theme_js__WEBPACK_IMPORTED_MODULE_73__ = __webpack_require__(/*! ./Options/Classes/Theme/Theme.js */ \"./dist/browser/Options/Classes/Theme/Theme.js\");\n/* harmony import */ var _Options_Classes_Theme_ThemeDefault_js__WEBPACK_IMPORTED_MODULE_74__ = __webpack_require__(/*! ./Options/Classes/Theme/ThemeDefault.js */ \"./dist/browser/Options/Classes/Theme/ThemeDefault.js\");\n/* harmony import */ var _Options_Classes_ValueWithRandom_js__WEBPACK_IMPORTED_MODULE_75__ = __webpack_require__(/*! ./Options/Classes/ValueWithRandom.js */ \"./dist/browser/Options/Classes/ValueWithRandom.js\");\n/* harmony import */ var _Utils_CanvasUtils_js__WEBPACK_IMPORTED_MODULE_76__ = __webpack_require__(/*! ./Utils/CanvasUtils.js */ \"./dist/browser/Utils/CanvasUtils.js\");\n/* harmony import */ var _Utils_ColorUtils_js__WEBPACK_IMPORTED_MODULE_77__ = __webpack_require__(/*! ./Utils/ColorUtils.js */ \"./dist/browser/Utils/ColorUtils.js\");\n/* harmony import */ var _Utils_NumberUtils_js__WEBPACK_IMPORTED_MODULE_78__ = __webpack_require__(/*! ./Utils/NumberUtils.js */ \"./dist/browser/Utils/NumberUtils.js\");\n/* harmony import */ var _Utils_OptionsUtils_js__WEBPACK_IMPORTED_MODULE_79__ = __webpack_require__(/*! ./Utils/OptionsUtils.js */ \"./dist/browser/Utils/OptionsUtils.js\");\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_80__ = __webpack_require__(/*! ./Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _Utils_TypeUtils_js__WEBPACK_IMPORTED_MODULE_81__ = __webpack_require__(/*! ./Utils/TypeUtils.js */ \"./dist/browser/Utils/TypeUtils.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/exports.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlterType: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.AlterType),\n/* harmony export */   AnimatableColor: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.AnimatableColor),\n/* harmony export */   AnimationMode: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.AnimationMode),\n/* harmony export */   AnimationOptions: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.AnimationOptions),\n/* harmony export */   AnimationStatus: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.AnimationStatus),\n/* harmony export */   AnimationValueWithRandom: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.AnimationValueWithRandom),\n/* harmony export */   Background: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Background),\n/* harmony export */   BackgroundMask: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.BackgroundMask),\n/* harmony export */   BackgroundMaskCover: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.BackgroundMaskCover),\n/* harmony export */   BaseRange: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.BaseRange),\n/* harmony export */   Circle: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Circle),\n/* harmony export */   ClickEvent: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ClickEvent),\n/* harmony export */   CollisionMode: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.CollisionMode),\n/* harmony export */   Collisions: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Collisions),\n/* harmony export */   CollisionsAbsorb: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.CollisionsAbsorb),\n/* harmony export */   CollisionsOverlap: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.CollisionsOverlap),\n/* harmony export */   ColorAnimation: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ColorAnimation),\n/* harmony export */   DestroyType: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.DestroyType),\n/* harmony export */   DivEvent: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.DivEvent),\n/* harmony export */   DivType: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.DivType),\n/* harmony export */   EasingType: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.EasingType),\n/* harmony export */   EventType: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.EventType),\n/* harmony export */   Events: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Events),\n/* harmony export */   ExternalInteractorBase: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ExternalInteractorBase),\n/* harmony export */   FullScreen: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.FullScreen),\n/* harmony export */   GradientType: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.GradientType),\n/* harmony export */   HoverEvent: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.HoverEvent),\n/* harmony export */   HslAnimation: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.HslAnimation),\n/* harmony export */   Interactivity: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Interactivity),\n/* harmony export */   InteractivityDetect: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.InteractivityDetect),\n/* harmony export */   InteractorType: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.InteractorType),\n/* harmony export */   LimitMode: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.LimitMode),\n/* harmony export */   ManualParticle: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ManualParticle),\n/* harmony export */   Modes: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Modes),\n/* harmony export */   Move: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Move),\n/* harmony export */   MoveAngle: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.MoveAngle),\n/* harmony export */   MoveAttract: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.MoveAttract),\n/* harmony export */   MoveCenter: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.MoveCenter),\n/* harmony export */   MoveDirection: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.MoveDirection),\n/* harmony export */   MoveGravity: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.MoveGravity),\n/* harmony export */   MovePath: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.MovePath),\n/* harmony export */   MoveTrail: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.MoveTrail),\n/* harmony export */   Opacity: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Opacity),\n/* harmony export */   OpacityAnimation: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.OpacityAnimation),\n/* harmony export */   Options: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Options),\n/* harmony export */   OptionsColor: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.OptionsColor),\n/* harmony export */   OutMode: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.OutMode),\n/* harmony export */   OutModeDirection: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.OutModeDirection),\n/* harmony export */   OutModes: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.OutModes),\n/* harmony export */   Parallax: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Parallax),\n/* harmony export */   ParticleOutType: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ParticleOutType),\n/* harmony export */   ParticlesBounce: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ParticlesBounce),\n/* harmony export */   ParticlesBounceFactor: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ParticlesBounceFactor),\n/* harmony export */   ParticlesDensity: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ParticlesDensity),\n/* harmony export */   ParticlesInteractorBase: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ParticlesInteractorBase),\n/* harmony export */   ParticlesNumber: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ParticlesNumber),\n/* harmony export */   ParticlesNumberLimit: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ParticlesNumberLimit),\n/* harmony export */   ParticlesOptions: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ParticlesOptions),\n/* harmony export */   PixelMode: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.PixelMode),\n/* harmony export */   Point: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Point),\n/* harmony export */   RangedAnimationOptions: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.RangedAnimationOptions),\n/* harmony export */   RangedAnimationValueWithRandom: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.RangedAnimationValueWithRandom),\n/* harmony export */   Rectangle: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Rectangle),\n/* harmony export */   ResizeEvent: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ResizeEvent),\n/* harmony export */   Responsive: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Responsive),\n/* harmony export */   ResponsiveMode: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ResponsiveMode),\n/* harmony export */   RotateDirection: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.RotateDirection),\n/* harmony export */   Shadow: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Shadow),\n/* harmony export */   Shape: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Shape),\n/* harmony export */   Size: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Size),\n/* harmony export */   SizeAnimation: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.SizeAnimation),\n/* harmony export */   Spin: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Spin),\n/* harmony export */   StartValueType: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.StartValueType),\n/* harmony export */   Stroke: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Stroke),\n/* harmony export */   Theme: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Theme),\n/* harmony export */   ThemeDefault: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ThemeDefault),\n/* harmony export */   ThemeMode: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ThemeMode),\n/* harmony export */   ValueWithRandom: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ValueWithRandom),\n/* harmony export */   Vector: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Vector),\n/* harmony export */   Vector3d: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.Vector3d),\n/* harmony export */   ZIndex: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.ZIndex),\n/* harmony export */   alterHsl: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.alterHsl),\n/* harmony export */   animate: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.animate),\n/* harmony export */   areBoundsInside: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.areBoundsInside),\n/* harmony export */   arrayRandomIndex: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.arrayRandomIndex),\n/* harmony export */   calcExactPositionOrRandomFromSize: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.calcExactPositionOrRandomFromSize),\n/* harmony export */   calcExactPositionOrRandomFromSizeRanged: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.calcExactPositionOrRandomFromSizeRanged),\n/* harmony export */   calcPositionFromSize: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.calcPositionFromSize),\n/* harmony export */   calcPositionOrRandomFromSize: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.calcPositionOrRandomFromSize),\n/* harmony export */   calcPositionOrRandomFromSizeRanged: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.calcPositionOrRandomFromSizeRanged),\n/* harmony export */   calculateBounds: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.calculateBounds),\n/* harmony export */   cancelAnimation: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.cancelAnimation),\n/* harmony export */   canvasFirstIndex: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.canvasFirstIndex),\n/* harmony export */   canvasTag: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.canvasTag),\n/* harmony export */   circleBounce: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.circleBounce),\n/* harmony export */   circleBounceDataFromParticle: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.circleBounceDataFromParticle),\n/* harmony export */   clamp: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.clamp),\n/* harmony export */   clear: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.clear),\n/* harmony export */   clickRadius: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.clickRadius),\n/* harmony export */   cloneStyle: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.cloneStyle),\n/* harmony export */   collisionVelocity: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.collisionVelocity),\n/* harmony export */   colorMix: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.colorMix),\n/* harmony export */   colorToHsl: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.colorToHsl),\n/* harmony export */   colorToRgb: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.colorToRgb),\n/* harmony export */   countOffset: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.countOffset),\n/* harmony export */   decayOffset: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.decayOffset),\n/* harmony export */   deepExtend: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.deepExtend),\n/* harmony export */   defaultAlpha: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultAlpha),\n/* harmony export */   defaultAngle: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultAngle),\n/* harmony export */   defaultDensityFactor: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultDensityFactor),\n/* harmony export */   defaultFps: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultFps),\n/* harmony export */   defaultFpsLimit: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultFpsLimit),\n/* harmony export */   defaultLoops: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultLoops),\n/* harmony export */   defaultOpacity: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultOpacity),\n/* harmony export */   defaultRadius: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultRadius),\n/* harmony export */   defaultRatio: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultRatio),\n/* harmony export */   defaultReduceFactor: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultReduceFactor),\n/* harmony export */   defaultRemoveQuantity: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultRemoveQuantity),\n/* harmony export */   defaultRetryCount: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultRetryCount),\n/* harmony export */   defaultRgbMin: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultRgbMin),\n/* harmony export */   defaultTime: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultTime),\n/* harmony export */   defaultTransform: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultTransform),\n/* harmony export */   defaultTransformValue: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultTransformValue),\n/* harmony export */   defaultVelocity: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.defaultVelocity),\n/* harmony export */   degToRad: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.degToRad),\n/* harmony export */   deleteCount: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.deleteCount),\n/* harmony export */   divMode: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.divMode),\n/* harmony export */   divModeExecute: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.divModeExecute),\n/* harmony export */   double: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.double),\n/* harmony export */   doublePI: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.doublePI),\n/* harmony export */   drawEffect: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.drawEffect),\n/* harmony export */   drawLine: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.drawLine),\n/* harmony export */   drawParticle: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.drawParticle),\n/* harmony export */   drawParticlePlugin: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.drawParticlePlugin),\n/* harmony export */   drawPlugin: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.drawPlugin),\n/* harmony export */   drawShape: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.drawShape),\n/* harmony export */   drawShapeAfterDraw: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.drawShapeAfterDraw),\n/* harmony export */   empty: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.empty),\n/* harmony export */   errorPrefix: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.errorPrefix),\n/* harmony export */   executeOnSingleOrMultiple: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.executeOnSingleOrMultiple),\n/* harmony export */   findItemFromSingleOrMultiple: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.findItemFromSingleOrMultiple),\n/* harmony export */   generatedAttribute: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.generatedAttribute),\n/* harmony export */   generatedFalse: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.generatedFalse),\n/* harmony export */   generatedTrue: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.generatedTrue),\n/* harmony export */   getDistance: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getDistance),\n/* harmony export */   getDistances: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getDistances),\n/* harmony export */   getFullScreenStyle: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getFullScreenStyle),\n/* harmony export */   getHslAnimationFromHsl: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getHslAnimationFromHsl),\n/* harmony export */   getHslFromAnimation: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getHslFromAnimation),\n/* harmony export */   getLinkColor: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getLinkColor),\n/* harmony export */   getLinkRandomColor: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getLinkRandomColor),\n/* harmony export */   getLogger: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getLogger),\n/* harmony export */   getParticleBaseVelocity: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getParticleBaseVelocity),\n/* harmony export */   getParticleDirectionAngle: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getParticleDirectionAngle),\n/* harmony export */   getPosition: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getPosition),\n/* harmony export */   getRandom: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getRandom),\n/* harmony export */   getRandomRgbColor: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getRandomRgbColor),\n/* harmony export */   getRangeMax: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getRangeMax),\n/* harmony export */   getRangeMin: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getRangeMin),\n/* harmony export */   getRangeValue: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getRangeValue),\n/* harmony export */   getSize: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getSize),\n/* harmony export */   getStyleFromHsl: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getStyleFromHsl),\n/* harmony export */   getStyleFromRgb: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.getStyleFromRgb),\n/* harmony export */   hMax: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.hMax),\n/* harmony export */   hMin: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.hMin),\n/* harmony export */   hPhase: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.hPhase),\n/* harmony export */   half: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.half),\n/* harmony export */   hasMatchMedia: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.hasMatchMedia),\n/* harmony export */   hslToRgb: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.hslToRgb),\n/* harmony export */   hslaToRgba: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.hslaToRgba),\n/* harmony export */   identity: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.identity),\n/* harmony export */   initParticleNumericAnimationValue: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.initParticleNumericAnimationValue),\n/* harmony export */   inverseFactorNumerator: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.inverseFactorNumerator),\n/* harmony export */   isArray: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.isArray),\n/* harmony export */   isBoolean: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.isBoolean),\n/* harmony export */   isDivModeEnabled: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.isDivModeEnabled),\n/* harmony export */   isFunction: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.isFunction),\n/* harmony export */   isInArray: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.isInArray),\n/* harmony export */   isNull: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.isNull),\n/* harmony export */   isNumber: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.isNumber),\n/* harmony export */   isObject: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.isObject),\n/* harmony export */   isPointInside: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.isPointInside),\n/* harmony export */   isSsr: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.isSsr),\n/* harmony export */   isString: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.isString),\n/* harmony export */   itemFromArray: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.itemFromArray),\n/* harmony export */   itemFromSingleOrMultiple: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.itemFromSingleOrMultiple),\n/* harmony export */   lFactor: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.lFactor),\n/* harmony export */   lMax: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.lMax),\n/* harmony export */   lMin: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.lMin),\n/* harmony export */   lengthOffset: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.lengthOffset),\n/* harmony export */   loadFont: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.loadFont),\n/* harmony export */   loadMinIndex: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.loadMinIndex),\n/* harmony export */   loadOptions: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.loadOptions),\n/* harmony export */   loadParticlesOptions: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.loadParticlesOptions),\n/* harmony export */   loadRandomFactor: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.loadRandomFactor),\n/* harmony export */   manualCount: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.manualCount),\n/* harmony export */   manualDefaultPosition: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.manualDefaultPosition),\n/* harmony export */   midColorValue: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.midColorValue),\n/* harmony export */   millisecondsToSeconds: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.millisecondsToSeconds),\n/* harmony export */   minCoordinate: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.minCoordinate),\n/* harmony export */   minCount: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.minCount),\n/* harmony export */   minFpsLimit: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.minFpsLimit),\n/* harmony export */   minIndex: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.minIndex),\n/* harmony export */   minLimit: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.minLimit),\n/* harmony export */   minRetries: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.minRetries),\n/* harmony export */   minStrokeWidth: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.minStrokeWidth),\n/* harmony export */   minVelocity: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.minVelocity),\n/* harmony export */   minZ: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.minZ),\n/* harmony export */   minimumLength: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.minimumLength),\n/* harmony export */   minimumSize: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.minimumSize),\n/* harmony export */   mix: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.mix),\n/* harmony export */   mouseDownEvent: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.mouseDownEvent),\n/* harmony export */   mouseLeaveEvent: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.mouseLeaveEvent),\n/* harmony export */   mouseMoveEvent: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.mouseMoveEvent),\n/* harmony export */   mouseOutEvent: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.mouseOutEvent),\n/* harmony export */   mouseUpEvent: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.mouseUpEvent),\n/* harmony export */   none: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.none),\n/* harmony export */   one: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.one),\n/* harmony export */   originPoint: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.originPoint),\n/* harmony export */   paintBase: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.paintBase),\n/* harmony export */   paintImage: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.paintImage),\n/* harmony export */   parseAlpha: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.parseAlpha),\n/* harmony export */   percentDenominator: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.percentDenominator),\n/* harmony export */   phaseNumerator: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.phaseNumerator),\n/* harmony export */   posOffset: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.posOffset),\n/* harmony export */   qTreeCapacity: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.qTreeCapacity),\n/* harmony export */   quarter: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.quarter),\n/* harmony export */   randomColorValue: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.randomColorValue),\n/* harmony export */   randomInRange: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.randomInRange),\n/* harmony export */   rangeColorToHsl: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.rangeColorToHsl),\n/* harmony export */   rangeColorToRgb: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.rangeColorToRgb),\n/* harmony export */   rectBounce: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.rectBounce),\n/* harmony export */   removeDeleteCount: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.removeDeleteCount),\n/* harmony export */   removeMinIndex: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.removeMinIndex),\n/* harmony export */   resizeEvent: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.resizeEvent),\n/* harmony export */   rgbFactor: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.rgbFactor),\n/* harmony export */   rgbMax: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.rgbMax),\n/* harmony export */   rgbToHsl: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.rgbToHsl),\n/* harmony export */   rollFactor: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.rollFactor),\n/* harmony export */   sMax: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.sMax),\n/* harmony export */   sMin: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.sMin),\n/* harmony export */   sNormalizedOffset: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.sNormalizedOffset),\n/* harmony export */   safeIntersectionObserver: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.safeIntersectionObserver),\n/* harmony export */   safeMatchMedia: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.safeMatchMedia),\n/* harmony export */   safeMutationObserver: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.safeMutationObserver),\n/* harmony export */   setAnimationFunctions: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.setAnimationFunctions),\n/* harmony export */   setLogger: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.setLogger),\n/* harmony export */   setRandom: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.setRandom),\n/* harmony export */   setRangeValue: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.setRangeValue),\n/* harmony export */   sextuple: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.sextuple),\n/* harmony export */   singleDivModeExecute: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.singleDivModeExecute),\n/* harmony export */   sizeFactor: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.sizeFactor),\n/* harmony export */   squareExp: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.squareExp),\n/* harmony export */   stringToAlpha: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.stringToAlpha),\n/* harmony export */   stringToRgb: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.stringToRgb),\n/* harmony export */   subdivideCount: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.subdivideCount),\n/* harmony export */   threeQuarter: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.threeQuarter),\n/* harmony export */   touchCancelEvent: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.touchCancelEvent),\n/* harmony export */   touchDelay: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.touchDelay),\n/* harmony export */   touchEndEvent: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.touchEndEvent),\n/* harmony export */   touchEndLengthOffset: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.touchEndLengthOffset),\n/* harmony export */   touchMoveEvent: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.touchMoveEvent),\n/* harmony export */   touchStartEvent: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.touchStartEvent),\n/* harmony export */   triple: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.triple),\n/* harmony export */   tryCountIncrement: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.tryCountIncrement),\n/* harmony export */   tsParticles: () => (/* binding */ tsParticles),\n/* harmony export */   updateAnimation: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.updateAnimation),\n/* harmony export */   updateColor: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.updateColor),\n/* harmony export */   updateColorValue: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.updateColorValue),\n/* harmony export */   visibilityChangeEvent: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.visibilityChangeEvent),\n/* harmony export */   zIndexFactorOffset: () => (/* reexport safe */ _exports_js__WEBPACK_IMPORTED_MODULE_2__.zIndexFactorOffset)\n/* harmony export */ });\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./init.js */ \"./dist/browser/init.js\");\n/* harmony import */ var _Utils_Utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils/Utils.js */ \"./dist/browser/Utils/Utils.js\");\n/* harmony import */ var _exports_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./exports.js */ \"./dist/browser/exports.js\");\n\n\nconst tsParticles = (0,_init_js__WEBPACK_IMPORTED_MODULE_0__.init)();\nif (!(0,_Utils_Utils_js__WEBPACK_IMPORTED_MODULE_1__.isSsr)()) {\n  window.tsParticles = tsParticles;\n}\n\n\n\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/index.js?");

/***/ }),

/***/ "./dist/browser/init.js":
/*!******************************!*\
  !*** ./dist/browser/init.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   init: () => (/* binding */ init)\n/* harmony export */ });\n/* harmony import */ var _Core_Engine_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Core/Engine.js */ \"./dist/browser/Core/Engine.js\");\n\nfunction init() {\n  const engine = new _Core_Engine_js__WEBPACK_IMPORTED_MODULE_0__.Engine();\n  engine.init();\n  return engine;\n}\n\n//# sourceURL=webpack://@tsparticles/engine/./dist/browser/init.js?");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});