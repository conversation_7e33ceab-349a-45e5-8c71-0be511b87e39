import React, { useEffect, useRef } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Parallax } from 'react-scroll-parallax';

// Feature Card Component
const FeatureCard = ({ icon, title, description, index }) => {
  const controls = useAnimation();
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: true, amount: 0.2 });

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);

  return (
    <motion.div
      ref={cardRef}
      className="bg-black/30 backdrop-blur-sm border border-white/10 rounded-xl p-6"
      initial="hidden"
      animate={controls}
      variants={{
        hidden: { opacity: 0, y: 30 },
        visible: {
          opacity: 1,
          y: 0,
          transition: {
            duration: 0.5,
            delay: 0.1 * index
          }
        }
      }}
    >
      <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mb-4">
        {icon}
      </div>
      <h3 className="text-xl font-bold text-white mb-3">{title}</h3>
      <p className="text-gray-300">{description}</p>
    </motion.div>
  );
};

// Process Step Component
const ProcessStep = ({ number, title, description, index }) => {
  const controls = useAnimation();
  const stepRef = useRef(null);
  const isInView = useInView(stepRef, { once: false, amount: 0.2 });

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);

  return (
    <motion.div
      ref={stepRef}
      className="flex items-start gap-4"
      initial="hidden"
      animate={controls}
      variants={{
        hidden: { opacity: 0, y: 20 },
        visible: {
          opacity: 1,
          y: 0,
          transition: {
            duration: 0.5,
            delay: 0.1 * index
          }
        }
      }}
    >
      <div className="flex-shrink-0 w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold">
        {number}
      </div>
      <div>
        <h3 className="text-lg font-bold text-white mb-2">{title}</h3>
        <p className="text-gray-300 mb-6">{description}</p>
      </div>
    </motion.div>
  );
};

// Technology Card Component
const TechnologyCard = ({ icon, name, index }) => {
  return (
    <motion.div
      className="flex flex-col items-center bg-black/30 backdrop-blur-sm border border-white/10 rounded-xl p-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.05 * index }}
    >
      <div className="text-4xl mb-2">{icon}</div>
      <span className="text-white font-medium">{name}</span>
    </motion.div>
  );
};

const WebsiteDevelopment = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.1 });
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Features data
  const features = [
    {
      icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>,
      title: "Responsive Design",
      description: "Websites that look and function perfectly on all devices, from desktops to smartphones, ensuring a seamless user experience."
    },
    {
      icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>,
      title: "Fast Loading Speeds",
      description: "Optimized performance with quick loading times to improve user experience and search engine rankings."
    },
    {
      icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
      </svg>,
      title: "Content Management",
      description: "User-friendly CMS integration allowing you to easily update and manage your website content without technical knowledge."
    },
    {
      icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
      </svg>,
      title: "Security Features",
      description: "Built-in security measures to protect your website from common vulnerabilities and threats."
    },
    {
      icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>,
      title: "SEO Optimization",
      description: "Search engine friendly structure and features to help your website rank higher in search results."
    },
    {
      icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
      </svg>,
      title: "Custom Functionality",
      description: "Tailored features and functionality specific to your business needs and goals."
    }
  ];

  // Process steps
  const processSteps = [
    {
      number: "1",
      title: "Discovery & Planning",
      description: "We begin by understanding your business, target audience, and goals. This phase includes market research, competitor analysis, and defining the website's objectives and requirements."
    },
    {
      number: "2",
      title: "Information Architecture & Wireframing",
      description: "We create the blueprint for your website, organizing content and planning user flows. Wireframes provide a visual guide for the layout and structure of each page."
    },
    {
      number: "3",
      title: "Visual Design",
      description: "Our designers create the visual identity of your website, including color schemes, typography, imagery, and overall aesthetic that aligns with your brand."
    },
    {
      number: "4",
      title: "Development",
      description: "Our student developers, under professional guidance, bring the designs to life with clean, efficient code, ensuring responsive behavior and cross-browser compatibility."
    },
    {
      number: "5",
      title: "Content Integration",
      description: "We populate your website with optimized content, including text, images, videos, and other media elements."
    },
    {
      number: "6",
      title: "Testing & Quality Assurance",
      description: "Rigorous testing across devices and browsers ensures your website functions flawlessly, with all features working as intended."
    },
    {
      number: "7",
      title: "Launch & Training",
      description: "After final approval, we launch your website and provide training on how to use the content management system and maintain your site."
    }
  ];

  // Technologies
  const technologies = [
    { icon: "🔵", name: "React" },
    { icon: "🟣", name: "Next.js" },
    { icon: "🟠", name: "HTML5" },
    { icon: "🔷", name: "CSS3" },
    { icon: "🟡", name: "JavaScript" },
    { icon: "🟢", name: "Node.js" },
    { icon: "🔴", name: "WordPress" },
    { icon: "⚫", name: "Shopify" }
  ];

  return (
    <section ref={sectionRef} className="py-32 bg-black relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <Parallax translateY={[-20, 20]} className="absolute top-1/4 right-1/4 w-96 h-96 bg-purple-600/10 rounded-full filter blur-3xl"></Parallax>
        <Parallax translateY={[20, -20]} className="absolute bottom-1/3 left-1/3 w-80 h-80 bg-blue-600/10 rounded-full filter blur-3xl"></Parallax>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Breadcrumbs */}
        <div className="max-w-7xl mx-auto mb-8">
          <div className="flex items-center text-sm text-gray-400">
            <Link to="/" className="hover:text-purple-400 transition-colors">Home</Link>
            <span className="mx-2">/</span>
            <Link to="/services" className="hover:text-purple-400 transition-colors">Services</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-300">Website Development</span>
          </div>
        </div>

        {/* Hero Section */}
        <motion.div
          className="text-center max-w-4xl mx-auto mb-20"
          initial="hidden"
          animate={controls}
          variants={{
            hidden: { opacity: 0 },
            visible: {
              opacity: 1,
              transition: {
                staggerChildren: 0.2
              }
            }
          }}
        >
          <motion.h1
            className="text-4xl md:text-5xl font-bold text-white mb-6"
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: {
                  duration: 0.5,
                  delay: 0.2
                }
              }
            }}
          >
            Website <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-500">Development</span>
          </motion.h1>
          <motion.p
            className="text-xl text-gray-300 mb-10"
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: {
                  duration: 0.5,
                  delay: 0.3
                }
              }
            }}
          >
            Custom-designed, responsive websites that showcase your brand and drive conversions.
          </motion.p>
        </motion.div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto mb-20">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {/* Left Column - Image */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="rounded-xl overflow-hidden"
            >
              <img 
                src="https://images.unsplash.com/photo-1547658719-da2b51169166?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80" 
                alt="Website Development" 
                className="w-full h-full object-cover"
              />
            </motion.div>
            
            {/* Right Column - Text */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <h2 className="text-2xl font-bold text-white mb-6">Creating Your Digital Presence</h2>
              <div className="prose prose-invert max-w-none">
                <p className="text-gray-300 mb-4">
                  In today's digital landscape, your website is often the first interaction potential customers have with your brand. At Intelermate, we specialize in creating websites that not only look stunning but also drive results for your business.
                </p>
                <p className="text-gray-300 mb-4">
                  Our website development service combines beautiful design with powerful functionality, creating a digital presence that effectively communicates your brand message and converts visitors into customers.
                </p>
                <p className="text-gray-300 mb-6">
                  Whether you need a simple informational site, a complex corporate platform, or anything in between, our team of talented student developers, guided by experienced professionals, will create a custom solution tailored to your specific needs and goals.
                </p>
                <div className="flex flex-wrap gap-4">
                  <span className="inline-block bg-purple-900/30 text-purple-300 px-3 py-1 rounded-full text-sm">Responsive Design</span>
                  <span className="inline-block bg-blue-900/30 text-blue-300 px-3 py-1 rounded-full text-sm">SEO-Friendly</span>
                  <span className="inline-block bg-pink-900/30 text-pink-300 px-3 py-1 rounded-full text-sm">Fast Loading</span>
                  <span className="inline-block bg-green-900/30 text-green-300 px-3 py-1 rounded-full text-sm">User-Friendly</span>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Features Section */}
        <div className="max-w-7xl mx-auto mb-24">
          <motion.h2
            className="text-3xl font-bold text-white mb-12 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={controls}
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: {
                  duration: 0.5,
                  delay: 0.5
                }
              }
            }}
          >
            Key Features
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature, index) => (
              <FeatureCard
                key={index}
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
                index={index}
              />
            ))}
          </div>
        </div>

        {/* Process Section */}
        <div className="max-w-5xl mx-auto mb-24">
          <motion.h2
            className="text-3xl font-bold text-white mb-12 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={controls}
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: {
                  duration: 0.5,
                  delay: 0.6
                }
              }
            }}
          >
            Our Development Process
          </motion.h2>

          <div className="space-y-6">
            {processSteps.map((step, index) => (
              <ProcessStep
                key={index}
                number={step.number}
                title={step.title}
                description={step.description}
                index={index}
              />
            ))}
          </div>
        </div>

        {/* Technologies Section */}
        <div className="max-w-5xl mx-auto mb-24">
          <motion.h2
            className="text-3xl font-bold text-white mb-12 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={controls}
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: {
                  duration: 0.5,
                  delay: 0.7
                }
              }
            }}
          >
            Technologies We Use
          </motion.h2>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {technologies.map((tech, index) => (
              <TechnologyCard
                key={index}
                icon={tech.icon}
                name={tech.name}
                index={index}
              />
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <motion.div
          className="max-w-4xl mx-auto text-center bg-black/30 backdrop-blur-sm border border-white/10 rounded-xl p-8"
          initial={{ opacity: 0, y: 20 }}
          animate={controls}
          variants={{
            hidden: { opacity: 0, y: 20 },
            visible: {
              opacity: 1,
              y: 0,
              transition: {
                duration: 0.5,
                delay: 0.8
              }
            }
          }}
        >
          <h2 className="text-3xl font-bold text-white mb-6">Ready to Build Your Website?</h2>
          <p className="text-xl text-gray-300 mb-8">
            Schedule a free consultation to discuss your website project. Our team will work with you to create a custom solution that meets your specific needs and goals.
          </p>
          <Link
            to="/consultation"
            className="inline-block bg-gradient-to-r from-purple-600 to-blue-500 text-white px-8 py-4 rounded-lg text-lg font-medium hover:shadow-lg hover:shadow-purple-500/20 transition-all"
          >
            Schedule Free Consultation
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default WebsiteDevelopment;
