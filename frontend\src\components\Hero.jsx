import React, { useEffect, useRef, useState, useMemo } from 'react';
import { Parallax, useParallax } from 'react-scroll-parallax';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import CurvedLoop from './ui/curved-loop';

const Hero = () => {
  const heroRef = useRef(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [tagElements, setTagElements] = useState([]);
  const [isMobile, setIsMobile] = useState(false);
  const navigate = useNavigate();

  // Move parallax hooks to top level
  const purpleBlob = useParallax({
    speed: -10,
    easing: 'easeInQuad',
  });

  const blueBlob = useParallax({
    speed: 10,
    easing: 'easeOutQuad',
  });

  const pinkBlob = useParallax({
    speed: -5,
    easing: 'easeInOutQuad',
  });

  // Memoize parallax refs to prevent unnecessary re-renders
  const parallaxRefs = useMemo(() => ({
    purpleBlob: purpleBlob.ref,
    blueBlob: blueBlob.ref,
    pinkBlob: pinkBlob.ref
  }), [purpleBlob.ref, blueBlob.ref, pinkBlob.ref]);

  // Optimize mouse move handler with debounce
  useEffect(() => {
    let timeoutId;
    const handleMouseMove = (e) => {
      if (timeoutId) {
        cancelAnimationFrame(timeoutId);
      }
      timeoutId = requestAnimationFrame(() => {
        setMousePosition({ x: e.clientX, y: e.clientY });
      });
    };

    window.addEventListener('mousemove', handleMouseMove, { passive: true });

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      if (timeoutId) {
        cancelAnimationFrame(timeoutId);
      }
    };
  }, []);

  // Optimize mobile check with debounce
  useEffect(() => {
    let timeoutId;
    const checkMobile = () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(() => {
        setIsMobile(window.innerWidth < 768);
      }, 100);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile, { passive: true });

    return () => {
      window.removeEventListener('resize', checkMobile);
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, []);

  // Optimize tag elements initialization
  useEffect(() => {
    const elements = Array.from(document.querySelectorAll('.tech-tag-behind'));
    setTagElements(elements);
  }, []);

  // Store the current positions and animation state of each tag
  const tagPositions = useRef({});
  const animationTime = useRef(0);

  // Optimize animation frame handling
  useEffect(() => {
    if (tagElements.length === 0) return;

    let animationFrameId;
    let lastTimestamp = 0;
    const FPS = 60;
    const frameInterval = 1000 / FPS;

    const applyWellfoundEffect = (timestamp) => {
      if (timestamp - lastTimestamp < frameInterval) {
        animationFrameId = requestAnimationFrame(applyWellfoundEffect);
        return;
      }
      lastTimestamp = timestamp;

      animationTime.current = timestamp / 1000;

      const tagDataElements = document.querySelectorAll('.tech-tag-behind');
      const heroRect = heroRef.current?.getBoundingClientRect() || { width: window.innerWidth, height: window.innerHeight };

      const isMouseInHero =
        mousePosition.x >= heroRect.left &&
        mousePosition.x <= heroRect.right &&
        mousePosition.y >= heroRect.top &&
        mousePosition.y <= heroRect.bottom;

      const normalizedMouseX = ((mousePosition.x - heroRect.left) / heroRect.width) * 2 - 1;
      const normalizedMouseY = ((mousePosition.y - heroRect.top) / heroRect.height) * 2 - 1;

      tagDataElements.forEach((tag, index) => {
        const rect = tag.getBoundingClientRect();
        if (!tagPositions.current[index]) {
          const randomPhase = Math.random() * Math.PI * 2;
          const randomAmplitude = 2 + Math.random() * 3;
          tagPositions.current[index] = {
            x: 0,
            y: 0,
            baseX: 0,
            baseY: 0,
            phase: randomPhase,
            amplitude: randomAmplitude,
            speed: 0.5 + Math.random() * 0.5
          };
        }

        if (isMobile) {
          tag.style.transform = 'translate(0px, 0px)';
          tag.style.zIndex = '0';
          tag.style.opacity = '0.8';
          tag.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
          tag.style.transition = 'opacity 0.3s ease, background-color 0.3s ease';
        } else {
          const tagInfo = tagPositions.current[index];
          const floatX = Math.sin(animationTime.current * tagInfo.speed + tagInfo.phase) * tagInfo.amplitude;
          const floatY = Math.cos(animationTime.current * tagInfo.speed + tagInfo.phase + 1) * tagInfo.amplitude;

          const tagCenterX = (rect.left + rect.width / 2 - heroRect.left) / heroRect.width * 2 - 1;
          const tagCenterY = (rect.top + rect.height / 2 - heroRect.top) / heroRect.height * 2 - 1;

          const vectorX = tagCenterX - normalizedMouseX;
          const vectorY = tagCenterY - normalizedMouseY;
          const distance = Math.sqrt(vectorX * vectorX + vectorY * vectorY);

          const mouseMaxDistance = 0.6;
          const mouseStrength = 15;

          let mouseX = 0;
          let mouseY = 0;

          if (isMouseInHero && distance < mouseMaxDistance) {
            const influence = (1 - distance / mouseMaxDistance) * mouseStrength;
            mouseX = vectorX * influence;
            mouseY = vectorY * influence;
          }

          const finalX = floatX + mouseX;
          const finalY = floatY + mouseY;

          tag.style.transform = `translate(${finalX}px, ${finalY}px)`;
          tag.style.zIndex = isMouseInHero && distance < mouseMaxDistance ? '1' : '0';
          tag.style.opacity = isMouseInHero && distance < mouseMaxDistance ? '1' : '0.8';
          tag.style.backgroundColor = isMouseInHero && distance < mouseMaxDistance ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.05)';
          tag.style.transition = 'opacity 0.3s ease, background-color 0.3s ease';
        }
      });

      animationFrameId = requestAnimationFrame(applyWellfoundEffect);
    };

    animationFrameId = requestAnimationFrame(applyWellfoundEffect);

    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [tagElements, mousePosition, isMobile]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: 'spring', stiffness: 100 },
    },
  };

  return (
    <section ref={heroRef} className="relative min-h-screen flex flex-col justify-center items-center overflow-hidden bg-gradient-to-b from-black via-purple-900/20 to-black" style={{ position: 'relative', isolation: 'isolate' }}>
      {/* Background parallax elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div ref={parallaxRefs.purpleBlob} className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-600/20 rounded-full filter blur-3xl will-change-transform"></div>
        <div ref={parallaxRefs.blueBlob} className="absolute bottom-1/3 right-1/3 w-80 h-80 bg-blue-600/20 rounded-full filter blur-3xl will-change-transform"></div>
        <div ref={parallaxRefs.pinkBlob} className="absolute top-2/3 left-1/3 w-64 h-64 bg-pink-600/20 rounded-full filter blur-3xl will-change-transform"></div>
      </div>

      {/* Background tech tags - wellfound.com style stacked grid in mobile view */}
      <div className={`${isMobile ? 'absolute left-0 right-0 bottom-0 flex flex-wrap justify-center items-center gap-1 p-2 pb-6 bg-gradient-to-t from-black to-transparent' : 'absolute inset-0'} z-0 ${isMobile ? 'pointer-events-none' : 'pointer-events-none'} overflow-hidden`}
           style={{
             height: isMobile ? 'auto' : '100%',
             maxHeight: isMobile ? '20vh' : '100vh',
             paddingBottom: isMobile ? '20px' : '0',
             bottom: isMobile ? '0' : 'auto'
           }}>
        {[
          // Standard tech tags
          { text: 'React', delay: 0.4, position: { top: '35%', left: '15%' }, factor: { x: 0.5, y: -0.3 } },
          { text: 'Tailwind CSS', delay: 0.5, position: { top: '20%', left: '65%' }, factor: { x: -0.2, y: 0.4 } },
          { text: 'JavaScript', delay: 0.6, position: { top: '70%', left: '25%' }, factor: { x: 0.3, y: 0.2 } },
          { text: 'Node.js', delay: 0.7, position: { top: '45%', left: '75%' }, factor: { x: -0.4, y: -0.3 } },
          { text: 'TypeScript', delay: 0.8, position: { top: '65%', left: '60%' }, factor: { x: 0.1, y: -0.5 } },
          { text: 'Next.js', delay: 0.9, position: { top: '15%', left: '40%' }, factor: { x: -0.3, y: 0.3 } },
          { text: 'MongoDB', delay: 1.0, position: { top: '80%', left: '45%' }, factor: { x: 0.4, y: 0.1 } },
          { text: 'GraphQL', delay: 0.5, position: { top: '30%', left: '80%' }, factor: { x: -0.5, y: -0.2 } },
          { text: 'Firebase', delay: 0.6, position: { top: '55%', left: '10%' }, factor: { x: 0.2, y: 0.5 } },
          { text: 'AWS', delay: 0.7, position: { top: '25%', left: '30%' }, factor: { x: -0.1, y: -0.4 } },
          { text: 'Redux', delay: 0.8, position: { top: '60%', left: '35%' }, factor: { x: 0.3, y: -0.1 } },
          { text: 'Express', delay: 0.9, position: { top: '40%', left: '55%' }, factor: { x: -0.2, y: 0.2 } },
          { text: 'Vue.js', delay: 1.0, position: { top: '75%', left: '70%' }, factor: { x: 0.4, y: -0.3 } },
          { text: 'PostgreSQL', delay: 0.4, position: { top: '50%', left: '20%' }, factor: { x: -0.3, y: 0.4 } },
          { text: 'Docker', delay: 0.5, position: { top: '10%', left: '55%' }, factor: { x: 0.2, y: -0.2 } },
        ].map((tag, index) => (
          <motion.div
            key={index}
            className={`${isMobile ? '' : 'absolute'} ${tag.special
              ? 'bg-gradient-to-r from-purple-600/80 to-blue-500/80'
              : 'bg-white/5'} backdrop-blur-sm ${isMobile ? 'px-2 py-1 text-xs' : 'px-4 py-2'} rounded-lg border ${tag.special
                ? 'border-white/30'
                : 'border-white/10'} shadow-sm tech-tag-behind pointer-events-auto ${isMobile ? 'my-1 mx-1' : ''} will-change-transform`}
            style={isMobile ? {} : tag.position}
            data-tag-factor-x={tag.factor.x}
            data-tag-factor-y={tag.factor.y}
            initial={{ opacity: 0, scale: 0.8, rotate: tag.special ? -3 : 0 }}
            animate={{
              opacity: tag.special ? 0.9 : 0.7,
              scale: 1,
              rotate: tag.special ? -3 : 0,
              transition: { delay: tag.delay, duration: 0.5 }
            }}
            // Only enable hover effects on desktop
            {...(!isMobile && {
              whileHover: {
                zIndex: 30,
                scale: 1.2,
                opacity: 1,
                rotate: 0,
                backgroundColor: tag.special
                  ? 'rgba(139, 92, 246, 0.8)'
                  : 'rgba(255, 255, 255, 0.2)',
                y: -10,
                transition: { duration: 0.2 }
              }
            })}
            // Only enable drag on desktop
            {...(!isMobile && {
              drag: true,
              dragConstraints: {
                top: -50,
                left: -50,
                right: 50,
                bottom: 50,
              },
              dragElastic: 0.1
            })}
          >
            {tag.special && (
              <motion.div
                className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-300 rounded-full"
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{
                  repeat: Infinity,
                  duration: 2,
                  ease: "easeInOut"
                }}
              />
            )}
            <span className={`${tag.special ? 'text-white' : 'text-white'} font-mono ${isMobile ? 'text-xs' : 'text-sm'} font-medium`}>{tag.text}</span>
          </motion.div>
        ))}
      </div>

      {/* Content with animations - pointer-events-none to allow interaction with tags behind */}
      <div className="container mx-auto px-4 md:px-6  relative z-10 pointer-events-none">
        <motion.div
          className="max-w-4xl mx-auto text-center"
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          <motion.h1
            className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight relative"
            variants={itemVariants}
          >
            Web Services That <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-500">Transform</span> Your Business
          </motion.h1>

          <motion.p
            className="text-xl md:text-2xl text-gray-300 mb-10 max-w-3xl mx-auto"
            variants={itemVariants}
          >
            We design and develop stunning websites and web applications that drive growth and deliver exceptional user experiences.
          </motion.p>

          <motion.div
            className="flex justify-center items-center"
            variants={itemVariants}
          >
            <motion.button
              onClick={() => navigate('/consultation')}
              className="bg-gradient-to-r from-purple-600 to-blue-500 text-white px-8 py-3 rounded-full text-lg font-medium hover:shadow-lg hover:shadow-purple-500/20 transition-all pointer-events-auto"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Get Started
            </motion.button>
          </motion.div>
        </motion.div>
      </div>

      {/* Curved Loop at the bottom */}
      <div className="absolute bottom-0 left-0 right-0 z-20 pointer-events-auto">
        <CurvedLoop
          marqueeText="INNOVATIVE SOLUTIONS • STUDENT DEVELOPERS • AFFORDABLE PRICING • QUALITY RESULTS • "
          speed={1.5}
          curveAmount={150}
          direction="left"
          interactive={true}
          className="fill-white/90"
        />
      </div>
    </section>
  );
};

export default Hero;
