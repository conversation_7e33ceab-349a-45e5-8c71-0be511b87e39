import { useRef, useEffect } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import { Parallax } from 'react-scroll-parallax';
import { Component } from "./ui/infinite-menu";
import { TestimonialsColumn } from "./ui/testimonials-columns-1";

const Portfolio = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.1 });
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);

  // Project data for the infinite menu
  const projects = [
    {
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop",
      link: "#",
      title: "E-Commerce Platform",
      description: "Modern e-commerce solution with advanced features and seamless user experience"
    },
    {
      image: "https://images.unsplash.com/photo-**********-87deedd944c3?w=800&h=600&fit=crop",
      link: "#",
      title: "SaaS Dashboard",
      description: "Comprehensive analytics dashboard for business intelligence and data visualization"
    },
    {
      image: "https://images.unsplash.com/photo-**********-491a97ff2040?w=800&h=600&fit=crop",
      link: "#",
      title: "Mobile Banking App",
      description: "Secure and intuitive mobile banking application with modern UI/UX design"
    },
    {
      image: "https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=600&fit=crop",
      link: "#",
      title: "Healthcare Portal",
      description: "Patient management system with appointment scheduling and medical records"
    },
    {
      image: "https://images.unsplash.com/photo-**********-824ae1b704d3?w=800&h=600&fit=crop",
      link: "#",
      title: "Learning Management",
      description: "Educational platform with course management and interactive learning tools"
    },
    {
      image: "https://images.unsplash.com/photo-**********-e076c223a692?w=800&h=600&fit=crop",
      link: "#",
      title: "Real Estate Platform",
      description: "Property listing and management system with virtual tours and analytics"
    }
  ];

  // Client testimonials for the columns
  const testimonials = [
    {
      text: "Intelermate delivered an exceptional e-commerce platform that increased our sales by 150%. Their attention to detail and user experience is outstanding.",
      image: "https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face",
      name: "Priya Sharma",
      role: "CEO, ShopSmart India",
    },
    {
      text: "The SaaS dashboard they built transformed how we analyze our business data. The interface is intuitive and the performance is excellent.",
      image: "https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      name: "Rajesh Kumar",
      role: "CTO, DataFlow Solutions",
    },
    {
      text: "Our mobile banking app has received incredible user feedback. Intelermate's team understood our security requirements perfectly.",
      image: "https://images.unsplash.com/photo-*************-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      name: "Sana Sheikh",
      role: "Product Manager, FinTech Pro",
    },
    {
      text: "The healthcare portal streamlined our patient management process. The system is robust, secure, and user-friendly.",
      image: "https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      name: "Dr. Vikram Mehta",
      role: "Director, MediCare Plus",
    },
    {
      text: "Our learning management platform has helped thousands of students. The interactive features and performance are exceptional.",
      image: "https://images.unsplash.com/photo-*************-53994a69daeb?w=150&h=150&fit=crop&crop=face",
      name: "Zainab Hussain",
      role: "Academic Director, EduTech",
    },
    {
      text: "The real estate platform revolutionized how we showcase properties. The virtual tour feature is a game-changer for our business.",
      image: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face",
      name: "Arjun Patel",
      role: "Founder, PropertyHub",
    }
  ];

  const firstColumn = testimonials.slice(0, 2);
  const secondColumn = testimonials.slice(2, 4);
  const thirdColumn = testimonials.slice(4, 6);

  return (
    <section ref={sectionRef} id="portfolio" className="py-20 bg-black relative overflow-hidden">
      {/* Background gradient effects */}
      <div className="absolute inset-0 overflow-hidden">
        <Parallax translateY={[-20, 20]} className="absolute top-1/4 right-1/4 w-96 h-96 bg-purple-600/10 rounded-full filter blur-3xl"></Parallax>
        <Parallax translateY={[20, -20]} className="absolute bottom-1/3 left-1/3 w-80 h-80 bg-blue-600/10 rounded-full filter blur-3xl"></Parallax>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        {/* Header Section */}
        <motion.div
          className="text-center mb-16"
          initial="hidden"
          animate={controls}
          variants={{
            hidden: { opacity: 0 },
            visible: { opacity: 1 }
          }}
        >
          <motion.div
            className="flex justify-center mb-6"
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: { delay: 0.1, duration: 0.6 }
              }
            }}
          >
            <div className="border border-purple-500/30 py-1 px-4 rounded-lg bg-purple-500/10 text-purple-300">
              Portfolio
            </div>
          </motion.div>

          <motion.h2
            className="text-3xl md:text-4xl font-bold text-white mb-4 relative"
            variants={{
              hidden: { opacity: 0, y: -20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: {
                  type: 'spring',
                  stiffness: 100,
                  damping: 10,
                  delay: 0.2
                }
              }
            }}
          >
            Our <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-500">Projects</span>

            {/* Featured Work tag */}
            <motion.div
              className="md:absolute md:-top-10 md:right-0 hidden md:block z-10 transform -rotate-6"
              initial={{ opacity: 0, y: 20, scale: 0.8 }}
              animate={isInView ? {
                opacity: 1,
                y: 0,
                scale: 1,
                transition: {
                  type: 'spring',
                  stiffness: 300,
                  damping: 15,
                  delay: 0.5
                }
              } : {}}
              whileHover={{
                scale: 1.05,
                rotate: 0,
                transition: { duration: 0.2 }
              }}
            >
              <div className="bg-gradient-to-r from-purple-600 to-blue-500 text-white px-2 py-1 md:px-4 md:py-2 rounded-lg shadow-lg relative overflow-hidden scale-75 md:scale-100">
                <span className="relative z-10 font-bold text-xs md:text-sm">Featured Work!</span>

                {/* Animated dots/sparkles */}
                <motion.div
                  className="absolute top-1 right-1 w-1 h-1 bg-white rounded-full"
                  animate={{
                    opacity: [0, 1, 0],
                    scale: [0.8, 1.2, 0.8],
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 2,
                    ease: "easeInOut"
                  }}
                />
                <motion.div
                  className="absolute bottom-1 left-2 w-1 h-1 bg-white rounded-full"
                  animate={{
                    opacity: [0, 1, 0],
                    scale: [0.8, 1.2, 0.8],
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 1.5,
                    delay: 0.5,
                    ease: "easeInOut"
                  }}
                />
              </div>
            </motion.div>
          </motion.h2>

          <motion.p
            className="text-xl text-gray-300 max-w-3xl mx-auto"
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: { delay: 0.3, duration: 0.6 }
              }
            }}
          >
            Explore our featured projects and see what our clients say about working with our talented student developers.
          </motion.p>
        </motion.div>

        {/* 3D Project Showcase - Full Screen */}
        <motion.div
          className="mb-20 -mx-4 md:-mx-6"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <div className="w-screen relative left-1/2 right-1/2 -ml-[50vw] -mr-[50vw]">
            <div
              className="w-full relative"
              style={{
                height: "100vh",
                minHeight: "600px",
                maxHeight: "100vh"
              }}
            >
              <Component items={projects} />
            </div>
          </div>
        </motion.div>

        {/* Client Testimonials Columns */}
        <motion.div
          className="mt-16"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
              What Our <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-500">Clients</span> Say
            </h3>
            <p className="text-gray-300 max-w-2xl mx-auto">
              Real feedback from clients who trusted us with their projects
            </p>
          </div>

          <div className="flex justify-center gap-6 [mask-image:linear-gradient(to_bottom,transparent,black_25%,black_75%,transparent)] max-h-[500px] overflow-hidden">
            <TestimonialsColumn testimonials={firstColumn} duration={20} />
            <TestimonialsColumn testimonials={secondColumn} className="hidden md:block" duration={25} />
            <TestimonialsColumn testimonials={thirdColumn} className="hidden lg:block" duration={22} />
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Portfolio;