{"version": 3, "sources": ["../../bezier-easing/src/index.js", "../../parallax-controller/src/classes/Limits.ts", "../../parallax-controller/src/types.ts", "../../parallax-controller/src/utils/createId.ts", "../../parallax-controller/src/classes/Rect.ts", "../../parallax-controller/src/utils/parseValueAndUnit.ts", "../../parallax-controller/src/constants.ts", "../../parallax-controller/src/helpers/createEasingFunction.ts", "../../parallax-controller/src/helpers/parseElementTransitionEffects.ts", "../../parallax-controller/src/helpers/getProgressAmount.ts", "../../parallax-controller/src/helpers/isElementInView.ts", "../../parallax-controller/src/utils/scaleBetween.ts", "../../parallax-controller/src/helpers/scaleEffectByProgress.ts", "../../parallax-controller/src/helpers/elementStyles.ts", "../../parallax-controller/src/helpers/createLimitsForRelativeElements.ts", "../../parallax-controller/src/helpers/getTranslateScalar.ts", "../../parallax-controller/src/helpers/getStartEndValueInPx.ts", "../../parallax-controller/src/helpers/createLimitsWithTranslationsForRelativeElements.ts", "../../parallax-controller/src/helpers/scaleTranslateEffectsForSlowerScroll.ts", "../../parallax-controller/src/helpers/getShouldScaleTranslateEffects.ts", "../../parallax-controller/src/helpers/clamp.ts", "../../parallax-controller/src/classes/Element.ts", "../../parallax-controller/src/classes/View.ts", "../../parallax-controller/src/classes/Scroll.ts", "../../parallax-controller/src/utils/testForPassiveScroll.ts", "../../parallax-controller/src/classes/ParallaxController.ts", "../../react-scroll-parallax/src/utils/removeUndefinedObjectKeys.ts", "../../react-scroll-parallax/src/helpers/getIsolatedParallaxProps.ts", "../../react-scroll-parallax/src/components/Parallax/hooks.ts", "../../react-scroll-parallax/src/context/ParallaxContext.ts", "../../react-scroll-parallax/src/hooks/useParallaxController.ts", "../../react-scroll-parallax/src/hooks/useParallax.ts", "../../react-scroll-parallax/src/components/Parallax/Parallax.tsx", "../../react-scroll-parallax/src/components/ParallaxBanner/helpers/getExpandedStyle.ts", "../../react-scroll-parallax/src/components/ParallaxBanner/helpers/getImageStyle.ts", "../../react-scroll-parallax/src/components/ParallaxBanner/components/ParallaxBannerLayer.tsx", "../../react-scroll-parallax/src/components/ParallaxBanner/ParallaxBanner.tsx", "../../react-scroll-parallax/src/components/ParallaxProvider/helpers.ts", "../../react-scroll-parallax/src/components/ParallaxProvider/ParallaxProvider.tsx"], "sourcesContent": ["/**\n * https://github.com/gre/bezier-easing\n * BezierEasing - use bezier curve for transition easing function\n * by <PERSON><PERSON><PERSON><PERSON> 2014 - 2015 – MIT License\n */\n\n// These values are established by empiricism with tests (tradeoff: performance VS precision)\nvar NEWTON_ITERATIONS = 4;\nvar NEWTON_MIN_SLOPE = 0.001;\nvar SUBDIVISION_PRECISION = 0.0000001;\nvar SUBDIVISION_MAX_ITERATIONS = 10;\n\nvar kSplineTableSize = 11;\nvar kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);\n\nvar float32ArraySupported = typeof Float32Array === 'function';\n\nfunction A (aA1, aA2) { return 1.0 - 3.0 * aA2 + 3.0 * aA1; }\nfunction B (aA1, aA2) { return 3.0 * aA2 - 6.0 * aA1; }\nfunction C (aA1)      { return 3.0 * aA1; }\n\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nfunction calcBezier (aT, aA1, aA2) { return ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT; }\n\n// Returns dx/dt given t, x1, and x2, or dy/dt given t, y1, and y2.\nfunction getSlope (aT, aA1, aA2) { return 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1); }\n\nfunction binarySubdivide (aX, aA, aB, mX1, mX2) {\n  var currentX, currentT, i = 0;\n  do {\n    currentT = aA + (aB - aA) / 2.0;\n    currentX = calcBezier(currentT, mX1, mX2) - aX;\n    if (currentX > 0.0) {\n      aB = currentT;\n    } else {\n      aA = currentT;\n    }\n  } while (Math.abs(currentX) > SUBDIVISION_PRECISION && ++i < SUBDIVISION_MAX_ITERATIONS);\n  return currentT;\n}\n\nfunction newtonRaphsonIterate (aX, aGuessT, mX1, mX2) {\n for (var i = 0; i < NEWTON_ITERATIONS; ++i) {\n   var currentSlope = getSlope(aGuessT, mX1, mX2);\n   if (currentSlope === 0.0) {\n     return aGuessT;\n   }\n   var currentX = calcBezier(aGuessT, mX1, mX2) - aX;\n   aGuessT -= currentX / currentSlope;\n }\n return aGuessT;\n}\n\nfunction LinearEasing (x) {\n  return x;\n}\n\nmodule.exports = function bezier (mX1, mY1, mX2, mY2) {\n  if (!(0 <= mX1 && mX1 <= 1 && 0 <= mX2 && mX2 <= 1)) {\n    throw new Error('bezier x values must be in [0, 1] range');\n  }\n\n  if (mX1 === mY1 && mX2 === mY2) {\n    return LinearEasing;\n  }\n\n  // Precompute samples table\n  var sampleValues = float32ArraySupported ? new Float32Array(kSplineTableSize) : new Array(kSplineTableSize);\n  for (var i = 0; i < kSplineTableSize; ++i) {\n    sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);\n  }\n\n  function getTForX (aX) {\n    var intervalStart = 0.0;\n    var currentSample = 1;\n    var lastSample = kSplineTableSize - 1;\n\n    for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {\n      intervalStart += kSampleStepSize;\n    }\n    --currentSample;\n\n    // Interpolate to provide an initial guess for t\n    var dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);\n    var guessForT = intervalStart + dist * kSampleStepSize;\n\n    var initialSlope = getSlope(guessForT, mX1, mX2);\n    if (initialSlope >= NEWTON_MIN_SLOPE) {\n      return newtonRaphsonIterate(aX, guessForT, mX1, mX2);\n    } else if (initialSlope === 0.0) {\n      return guessForT;\n    } else {\n      return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);\n    }\n  }\n\n  return function BezierEasing (x) {\n    // Because JavaScript number are imprecise, we should guarantee the extremes are right.\n    if (x === 0) {\n      return 0;\n    }\n    if (x === 1) {\n      return 1;\n    }\n    return calcBezier(getTForX(x), mY1, mY2);\n  };\n};\n", "export type LimitOptions = {\n  startX: number;\n  startY: number;\n  endX: number;\n  endY: number;\n  startMultiplierX?: number;\n  endMultiplierX?: number;\n  startMultiplierY?: number;\n  endMultiplierY?: number;\n};\n\nexport class Limits {\n  startX: number;\n  startY: number;\n  endX: number;\n  endY: number;\n  totalX: number;\n  totalY: number;\n  startMultiplierX: number;\n  endMultiplierX: number;\n  startMultiplierY: number;\n  endMultiplierY: number;\n\n  constructor(properties: LimitOptions) {\n    this.startX = properties.startX;\n    this.startY = properties.startY;\n    this.endX = properties.endX;\n    this.endY = properties.endY;\n    // Used to calculate the progress of the element\n    this.totalX = this.endX - this.startX;\n    this.totalY = this.endY - this.startY;\n\n    // Used to scale translate effects\n    this.startMultiplierX = properties.startMultiplierX || 1;\n    this.endMultiplierX = properties.endMultiplierX || 1;\n    this.startMultiplierY = properties.startMultiplierY || 1;\n    this.endMultiplierY = properties.endMultiplierY || 1;\n  }\n}\n", "import { EasingFunction } from 'bezier-easing';\nimport { Element } from './classes/Element';\n\nexport type ParallaxStartEndEffects = {\n  translateX?: ParsedValueEffect;\n  translateY?: ParsedValueEffect;\n  rotate?: ParsedValueEffect;\n  rotateX?: ParsedValueEffect;\n  rotateY?: ParsedValueEffect;\n  rotateZ?: ParsedValueEffect;\n  scale?: ParsedValueEffect;\n  scaleX?: ParsedValueEffect;\n  scaleY?: ParsedValueEffect;\n  scaleZ?: ParsedValueEffect;\n  opacity?: ParsedValueEffect;\n};\n\nexport enum ValidCSSEffects {\n  'speed' = 'speed',\n  'translateX' = 'translateX',\n  'translateY' = 'translateY',\n  'rotate' = 'rotate',\n  'rotateX' = 'rotateX',\n  'rotateY' = 'rotateY',\n  'rotateZ' = 'rotateZ',\n  'scale' = 'scale',\n  'scaleX' = 'scaleX',\n  'scaleY' = 'scaleY',\n  'scaleZ' = 'scaleZ',\n  'opacity' = 'opacity',\n}\n\nexport enum Units {\n  'px' = 'px',\n  '%' = '%',\n  'vh' = 'vh',\n  'vw' = 'vw',\n}\nexport type ValidUnits = keyof typeof Units;\n\nexport enum RotationUnits {\n  'deg' = 'deg',\n  'turn' = 'turn',\n  'rad' = 'rad',\n}\n\nexport enum ScaleUnits {\n  '' = '',\n}\n\nexport type ValidScaleUnits = keyof typeof ScaleUnits;\n\nexport type ValidRotationUnits = keyof typeof RotationUnits;\n\nexport type AllValidUnits = ValidUnits | ValidRotationUnits | ValidScaleUnits;\n\nexport enum ScrollAxis {\n  'vertical' = 'vertical',\n  'horizontal' = 'horizontal',\n}\n\nexport type ValidScrollAxis = keyof typeof ScrollAxis;\n\nexport type ParsedValueShape = {\n  value: number;\n  unit: AllValidUnits;\n};\n\nexport type ParsedValueEffect = {\n  start: number;\n  end: number;\n  unit: AllValidUnits;\n  easing?: EasingFunction;\n};\n\nexport type ViewElement = HTMLElement | Window;\nexport type ParallaxControllerOptions = {\n  scrollAxis?: ValidScrollAxis;\n  scrollContainer?: HTMLElement;\n  disabled?: boolean;\n};\n\nexport type EffectNumber = [number, number, EasingParam?];\nexport type EffectString = [string, string, EasingParam?];\nexport type EasingParam = ValidEasingPresets | EasingParams;\nexport type CSSEffect = EffectNumber | EffectString;\nexport type ScaleOpacityEffect = EffectNumber;\n\nexport type ParallaxElementConfig = {\n  speed?: number;\n  disabled?: boolean;\n  translateX?: CSSEffect;\n  translateY?: CSSEffect;\n  rotate?: CSSEffect;\n  rotateX?: CSSEffect;\n  rotateY?: CSSEffect;\n  rotateZ?: CSSEffect;\n  scale?: ScaleOpacityEffect;\n  scaleX?: ScaleOpacityEffect;\n  scaleY?: ScaleOpacityEffect;\n  scaleZ?: ScaleOpacityEffect;\n  opacity?: ScaleOpacityEffect;\n  easing?: EasingParams | ValidEasingPresets;\n  rootMargin?: RootMarginShape;\n  /* Always start and end animations at the given effect values - if the element is positioned inside the view when scroll is at zero or ends in view at final scroll position, the initial and final positions are used to determine progress instead of the scroll view size */\n  shouldAlwaysCompleteAnimation?: boolean;\n  /* Disable scaling translations - translate effects that cause the element to appear in the view longer must be scaled up so that animation doesn't end early */\n  shouldDisableScalingTranslations?: boolean;\n\n  startScroll?: number;\n  endScroll?: number;\n  targetElement?: HTMLElement;\n\n  onEnter?: (element: Element) => any;\n  onExit?: (element: Element) => any;\n  onChange?: (element: Element) => any;\n  onProgressChange?: (progress: number) => any;\n};\n\nexport type CreateElementOptions = {\n  el: HTMLElement;\n  props: ParallaxElementConfig;\n};\n\nexport type EasingParams = [number, number, number, number];\n\nexport enum EasingPreset {\n  ease = 'ease',\n  easeIn = 'easeIn',\n  easeOut = 'easeOut',\n  easeInOut = 'easeInOut',\n  easeInQuad = 'easeInQuad',\n  easeInCubic = 'easeInCubic',\n  easeInQuart = 'easeInQuart',\n  easeInQuint = 'easeInQuint',\n  easeInSine = 'easeInSine',\n  easeInExpo = 'easeInExpo',\n  easeInCirc = 'easeInCirc',\n  easeOutQuad = 'easeOutQuad',\n  easeOutCubic = 'easeOutCubic',\n  easeOutQuart = 'easeOutQuart',\n  easeOutQuint = 'easeOutQuint',\n  easeOutSine = 'easeOutSine',\n  easeOutExpo = 'easeOutExpo',\n  easeOutCirc = 'easeOutCirc',\n  easeInOutQuad = 'easeInOutQuad',\n  easeInOutCubic = 'easeInOutCubic',\n  easeInOutQuart = 'easeInOutQuart',\n  easeInOutQuint = 'easeInOutQuint',\n  easeInOutSine = 'easeInOutSine',\n  easeInOutExpo = 'easeInOutExpo',\n  easeInOutCirc = 'easeInOutCirc',\n  easeInBack = 'easeInBack',\n  easeOutBack = 'easeOutBack',\n  easeInOutBack = 'easeInOutBack',\n}\n\nexport type ValidEasingPresets = keyof typeof EasingPreset;\n\nexport type RootMarginShape = {\n  top: number;\n  bottom: number;\n  left: number;\n  right: number;\n};\n", "/**\n * Creates a unique id to distinguish parallax elements.\n */\n\nlet id = 0;\n\nexport function createId(): number {\n  ++id;\n  return id;\n}\n", "import { View } from './View';\nimport { RootMarginShape } from '../types';\n\nexport class Rect {\n  height: number;\n  width: number;\n  left: number;\n  right: number;\n  top: number;\n  bottom: number;\n\n  constructor(options: {\n    el: HTMLElement;\n    view: View;\n    rootMargin?: RootMarginShape;\n  }) {\n    let rect = options.el.getBoundingClientRect();\n\n    // rect is based on viewport -- must adjust for relative scroll container\n    if (options.view.scrollContainer) {\n      const scrollRect = options.view.scrollContainer.getBoundingClientRect();\n      rect = {\n        ...rect,\n        top: rect.top - scrollRect.top,\n        right: rect.right - scrollRect.left,\n        bottom: rect.bottom - scrollRect.top,\n        left: rect.left - scrollRect.left,\n      };\n    }\n    this.height = options.el.offsetHeight;\n    this.width = options.el.offsetWidth;\n    this.left = rect.left;\n    this.right = rect.right;\n    this.top = rect.top;\n    this.bottom = rect.bottom;\n\n    if (options.rootMargin) {\n      this._setRectWithRootMargin(options.rootMargin);\n    }\n  }\n\n  /**\n   * Apply root margin to all properties\n   */\n  _setRectWithRootMargin(rootMargin: RootMarginShape) {\n    let totalRootY = rootMargin.top + rootMargin.bottom;\n    let totalRootX = rootMargin.left + rootMargin.right;\n    this.top -= rootMargin.top;\n    this.right += rootMargin.right;\n    this.bottom += rootMargin.bottom;\n    this.left -= rootMargin.left;\n    this.height += totalRootY;\n    this.width += totalRootX;\n  }\n}\n", "import {\n  ScaleUnits,\n  ParsedValueShape,\n  RotationUnits,\n  Units,\n  AllValidUnits,\n} from '../types';\n\nexport const VALID_UNITS = [\n  ScaleUnits[''],\n  Units.px,\n  Units['%'],\n  Units['vh'],\n  Units['vw'],\n  RotationUnits.deg,\n  RotationUnits.turn,\n  RotationUnits.rad,\n];\n\n/**\n * Determines the unit of a string and parses the value\n */\n\nexport function parseValueAndUnit(\n  str?: string | number,\n  defaultUnit: AllValidUnits = Units['%']\n): ParsedValueShape {\n  let out: ParsedValueShape = { value: 0, unit: defaultUnit };\n\n  if (typeof str === 'undefined') return out;\n\n  const isValid = typeof str === 'number' || typeof str === 'string';\n\n  if (!isValid) {\n    throw new Error(\n      'Invalid value provided. Must provide a value as a string or number'\n    );\n  }\n\n  str = String(str);\n  out.value = parseFloat(str);\n\n  // @ts-ignore\n  out.unit = str.match(/[\\d.\\-+]*\\s*(.*)/)[1] || defaultUnit;\n\n  // @ts-expect-error\n  const isValidUnit: boolean = VALID_UNITS.includes(out.unit);\n\n  if (!isValidUnit) {\n    throw new Error('Invalid unit provided.');\n  }\n\n  return out;\n}\n", "import { ValidEasingPresets } from './types';\n\nexport type EasingPreset = { [key in ValidEasingPresets]: number[] };\n\nexport const easingPresets: EasingPreset = {\n  ease: [0.25, 0.1, 0.25, 1.0],\n  easeIn: [0.42, 0.0, 1.0, 1.0],\n  easeOut: [0.0, 0.0, 0.58, 1.0],\n  easeInOut: [0.42, 0.0, 0.58, 1.0],\n  /* Ease IN curves */\n  easeInQuad: [0.55, 0.085, 0.68, 0.53],\n  easeInCubic: [0.55, 0.055, 0.675, 0.19],\n  easeInQuart: [0.895, 0.03, 0.685, 0.22],\n  easeInQuint: [0.755, 0.05, 0.855, 0.06],\n  easeInSine: [0.47, 0.0, 0.745, 0.715],\n  easeInExpo: [0.95, 0.05, 0.795, 0.035],\n  easeInCirc: [0.6, 0.04, 0.98, 0.335],\n  /* Ease Out Curves */\n  easeOutQuad: [0.25, 0.46, 0.45, 0.94],\n  easeOutCubic: [0.215, 0.61, 0.355, 1.0],\n  easeOutQuart: [0.165, 0.84, 0.44, 1.0],\n  easeOutQuint: [0.23, 1.0, 0.32, 1.0],\n  easeOutSine: [0.39, 0.575, 0.565, 1.0],\n  easeOutExpo: [0.19, 1.0, 0.22, 1.0],\n  easeOutCirc: [0.075, 0.82, 0.165, 1.0],\n  /* Ease IN Out Curves */\n  easeInOutQuad: [0.455, 0.03, 0.515, 0.955],\n  easeInOutCubic: [0.645, 0.045, 0.355, 1.0],\n  easeInOutQuart: [0.77, 0.0, 0.175, 1.0],\n  easeInOutQuint: [0.86, 0.0, 0.07, 1.0],\n  easeInOutSine: [0.445, 0.05, 0.55, 0.95],\n  easeInOutExpo: [1.0, 0.0, 0.0, 1.0],\n  easeInOutCirc: [0.785, 0.135, 0.15, 0.86],\n  /* Ease Bounce Curves */\n  easeInBack: [0.6, -0.28, 0.735, 0.045],\n  easeOutBack: [0.175, 0.885, 0.32, 1.275],\n  easeInOutBack: [0.68, -0.55, 0.265, 1.55],\n};\n", "import bezier, { EasingFunction } from 'bezier-easing';\nimport { ValidEasingPresets, EasingParams } from '../types';\nimport { easingPresets } from '../constants';\n\nexport function createEasingFunction(\n  easing: ValidEasingPresets | EasingParams | undefined\n): EasingFunction | undefined {\n  if (Array.isArray(easing)) {\n    return bezier(easing[0], easing[1], easing[2], easing[3]);\n  }\n  if (\n    typeof easing === 'string' &&\n    typeof easingPresets[easing] !== 'undefined'\n  ) {\n    const params: number[] = easingPresets[easing];\n    return bezier(params[0], params[1], params[2], params[3]);\n  }\n  return;\n}\n", "import { CSSEffect, ScrollAxis, ValidScrollAxis } from '..';\nimport {\n  ParsedValueEffect,\n  ValidCSSEffects,\n  ParallaxElementConfig,\n  ParallaxStartEndEffects,\n  AllValidUnits,\n} from '../types';\nimport { parseValueAndUnit } from '../utils/parseValueAndUnit';\nimport { createEasingFunction } from './createEasingFunction';\n\nexport const PARALLAX_EFFECTS = Object.values(ValidCSSEffects);\n\nexport const MAP_EFFECT_TO_DEFAULT_UNIT: {\n  [key in ValidCSSEffects]: AllValidUnits;\n} = {\n  speed: 'px',\n  translateX: '%',\n  translateY: '%',\n  rotate: 'deg',\n  rotateX: 'deg',\n  rotateY: 'deg',\n  rotateZ: 'deg',\n  scale: '',\n  scaleX: '',\n  scaleY: '',\n  scaleZ: '',\n  opacity: '',\n};\n/**\n * Takes a parallax element effects and parses the properties to get the start and end values and units.\n */\nexport function parseElementTransitionEffects(\n  props: ParallaxElementConfig,\n  scrollAxis: ValidScrollAxis\n): ParallaxStartEndEffects {\n  const parsedEffects: { [key: string]: ParsedValueEffect } = {};\n\n  PARALLAX_EFFECTS.forEach((key: keyof typeof ValidCSSEffects) => {\n    const defaultValue: AllValidUnits = MAP_EFFECT_TO_DEFAULT_UNIT[key];\n\n    // If the provided type is a number, this must be the speed prop\n    // in which case we need to construct the proper translate config\n    if (typeof props?.[key] === 'number') {\n      const value = props?.[key] as number;\n      const startSpeed = `${(value || 0) * 10}px`;\n      const endSpeed = `${(value || 0) * -10}px`;\n\n      const startParsed = parseValueAndUnit(startSpeed);\n      const endParsed = parseValueAndUnit(endSpeed);\n\n      const speedConfig = {\n        start: startParsed.value,\n        end: endParsed.value,\n        unit: startParsed.unit,\n      };\n\n      // Manually set translate y value\n      if (scrollAxis === ScrollAxis.vertical) {\n        parsedEffects.translateY = speedConfig;\n      }\n\n      // Manually set translate y value\n      if (scrollAxis === ScrollAxis.horizontal) {\n        parsedEffects.translateX = speedConfig;\n      }\n    }\n\n    // The rest are standard effect being parsed\n    if (Array.isArray(props?.[key])) {\n      const value = props?.[key] as CSSEffect;\n\n      if (typeof value[0] !== 'undefined' && typeof value[1] !== 'undefined') {\n        const startParsed = parseValueAndUnit(value?.[0], defaultValue);\n        const endParsed = parseValueAndUnit(value?.[1], defaultValue);\n\n        const easing = createEasingFunction(value?.[2]);\n\n        parsedEffects[key] = {\n          start: startParsed.value,\n          end: endParsed.value,\n          unit: startParsed.unit,\n          easing,\n        };\n\n        if (startParsed.unit !== endParsed.unit) {\n          throw new Error(\n            'Must provide matching units for the min and max offset values of each axis.'\n          );\n        }\n      }\n    }\n  });\n\n  return parsedEffects;\n}\n", "import bezier from 'bezier-easing';\n\n/**\n * Returns the percent (0 - 100) moved based on position in the viewport\n */\n\nexport function getProgressAmount(\n  /*\n   * The start value from cache\n   */\n  start: number,\n  /*\n   * total dist the element has to move to be 100% complete (view width/height + element width/height)\n   */\n  totalDist: number,\n  /*\n   * Current scroll value\n   */\n  currentScroll: number,\n  /*\n   * an optional easing function to apply\n   */\n  easing?: bezier.EasingFunction\n): number {\n  // adjust cached value\n  const startAdjustedScroll = currentScroll - start;\n\n  // Amount the element has moved based on current and total distance to move\n  let amount = startAdjustedScroll / totalDist;\n\n  // Apply bezier easing if provided\n  if (easing) {\n    amount = easing(amount);\n  }\n\n  return amount;\n}\n", "/**\n * Takes two values (start, end) and returns whether the current scroll is within range\n * @param {number} start - start of scroll (x/y)\n * @param {number} end - end of scroll (x/y)\n * @param {number} scroll - current scroll (x/y)\n * @return {boolean} isInView\n */\n\nexport function isElementInView(\n  start: number,\n  end: number,\n  scroll: number\n): boolean {\n  const isInView = scroll >= start && scroll <= end;\n\n  return isInView;\n}\n", "// Scale between AKA normalize\nexport function scaleBetween(\n  value: number,\n  newMin: number,\n  newMax: number,\n  oldMin: number,\n  oldMax: number\n) {\n  return ((newMax - newMin) * (value - oldMin)) / (oldMax - oldMin) + newMin;\n}\n", "import { ParsedValueEffect } from '..';\nimport { AllValidUnits } from '../types';\nimport { scaleBetween } from '../utils/scaleBetween';\n\n/**\n * Scales a start and end value of an effect based on percent moved and easing function\n */\nexport function scaleEffectByProgress(\n  effect: ParsedValueEffect,\n  progress: number\n): {\n  value: number;\n  unit: AllValidUnits;\n} {\n  const value = scaleBetween(\n    typeof effect.easing === 'function' ? effect.easing(progress) : progress,\n    effect?.start || 0,\n    effect?.end || 0,\n    0,\n    1\n  );\n\n  return {\n    value,\n    unit: effect?.unit,\n  };\n}\n", "import { Element } from '../classes/Element';\nimport { ParallaxStartEndEffects, ValidCSSEffects } from '../types';\nimport { scaleEffectByProgress } from './scaleEffectByProgress';\n\n// Exclude opacity from transforms\nconst TRANSFORM_EFFECTS = Object.values(ValidCSSEffects).filter(\n  v => v !== 'opacity'\n);\n\nexport function setWillChangeStyles(\n  el: HTMLElement,\n  effects: ParallaxStartEndEffects\n) {\n  const keys = Object.keys(effects);\n  const hasOpacity = keys.includes('opacity');\n  const willChange = `transform${hasOpacity ? ',opacity' : ''}`;\n  el.style.willChange = willChange;\n}\n\nexport function setElementStyles(\n  effects: ParallaxStartEndEffects,\n  progress: number,\n  el?: HTMLElement\n) {\n  if (!el) return;\n  const transform = getTransformStyles(effects, progress);\n  const opacity = getOpacityStyles(effects, progress);\n  el.style.transform = transform;\n  el.style.opacity = opacity;\n}\n\nexport function getOpacityStyles(\n  effects: ParallaxStartEndEffects,\n  progress: number\n): string {\n  const scaledOpacity =\n    effects['opacity'] && scaleEffectByProgress(effects['opacity'], progress);\n\n  if (\n    typeof scaledOpacity === 'undefined' ||\n    typeof scaledOpacity.value === 'undefined' ||\n    typeof scaledOpacity.unit === 'undefined'\n  ) {\n    return '';\n  }\n\n  const styleStr = `${scaledOpacity.value}`;\n\n  return styleStr;\n}\n\nexport function getTransformStyles(\n  effects: ParallaxStartEndEffects,\n  progress: number\n): string {\n  const transform: string = TRANSFORM_EFFECTS.reduce((acc, key: string) => {\n    const scaledEffect =\n      // @ts-expect-error\n      effects[key] && scaleEffectByProgress(effects[key], progress);\n\n    if (\n      typeof scaledEffect === 'undefined' ||\n      typeof scaledEffect.value === 'undefined' ||\n      typeof scaledEffect.unit === 'undefined'\n    ) {\n      return acc;\n    }\n\n    const styleStr = `${key}(${scaledEffect.value}${scaledEffect.unit})`;\n\n    return acc + styleStr;\n  }, '');\n\n  return transform;\n}\n\n/**\n * Takes a parallax element and removes parallax offset styles.\n * @param {object} element\n */\nexport function resetStyles(element: Element) {\n  const el = element.el;\n  if (!el) return;\n  el.style.transform = '';\n  el.style.opacity = '';\n}\n", "import { Rect, Scroll, View } from '..';\nimport { Limits } from '../classes/Limits';\n\nexport function createLimitsForRelativeElements(\n  rect: Rect,\n  view: View,\n  scroll: Scroll,\n  shouldAlwaysCompleteAnimation?: boolean\n): Limits {\n  let startY = rect.top - view.height;\n  let startX = rect.left - view.width;\n  let endY = rect.bottom;\n  let endX = rect.right;\n\n  // add scroll\n  startX += scroll.x;\n  endX += scroll.x;\n  startY += scroll.y;\n  endY += scroll.y;\n\n  if (shouldAlwaysCompleteAnimation) {\n    if (scroll.y + rect.top < view.height) {\n      startY = 0;\n    }\n    if (scroll.x + rect.left < view.width) {\n      startX = 0;\n    }\n    if (endY > view.scrollHeight - view.height) {\n      endY = view.scrollHeight - view.height;\n    }\n    if (endX > view.scrollWidth - view.width) {\n      endX = view.scrollWidth - view.width;\n    }\n  }\n\n  const limits = new Limits({\n    startX,\n    startY,\n    endX,\n    endY,\n  });\n\n  return limits;\n}\n", "export function getTranslateScalar(\n  startTranslatePx: number,\n  endTranslatePx: number,\n  totalDist: number\n) {\n  const slow = endTranslatePx > startTranslatePx;\n\n  // calculating necessary scale to increase translations\n  const totalAbsOff =\n    (Math.abs(startTranslatePx) + Math.abs(endTranslatePx)) * (slow ? -1 : 1);\n  const totalDistTrue = totalDist + totalAbsOff;\n\n  // Determine multiple to scale by, only values greater than 1\n  const scale = Math.max(totalDist / totalDistTrue, 1);\n\n  return scale;\n}\n", "import { ParsedValueEffect } from '..';\n\n/**\n * Return the start and end pixel values for an elements translations\n */\nexport function getStartEndValueInPx(\n  translate: ParsedValueEffect,\n  elementSize: number\n) {\n  let { start, end, unit } = translate;\n\n  if (unit === '%') {\n    const scale = elementSize / 100;\n    start = start * scale;\n    end = end * scale;\n  }\n\n  if (unit === 'vw') {\n    const startScale = start / 100;\n    const endScale = end / 100;\n    start = window.innerWidth * startScale;\n    end = window.innerWidth * endScale;\n  }\n\n  if (unit === 'vh') {\n    const startScale = start / 100;\n    const endScale = end / 100;\n    start = window.innerHeight * startScale;\n    end = window.innerHeight * endScale;\n  }\n\n  return {\n    start,\n    end,\n  };\n}\n", "import { ParsedValueEffect } from '../types';\nimport { Rect } from '../classes/Rect';\nimport { View } from '../classes/View';\nimport { Limits } from '../classes/Limits';\nimport { Scroll } from '../classes/Scroll';\n\nimport { getTranslateScalar } from './getTranslateScalar';\nimport { getStartEndValueInPx } from './getStartEndValueInPx';\nimport { ParallaxStartEndEffects, ScrollAxis, ValidScrollAxis } from '../types';\n\nconst DEFAULT_VALUE: ParsedValueEffect = {\n  start: 0,\n  end: 0,\n  unit: '',\n};\n\nexport function createLimitsWithTranslationsForRelativeElements(\n  rect: Rect,\n  view: View,\n  effects: ParallaxStartEndEffects,\n  scroll: Scroll,\n  scrollAxis: ValidScrollAxis,\n  shouldAlwaysCompleteAnimation?: boolean\n): Limits {\n  // get start and end accounting for percent effects\n  const translateX: ParsedValueEffect = effects.translateX || DEFAULT_VALUE;\n  const translateY: ParsedValueEffect = effects.translateY || DEFAULT_VALUE;\n\n  const {\n    start: startTranslateXPx,\n    end: endTranslateXPx,\n  } = getStartEndValueInPx(translateX, rect.width);\n  const {\n    start: startTranslateYPx,\n    end: endTranslateYPx,\n  } = getStartEndValueInPx(translateY, rect.height);\n\n  // default starting values\n  let startY = rect.top - view.height;\n  let startX = rect.left - view.width;\n  let endY = rect.bottom;\n  let endX = rect.right;\n\n  let startMultiplierY = 1;\n  let endMultiplierY = 1;\n  if (scrollAxis === ScrollAxis.vertical) {\n    startMultiplierY = getTranslateScalar(\n      startTranslateYPx,\n      endTranslateYPx,\n      view.height + rect.height\n    );\n    endMultiplierY = startMultiplierY;\n  }\n  let startMultiplierX = 1;\n  let endMultiplierX = 1;\n  if (scrollAxis === ScrollAxis.horizontal) {\n    startMultiplierX = getTranslateScalar(\n      startTranslateXPx,\n      endTranslateXPx,\n      view.width + rect.width\n    );\n    endMultiplierX = startMultiplierX;\n  }\n\n  // Apply the scale to initial values\n  if (startTranslateYPx < 0) {\n    startY = startY + startTranslateYPx * startMultiplierY;\n  }\n  if (endTranslateYPx > 0) {\n    endY = endY + endTranslateYPx * endMultiplierY;\n  }\n  if (startTranslateXPx < 0) {\n    startX = startX + startTranslateXPx * startMultiplierX;\n  }\n  if (endTranslateXPx > 0) {\n    endX = endX + endTranslateXPx * endMultiplierX;\n  }\n\n  // add scroll\n  startX += scroll.x;\n  endX += scroll.x;\n  startY += scroll.y;\n  endY += scroll.y;\n\n  // NOTE: please refactor and isolate this :(\n  if (shouldAlwaysCompleteAnimation) {\n    const topBeginsInView = scroll.y + rect.top < view.height;\n    const leftBeginsInView = scroll.x + rect.left < view.width;\n    const bottomEndsInView =\n      scroll.y + rect.bottom > view.scrollHeight - view.height;\n    const rightEndsInView =\n      scroll.x + rect.right > view.scrollWidth - view.height;\n\n    if (topBeginsInView && bottomEndsInView) {\n      startMultiplierY = 1;\n      endMultiplierY = 1;\n      startY = 0;\n      endY = view.scrollHeight - view.height;\n    }\n    if (leftBeginsInView && rightEndsInView) {\n      startMultiplierX = 1;\n      endMultiplierX = 1;\n      startX = 0;\n      endX = view.scrollWidth - view.width;\n    }\n\n    if (!topBeginsInView && bottomEndsInView) {\n      startY = rect.top - view.height + scroll.y;\n      endY = view.scrollHeight - view.height;\n      const totalDist = endY - startY;\n      startMultiplierY = getTranslateScalar(\n        startTranslateYPx,\n        endTranslateYPx,\n        totalDist\n      );\n      endMultiplierY = 1;\n      if (startTranslateYPx < 0) {\n        startY = startY + startTranslateYPx * startMultiplierY;\n      }\n    }\n    if (!leftBeginsInView && rightEndsInView) {\n      startX = rect.left - view.width + scroll.x;\n      endX = view.scrollWidth - view.width;\n      const totalDist = endX - startX;\n      startMultiplierX = getTranslateScalar(\n        startTranslateXPx,\n        endTranslateXPx,\n        totalDist\n      );\n      endMultiplierX = 1;\n      if (startTranslateXPx < 0) {\n        startX = startX + startTranslateXPx * startMultiplierX;\n      }\n    }\n\n    if (topBeginsInView && !bottomEndsInView) {\n      startY = 0;\n      endY = rect.bottom + scroll.y;\n      const totalDist = endY - startY;\n      startMultiplierY = 1;\n      endMultiplierY = getTranslateScalar(\n        startTranslateYPx,\n        endTranslateYPx,\n        totalDist\n      );\n      if (endTranslateYPx > 0) {\n        endY = endY + endTranslateYPx * endMultiplierY;\n      }\n    }\n    if (leftBeginsInView && !rightEndsInView) {\n      startX = 0;\n      endX = rect.right + scroll.x;\n      const totalDist = endX - startX;\n      startMultiplierX = 1;\n      endMultiplierX = getTranslateScalar(\n        startTranslateXPx,\n        endTranslateXPx,\n        totalDist\n      );\n      if (endTranslateXPx > 0) {\n        endX = endX + endTranslateXPx * endMultiplierX;\n      }\n    }\n  }\n\n  const limits = new Limits({\n    startX,\n    startY,\n    endX,\n    endY,\n    startMultiplierX,\n    endMultiplierX,\n    startMultiplierY,\n    endMultiplierY,\n  });\n\n  return limits;\n}\n", "import { ParsedValueEffect, ParallaxStartEndEffects } from '../types';\nimport { Limits } from '../classes/Limits';\n\nexport function scaleTranslateEffectsForSlowerScroll(\n  effects: ParallaxStartEndEffects,\n  limits: Limits\n): ParallaxStartEndEffects {\n  const effectsCopy = {\n    ...effects,\n  };\n\n  if (effectsCopy.translateX) {\n    effectsCopy.translateX = {\n      ...effects.translateX,\n      start: effectsCopy.translateX.start * limits.startMultiplierX,\n      end: effectsCopy.translateX.end * limits.endMultiplierX,\n    } as ParsedValueEffect;\n  }\n  if (effectsCopy.translateY) {\n    effectsCopy.translateY = {\n      ...effects.translateY,\n      start: effectsCopy.translateY.start * limits.startMultiplierY,\n      end: effectsCopy.translateY.end * limits.endMultiplierY,\n    } as ParsedValueEffect;\n  }\n\n  return effectsCopy;\n}\n", "import { ScrollAxis, ValidScrollAxis } from '../types';\nimport { ParallaxElementConfig, ParallaxStartEndEffects } from '../types';\n\nexport function getShouldScaleTranslateEffects(\n  props: ParallaxElementConfig,\n  effects: ParallaxStartEndEffects,\n  scrollAxis: ValidScrollAxis\n): boolean {\n  if (\n    props.rootMargin ||\n    props.targetElement ||\n    props.shouldDisableScalingTranslations\n  ) {\n    return false;\n  }\n\n  if (\n    (!!effects.translateX && scrollAxis === ScrollAxis.horizontal) ||\n    (!!effects.translateY && scrollAxis === ScrollAxis.vertical)\n  ) {\n    return true;\n  }\n\n  return false;\n}\n", "export const clamp = (num: number, min: number, max: number) =>\n  Math.min(Math.max(num, min), max);\n", "import bezier from 'bezier-easing';\nimport {\n  CreateElementOptions,\n  ParallaxElementConfig,\n  ParallaxStartEndEffects,\n  ScrollAxis,\n  ValidScrollAxis,\n  EasingParam,\n} from '../types';\nimport { createId } from '../utils/createId';\nimport { Rect } from './Rect';\nimport { View } from './View';\nimport { Scroll } from './Scroll';\nimport { Limits } from './Limits';\nimport { parseElementTransitionEffects } from '../helpers/parseElementTransitionEffects';\nimport { getProgressAmount } from '../helpers/getProgressAmount';\nimport { isElementInView } from '../helpers/isElementInView';\nimport {\n  resetStyles,\n  setElementStyles,\n  setWillChangeStyles,\n} from '../helpers/elementStyles';\nimport { createEasingFunction } from '../helpers/createEasingFunction';\nimport { createLimitsForRelativeElements } from '../helpers/createLimitsForRelativeElements';\nimport { createLimitsWithTranslationsForRelativeElements } from '../helpers/createLimitsWithTranslationsForRelativeElements';\nimport { scaleTranslateEffectsForSlowerScroll } from '../helpers/scaleTranslateEffectsForSlowerScroll';\nimport { getShouldScaleTranslateEffects } from '../helpers/getShouldScaleTranslateEffects';\nimport { clamp } from '../helpers/clamp';\n\ntype ParallaxControllerConstructorOptions = {\n  scrollAxis: ValidScrollAxis;\n  disabledParallaxController?: boolean;\n};\ntype ElementConstructorOptions = CreateElementOptions &\n  ParallaxControllerConstructorOptions;\n\nexport class Element {\n  el: HTMLElement;\n  props: ParallaxElementConfig;\n  scrollAxis: ValidScrollAxis;\n  disabledParallaxController: boolean;\n  id: number;\n  effects: ParallaxStartEndEffects;\n  isInView: boolean | null;\n  progress: number;\n  /* Optionally set if translate effect must be scaled */\n  scaledEffects?: ParallaxStartEndEffects;\n  rect?: Rect;\n  limits?: Limits;\n  easing?: bezier.EasingFunction;\n\n  constructor(options: ElementConstructorOptions) {\n    this.el = options.el;\n    this.props = options.props;\n    this.scrollAxis = options.scrollAxis;\n    this.disabledParallaxController =\n      options.disabledParallaxController || false;\n    this.id = createId();\n    this.effects = parseElementTransitionEffects(this.props, this.scrollAxis);\n    this.isInView = null;\n    this.progress = 0;\n\n    this._setElementEasing(options.props.easing);\n\n    setWillChangeStyles(options.el, this.effects);\n  }\n\n  updateProps(nextProps: ParallaxElementConfig) {\n    this.props = { ...this.props, ...nextProps };\n    this.effects = parseElementTransitionEffects(nextProps, this.scrollAxis);\n    this._setElementEasing(nextProps.easing);\n\n    return this;\n  }\n\n  setCachedAttributes(view: View, scroll: Scroll): Element {\n    // NOTE: Must reset styles before getting the rect, as it might impact the natural position\n    resetStyles(this);\n\n    this.rect = new Rect({\n      el: this.props.targetElement || this.el,\n      rootMargin: this.props.rootMargin,\n      view,\n    });\n\n    const shouldScaleTranslateEffects = getShouldScaleTranslateEffects(\n      this.props,\n      this.effects,\n      this.scrollAxis\n    );\n\n    if (\n      typeof this.props.startScroll === 'number' &&\n      typeof this.props.endScroll === 'number'\n    ) {\n      this.limits = new Limits({\n        startX: this.props.startScroll,\n        startY: this.props.startScroll,\n        endX: this.props.endScroll,\n        endY: this.props.endScroll,\n      });\n\n      // Undo the reset -- place it back at current position with styles\n      this._setElementStyles();\n\n      return this;\n    }\n\n    if (shouldScaleTranslateEffects) {\n      this.limits = createLimitsWithTranslationsForRelativeElements(\n        this.rect,\n        view,\n        this.effects,\n        scroll,\n        this.scrollAxis,\n        this.props.shouldAlwaysCompleteAnimation\n      );\n\n      this.scaledEffects = scaleTranslateEffectsForSlowerScroll(\n        this.effects,\n        this.limits\n      );\n    } else {\n      this.limits = createLimitsForRelativeElements(\n        this.rect,\n        view,\n        scroll,\n        this.props.shouldAlwaysCompleteAnimation\n      );\n    }\n\n    // Undo the reset -- place it back at current position with styles\n    this._setElementStyles();\n\n    return this;\n  }\n\n  _updateElementIsInView(nextIsInView: boolean) {\n    // NOTE: Check if this is the first change to make sure onExit isn't called\n    const isFirstChange = this.isInView === null;\n    if (nextIsInView !== this.isInView) {\n      if (nextIsInView) {\n        this.props.onEnter && this.props.onEnter(this);\n      } else if (!isFirstChange) {\n        this._setFinalProgress();\n        this._setElementStyles();\n        this.props.onExit && this.props.onExit(this);\n      }\n    }\n    this.isInView = nextIsInView;\n  }\n\n  _setFinalProgress() {\n    const finalProgress = clamp(Math.round(this.progress), 0, 1);\n    this._updateElementProgress(finalProgress);\n  }\n\n  _setElementStyles() {\n    if (this.props.disabled || this.disabledParallaxController) return;\n    const effects = this.scaledEffects || this.effects;\n    setElementStyles(effects, this.progress, this.el);\n  }\n\n  _updateElementProgress(nextProgress: number) {\n    this.progress = nextProgress;\n    this.props.onProgressChange && this.props.onProgressChange(this.progress);\n    this.props.onChange && this.props.onChange(this);\n  }\n\n  _setElementEasing(easing?: EasingParam): void {\n    this.easing = createEasingFunction(easing);\n  }\n\n  updateElementOptions(options: ParallaxControllerConstructorOptions) {\n    this.scrollAxis = options.scrollAxis;\n    this.disabledParallaxController =\n      options.disabledParallaxController || false;\n  }\n\n  updatePosition(scroll: Scroll): Element {\n    if (!this.limits) return this;\n\n    const isVertical = this.scrollAxis === ScrollAxis.vertical;\n    const isFirstChange = this.isInView === null;\n    // based on scroll axis\n    const start = isVertical ? this.limits.startY : this.limits.startX;\n    const end = isVertical ? this.limits.endY : this.limits.endX;\n    const total = isVertical ? this.limits.totalY : this.limits.totalX;\n    const s = isVertical ? scroll.y : scroll.x;\n\n    // check if in view\n    const nextIsInView = isElementInView(start, end, s);\n    this._updateElementIsInView(nextIsInView);\n\n    // set the progress if in view or this is the first change\n    if (nextIsInView) {\n      const nextProgress = getProgressAmount(start, total, s, this.easing);\n      this._updateElementProgress(nextProgress);\n      this._setElementStyles();\n    } else if (isFirstChange) {\n      // NOTE: this._updateElementProgress -- dont use this because it will trigger onChange\n      this.progress = clamp(\n        Math.round(getProgressAmount(start, total, s, this.easing)),\n        0,\n        1\n      );\n      this._setElementStyles();\n    }\n\n    return this;\n  }\n}\n", "export type ViewSizeParams = {\n  width: number;\n  height: number;\n  scrollHeight: number;\n  scrollWidth: number;\n};\nexport class View {\n  scrollContainer: HTMLElement | undefined;\n  width: number;\n  height: number;\n  scrollHeight: number;\n  scrollWidth: number;\n\n  constructor(config: {\n    width: number;\n    height: number;\n    scrollHeight: number;\n    scrollWidth: number;\n    scrollContainer?: HTMLElement;\n  }) {\n    this.scrollContainer = config.scrollContainer;\n    this.width = config.width;\n    this.height = config.height;\n    this.scrollHeight = config.scrollHeight;\n    this.scrollWidth = config.scrollWidth;\n  }\n\n  hasChanged(params: ViewSizeParams) {\n    if (\n      params.width !== this.width ||\n      params.height !== this.height ||\n      params.scrollWidth !== this.scrollWidth ||\n      params.scrollHeight !== this.scrollHeight\n    ) {\n      return true;\n    }\n    return false;\n  }\n\n  setSize(params: ViewSizeParams) {\n    this.width = params.width;\n    this.height = params.height;\n    this.scrollHeight = params.scrollHeight;\n    this.scrollWidth = params.scrollWidth;\n    return this;\n  }\n}\n", "export class Scroll {\n  x: number;\n  y: number;\n  dx: number;\n  dy: number;\n\n  constructor(x: number, y: number) {\n    this.x = x;\n    this.y = y;\n    this.dx = 0;\n    this.dy = 0;\n  }\n\n  setScroll(x: number, y: number) {\n    this.dx = x - this.x;\n    this.dy = y - this.y;\n    this.x = x;\n    this.y = y;\n    return this;\n  }\n}\n", "export function testForPassiveScroll() {\n  let supportsPassiveOption = false;\n  try {\n    const opts = Object.defineProperty({}, 'passive', {\n      get() {\n        supportsPassiveOption = true;\n        return true;\n      },\n    });\n    // @ts-expect-error\n    window.addEventListener('test', null, opts);\n    // @ts-expect-error\n    window.removeEventListener('test', null, opts);\n  } catch (e) {}\n  return supportsPassiveOption;\n}\n", "import { resetStyles } from '../helpers/elementStyles';\nimport { View } from './View';\nimport { Scroll } from './Scroll';\nimport { Element } from './Element';\nimport { testForPassiveScroll } from '../utils/testForPassiveScroll';\nimport {\n  CreateElementOptions,\n  ParallaxControllerOptions,\n  ParallaxElementConfig,\n  ScrollAxis,\n  ValidScrollAxis,\n  ViewElement,\n} from '../types';\n\n/**\n * -------------------------------------------------------\n * Parallax Controller\n * -------------------------------------------------------\n *\n * The global controller for setting up and managing a scroll view of elements.\n *\n */\n\nexport class ParallaxController {\n  disabled: boolean;\n  elements: Element[];\n  scrollAxis: ValidScrollAxis;\n  viewEl: ViewElement;\n  scroll: Scroll;\n  view: View;\n  _hasScrollContainer: boolean;\n  _ticking: boolean;\n  _supportsPassive: boolean;\n  _resizeObserver?: ResizeObserver;\n\n  /**\n   * Static method to instantiate the ParallaxController.\n   * @returns {Class} ParallaxController\n   */\n  static init(options: ParallaxControllerOptions): ParallaxController {\n    const hasWindow = typeof window !== 'undefined';\n\n    if (!hasWindow) {\n      throw new Error(\n        'Looks like ParallaxController.init() was called on the server. This method must be called on the client.'\n      );\n    }\n\n    return new ParallaxController(options);\n  }\n\n  constructor({\n    scrollAxis = ScrollAxis.vertical,\n    scrollContainer,\n    disabled = false,\n  }: ParallaxControllerOptions) {\n    this.disabled = disabled;\n    this.scrollAxis = scrollAxis;\n    // All parallax elements to be updated\n    this.elements = [];\n\n    this._hasScrollContainer = !!scrollContainer;\n    this.viewEl = scrollContainer ?? window;\n\n    // Scroll and View\n    const [x, y] = this._getScrollPosition();\n    this.scroll = new Scroll(x, y);\n\n    this.view = new View({\n      width: 0,\n      height: 0,\n      scrollWidth: 0,\n      scrollHeight: 0,\n      scrollContainer: this._hasScrollContainer ? scrollContainer : undefined,\n    });\n\n    // Ticking\n    this._ticking = false;\n\n    // Passive support\n    this._supportsPassive = testForPassiveScroll();\n\n    // Bind methods to class\n    this._bindAllMethods();\n\n    // If this is initialized disabled, don't do anything below.\n    if (this.disabled) return;\n\n    this._addListeners(this.viewEl);\n    this._addResizeObserver();\n    this._setViewSize();\n  }\n\n  _bindAllMethods() {\n    [\n      '_addListeners',\n      '_removeListeners',\n      '_getScrollPosition',\n      '_handleScroll',\n      '_handleUpdateCache',\n      '_updateAllElements',\n      '_updateElementPosition',\n      '_setViewSize',\n      '_addResizeObserver',\n      '_checkIfViewHasChanged',\n      '_getViewParams',\n      'getElements',\n      'createElement',\n      'removeElementById',\n      'resetElementStyles',\n      'updateElementPropsById',\n      'update',\n      'updateScrollContainer',\n      'destroy',\n    ].forEach((method: string) => {\n      // @ts-expect-error\n      this[method] = this[method].bind(this);\n    });\n  }\n\n  _addListeners(el: ViewElement) {\n    el.addEventListener(\n      'scroll',\n      this._handleScroll,\n      this._supportsPassive ? { passive: true } : false\n    );\n    window.addEventListener('resize', this._handleUpdateCache, false);\n    window.addEventListener('blur', this._handleUpdateCache, false);\n    window.addEventListener('focus', this._handleUpdateCache, false);\n    window.addEventListener('load', this._handleUpdateCache, false);\n  }\n\n  _removeListeners(el: ViewElement) {\n    el.removeEventListener('scroll', this._handleScroll, false);\n    window.removeEventListener('resize', this._handleUpdateCache, false);\n    window.removeEventListener('blur', this._handleUpdateCache, false);\n    window.removeEventListener('focus', this._handleUpdateCache, false);\n    window.removeEventListener('load', this._handleUpdateCache, false);\n    this._resizeObserver?.disconnect();\n  }\n\n  _addResizeObserver() {\n    try {\n      const observedEl: HTMLElement = this._hasScrollContainer\n        ? (this.viewEl as HTMLElement)\n        : document.documentElement;\n      this._resizeObserver = new ResizeObserver(() => this.update());\n      this._resizeObserver.observe(observedEl);\n    } catch (e) {\n      console.warn(\n        'Failed to create the resize observer in the ParallaxContoller'\n      );\n    }\n  }\n\n  _getScrollPosition() {\n    // Save current scroll\n    // Supports IE 9 and up.\n    const nx = this._hasScrollContainer\n      ? // @ts-expect-error\n        this.viewEl.scrollLeft\n      : window.pageXOffset;\n    const ny = this._hasScrollContainer\n      ? // @ts-expect-error\n        this.viewEl.scrollTop\n      : window.pageYOffset;\n\n    return [nx, ny];\n  }\n\n  /**\n   * Window scroll handler sets scroll position\n   * and then calls '_updateAllElements()'.\n   */\n  _handleScroll() {\n    const [nx, ny] = this._getScrollPosition();\n    this.scroll.setScroll(nx, ny);\n\n    // Only called if the last animation request has been\n    // completed and there are parallax elements to update\n    if (!this._ticking && this.elements?.length > 0) {\n      this._ticking = true;\n      // @ts-ignore\n      window.requestAnimationFrame(this._updateAllElements);\n    }\n  }\n\n  /**\n   * Window resize handler. Sets the new window inner height\n   * then updates parallax element attributes and positions.\n   */\n  _handleUpdateCache() {\n    this._setViewSize();\n    this._updateAllElements({ updateCache: true });\n  }\n\n  /**\n   * Update element positions.\n   * Determines if the element is in view based on the cached\n   * attributes, if so set the elements parallax styles.\n   */\n  _updateAllElements({ updateCache }: { updateCache?: boolean } = {}) {\n    if (this.elements) {\n      this.elements.forEach(element => {\n        if (updateCache) {\n          element.setCachedAttributes(this.view, this.scroll);\n        }\n        this._updateElementPosition(element);\n      });\n    }\n    // reset ticking so more animations can be called\n    this._ticking = false;\n  }\n\n  /**\n   * Update element positions.\n   * Determines if the element is in view based on the cached\n   * attributes, if so set the elements parallax styles.\n   */\n  _updateElementPosition(element: Element) {\n    if (element.props.disabled || this.disabled) return;\n    element.updatePosition(this.scroll);\n  }\n\n  /**\n   * Gets the params to set in the View from the scroll container or the window\n   */\n  _getViewParams(): {\n    width: number;\n    height: number;\n    scrollHeight: number;\n    scrollWidth: number;\n  } {\n    if (this._hasScrollContainer) {\n      // @ts-expect-error\n      const width = this.viewEl.offsetWidth;\n      // @ts-expect-error\n      const height = this.viewEl.offsetHeight;\n      // @ts-expect-error\n      const scrollHeight = this.viewEl.scrollHeight;\n      // @ts-expect-error\n      const scrollWidth = this.viewEl.scrollWidth;\n      return this.view.setSize({\n        width,\n        height,\n        scrollHeight,\n        scrollWidth,\n      });\n    }\n\n    const html = document.documentElement;\n    const width = window.innerWidth || html.clientWidth;\n    const height = window.innerHeight || html.clientHeight;\n    const scrollHeight = html.scrollHeight;\n    const scrollWidth = html.scrollWidth;\n\n    return { width, height, scrollHeight, scrollWidth };\n  }\n\n  /**\n   * Cache the view attributes\n   */\n  _setViewSize() {\n    return this.view.setSize(this._getViewParams());\n  }\n\n  /**\n   * Checks if any of the cached attributes of the view have changed.\n   * @returns boolean\n   */\n  _checkIfViewHasChanged() {\n    return this.view.hasChanged(this._getViewParams());\n  }\n\n  /**\n   * -------------------------------------------------------\n   * Public methods\n   * -------------------------------------------------------\n   */\n\n  /**\n   * Returns all the parallax elements in the controller\n   */\n  getElements(): Element[] {\n    return this.elements;\n  }\n\n  /**\n   * Creates and returns new parallax element with provided options to be managed by the controller.\n   */\n  createElement(options: CreateElementOptions): Element {\n    const newElement = new Element({\n      ...options,\n      scrollAxis: this.scrollAxis,\n      disabledParallaxController: this.disabled,\n    });\n    newElement.setCachedAttributes(this.view, this.scroll);\n    this.elements = this.elements\n      ? [...this.elements, newElement]\n      : [newElement];\n    this._updateElementPosition(newElement);\n\n    // NOTE: This checks if the view has changed then update the controller and all elements if it has\n    // This shouldn't always be necessary with a resize observer watching the view element\n    // but there seems to be cases where the resize observer does not catch and update.\n    if (this._checkIfViewHasChanged()) {\n      this.update();\n    }\n    return newElement;\n  }\n\n  /**\n   * Remove an element by id\n   */\n  removeElementById(id: number) {\n    if (!this.elements) return;\n    this.elements = this.elements.filter(el => el.id !== id);\n  }\n\n  /**\n   * Updates an existing parallax element object with new options.\n   */\n  updateElementPropsById(id: number, props: ParallaxElementConfig): void {\n    if (this.elements) {\n      this.elements = this.elements.map(el => {\n        if (el.id === id) {\n          return el.updateProps(props);\n        }\n        return el;\n      });\n    }\n\n    this.update();\n  }\n\n  /**\n   * Remove a target elements parallax styles\n   */\n  resetElementStyles(element: Element) {\n    resetStyles(element);\n  }\n\n  /**\n   * Updates all cached attributes on parallax elements.\n   */\n  update() {\n    // Save the latest scroll position because window.scroll\n    // may be called and the handle scroll event may not be called.\n    const [nx, ny] = this._getScrollPosition();\n    this.scroll.setScroll(nx, ny);\n\n    this._setViewSize();\n    this._updateAllElements({ updateCache: true });\n  }\n  /**\n   * Updates the scroll container of the parallax controller\n   */\n  updateScrollContainer(el: HTMLElement) {\n    // remove existing listeners with current el first\n    this._removeListeners(this.viewEl);\n\n    this.viewEl = el;\n    this._hasScrollContainer = !!el;\n    this.view = new View({\n      width: 0,\n      height: 0,\n      scrollWidth: 0,\n      scrollHeight: 0,\n      scrollContainer: el,\n    });\n    this._setViewSize();\n    this._addListeners(this.viewEl);\n    this._updateAllElements({ updateCache: true });\n  }\n\n  disableParallaxController() {\n    this.disabled = true;\n    // remove listeners\n    this._removeListeners(this.viewEl);\n    // reset all styles\n    if (this.elements) {\n      this.elements.forEach(element => resetStyles(element));\n    }\n  }\n\n  enableParallaxController() {\n    this.disabled = false;\n    if (this.elements) {\n      this.elements.forEach(element =>\n        element.updateElementOptions({\n          disabledParallaxController: false,\n          scrollAxis: this.scrollAxis,\n        })\n      );\n    }\n    // add back listeners\n    this._addListeners(this.viewEl);\n    this._addResizeObserver();\n    this._setViewSize();\n  }\n\n  /**\n   * Disable all parallax elements\n   */\n  disableAllElements() {\n    console.warn('deprecated: use disableParallaxController() instead');\n    if (this.elements) {\n      this.elements = this.elements.map(el => {\n        return el.updateProps({ disabled: true });\n      });\n    }\n    this.update();\n  }\n\n  /**\n   * Enable all parallax elements\n   */\n  enableAllElements() {\n    console.warn('deprecated: use enableParallaxController() instead');\n    if (this.elements) {\n      this.elements = this.elements.map(el => {\n        return el.updateProps({ disabled: false });\n      });\n    }\n    this.update();\n  }\n\n  /**\n   * Removes all listeners and resets all styles on managed elements.\n   */\n  destroy() {\n    this._removeListeners(this.viewEl);\n    if (this.elements) {\n      this.elements.forEach(element => resetStyles(element));\n    }\n    // @ts-expect-error\n    this.elements = undefined;\n  }\n}\n", "export function removeUndefinedObjectKeys(obj: { [key: string]: any }) {\n  Object.keys(obj).forEach((key) =>\n    obj[key] === undefined && delete obj[key]\n  );\n  return obj;\n}\n", "import { ParallaxElementConfig } from 'parallax-controller';\nimport { removeUndefinedObjectKeys } from '../utils/removeUndefinedObjectKeys';\n\nexport function getIsolatedParallaxProps(props: any): {\n  parallaxProps: ParallaxElementConfig;\n  rest: Record<string, any>;\n} {\n  const {\n    disabled,\n    easing,\n    endScroll,\n    onChange,\n    onEnter,\n    onExit,\n    onProgressChange,\n    opacity,\n    rootMargin,\n    rotate,\n    rotateX,\n    rotateY,\n    rotateZ,\n    scale,\n    scaleX,\n    scaleY,\n    scaleZ,\n    shouldAlwaysCompleteAnimation,\n    shouldDisableScalingTranslations,\n    speed,\n    startScroll,\n    targetElement,\n    translateX,\n    translateY,\n    ...rest\n  } = props;\n\n  const parallaxProps = removeUndefinedObjectKeys({\n    disabled,\n    easing,\n    endScroll,\n    onChange,\n    onEnter,\n    onExit,\n    onProgressChange,\n    opacity,\n    rootMargin,\n    rotate,\n    rotateX,\n    rotateY,\n    rotateZ,\n    scale,\n    scaleX,\n    scaleY,\n    scaleZ,\n    shouldAlwaysCompleteAnimation,\n    shouldDisableScalingTranslations,\n    speed,\n    startScroll,\n    targetElement,\n    translateX,\n    translateY,\n  });\n\n  return {\n    parallaxProps,\n    rest,\n  };\n}\n", "import { ParallaxController } from 'parallax-controller';\nimport { useEffect } from 'react';\n\nexport function useVerifyController(controller: ParallaxController | unknown) {\n  useEffect(() => {\n    const isServer = typeof window === 'undefined';\n    // Make sure the provided controller is an instance of the Parallax Controller\n    const isInstance = controller instanceof ParallaxController;\n    // Throw if neither context or global is available\n    if (!isServer && !controller && !isInstance) {\n      throw new Error(\n        \"Must wrap your application's <Parallax /> components in a <ParallaxProvider />.\"\n      );\n    }\n  }, [controller]);\n}\n", "import React from 'react';\nimport { ParallaxController } from 'parallax-controller';\n\nexport const ParallaxContext = React.createContext<ParallaxController | null>(\n  null\n);\n", "import { useContext } from 'react';\nimport { ParallaxController } from 'parallax-controller';\nimport { ParallaxContext } from '../context/ParallaxContext';\n\nexport function useParallaxController(): ParallaxController | null {\n  const parallaxController = useContext(ParallaxContext);\n  const isServer = typeof window === 'undefined';\n  if (isServer) {\n    return null;\n  }\n\n  if (!parallaxController) {\n    throw new Error(\n      'Could not find `react-scroll-parallax` context value. Please ensure the component is wrapped in a <ParallaxProvider>'\n    );\n  }\n\n  return parallaxController;\n}\n", "import { CreateElementOptions, Element } from 'parallax-controller';\nimport { useEffect, useRef, useState } from 'react';\nimport { useVerifyController } from '../components/Parallax/hooks';\nimport { ParallaxProps } from '../components/Parallax/types';\nimport { getIsolatedParallaxProps } from '../helpers/getIsolatedParallaxProps';\nimport { useParallaxController } from './useParallaxController';\n\nexport function useParallax<T extends HTMLElement>(props: ParallaxProps) {\n  const controller = useParallaxController();\n  const ref = useRef<T>(null);\n  const { parallaxProps } = getIsolatedParallaxProps(props);\n\n  useVerifyController(controller);\n\n  const [element, setElement] = useState<Element>();\n\n  // create element\n  useEffect(() => {\n    let newElement: Element | undefined;\n    if (ref.current instanceof HTMLElement) {\n      const options: CreateElementOptions = {\n        el: ref.current,\n        props: parallaxProps,\n      };\n      newElement = controller?.createElement(options);\n      setElement(newElement);\n    } else {\n      throw new Error(\n        'You must assign the ref returned by the useParallax() hook to an HTML Element.'\n      );\n    }\n\n    return () => {\n      if (newElement) {\n        controller?.removeElementById(newElement.id);\n      }\n    };\n  }, []);\n\n  // update element\n  useEffect(() => {\n    if (element) {\n      if (props.disabled) {\n        controller?.resetElementStyles(element);\n        controller?.updateElementPropsById(element.id, parallaxProps);\n      } else {\n        controller?.updateElementPropsById(element.id, parallaxProps);\n      }\n    }\n  }, [\n    props.disabled,\n    props.easing,\n    props.endScroll,\n    props.onChange,\n    props.onEnter,\n    props.onExit,\n    props.onProgressChange,\n    props.opacity,\n    props.rootMargin,\n    props.rotate,\n    props.rotateX,\n    props.rotateY,\n    props.rotateZ,\n    props.scale,\n    props.scaleX,\n    props.scaleY,\n    props.scaleZ,\n    props.shouldAlwaysCompleteAnimation,\n    props.shouldDisableScalingTranslations,\n    props.speed,\n    props.startScroll,\n    props.targetElement,\n    props.translateX,\n    props.translateY,\n  ]);\n\n  return { ref, controller, element };\n}\n", "import React, { PropsWithChildren } from 'react';\nimport { getIsolatedParallaxProps } from '../../helpers/getIsolatedParallaxProps';\nimport { useParallax } from '../../hooks/useParallax';\nimport { ParallaxProps } from './types';\n\nexport function Parallax(props: PropsWithChildren<ParallaxProps>) {\n  const { parallaxProps, rest } = getIsolatedParallaxProps(props);\n  const { ref } = useParallax<HTMLDivElement>(parallaxProps);\n  return (\n    <div ref={ref} {...rest}>\n      {props.children}\n    </div>\n  );\n}\n", "import { parseValueAndUnit } from 'parallax-controller';\nimport { BannerLayer } from '../types';\n\nconst FALLBACK_RECT = {\n  height: 0,\n};\n\ntype ExpandedStyle = {\n  top?: string;\n  bottom?: string;\n};\n\nexport function getExpandedStyle(layer: BannerLayer): ExpandedStyle {\n  if (Array.isArray(layer.translateY)) {\n    const translateYStart = parseValueAndUnit(layer.translateY[0]);\n    const translateYEnd = parseValueAndUnit(layer.translateY[1]);\n\n    if (translateYStart.unit === 'px' && translateYEnd.unit === 'px') {\n      return {\n        top: `${Math.abs(translateYEnd.value) * -1}px`,\n        bottom: `${Math.abs(translateYStart.value) * -1}px`,\n      };\n    }\n\n    if (translateYStart.unit === '%' && translateYEnd.unit === '%') {\n      const clientRect =\n        layer.targetElement?.getBoundingClientRect() ?? FALLBACK_RECT;\n      const top = Math.abs(clientRect.height * 0.01 * translateYEnd.value) * -1;\n      const bottom =\n        Math.abs(clientRect.height * 0.01 * translateYStart.value) * -1;\n      return {\n        top: `${top}px`,\n        bottom: `${bottom}px`,\n      };\n    }\n  }\n\n  if (layer.speed) {\n    const speed = layer.speed || 0;\n    const absSpeed = Math.abs(speed) * 10 * -1;\n\n    return {\n      top: `${absSpeed}px`,\n      bottom: `${absSpeed}px`,\n    };\n  }\n\n  return {};\n}\n", "import { BannerLayer } from '../types';\n\nexport function getImageStyle(layer: BannerLayer) {\n  return layer.image\n    ? {\n        backgroundImage: `url(${layer.image})`,\n        backgroundPosition: 'center',\n        backgroundSize: 'cover',\n      }\n    : {};\n}\n", "import React, { CSSProperties } from 'react';\nimport { useParallax } from '../../../hooks/useParallax';\nimport { getIsolatedParallaxProps } from '../../../helpers/getIsolatedParallaxProps';\nimport { getExpandedStyle } from '../helpers/getExpandedStyle';\nimport { getImageStyle } from '../helpers/getImageStyle';\nimport { BannerLayer } from '../types';\n\nconst absoluteStyle: CSSProperties = {\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  right: 0,\n  bottom: 0,\n};\n\nexport const ParallaxBannerLayer = (\n  props: BannerLayer & { testId?: string }\n) => {\n  const { parallaxProps, rest } = getIsolatedParallaxProps(props);\n  const {\n    children,\n    disabled,\n    style,\n    expanded = true,\n    image,\n    testId,\n    ...divProps\n  } = rest;\n\n  const imageStyle = getImageStyle(props);\n  const expandedStyle = expanded ? getExpandedStyle(props) : {};\n  const parallax = useParallax<HTMLDivElement>({\n    targetElement: props.targetElement,\n    shouldDisableScalingTranslations: true,\n    ...parallaxProps,\n  });\n\n  return (\n    <div\n      data-testid={testId}\n      ref={parallax.ref}\n      style={{\n        ...imageStyle,\n        ...absoluteStyle,\n        ...expandedStyle,\n        ...style,\n      }}\n      {...divProps}\n    >\n      {rest.children}\n    </div>\n  );\n};\n", "import React, {\n  PropsWithChildren,\n  CSSProperties,\n  useEffect,\n  useRef,\n  useState,\n  ReactElement,\n} from 'react';\nimport { ParallaxBannerLayer } from './components/ParallaxBannerLayer';\nimport { ParallaxBannerProps } from './types';\n\nconst containerStyle: CSSProperties = {\n  position: 'relative',\n  overflow: 'hidden',\n  width: '100%',\n};\n\nexport const ParallaxBanner = (\n  props: PropsWithChildren<ParallaxBannerProps>\n) => {\n  const [targetElement, setTargetElement] =\n    useState<HTMLDivElement | null>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n  useEffect(() => {\n    setTargetElement(containerRef.current);\n  }, []);\n  const {\n    disabled: disableAllLayers,\n    style: rootStyle,\n    layers = [],\n    ...rootRest\n  } = props;\n\n  function renderLayers() {\n    if (targetElement) {\n      const shouldUseLayers = layers && layers.length > 0;\n      if (shouldUseLayers) {\n        return layers.map((layer, i) => (\n          <ParallaxBannerLayer\n            {...layer}\n            targetElement={targetElement}\n            key={`layer-${i}`}\n            testId={`layer-${i}`}\n          />\n        ));\n      }\n    }\n    return null;\n  }\n\n  function renderChildren() {\n    if (targetElement) {\n      return React.Children.map(props.children, (child) => {\n        const item = child as ReactElement<\n          PropsWithChildren<{ targetElement: any }>\n        >;\n        // adds the targetElement prop to any ParallaxBannerLayer components\n        if (item?.type === ParallaxBannerLayer) {\n          const clone = React.cloneElement(item, {\n            targetElement,\n          });\n          return clone;\n        }\n        return child;\n      });\n    }\n    return null;\n  }\n  return (\n    <div\n      ref={containerRef}\n      style={{ ...containerStyle, ...rootStyle }}\n      {...rootRest}\n    >\n      {/* Using the `layers` prop to define children */}\n      {renderLayers()}\n      {/* Using children to compose layers */}\n      {renderChildren()}\n    </div>\n  );\n};\n", "import {\n  ParallaxController,\n  ParallaxControllerOptions,\n} from 'parallax-controller';\n\nexport const createController = (options: ParallaxControllerOptions) => {\n  // Don't initialize on the server\n  const isServer = typeof window === 'undefined';\n\n  if (!isServer) {\n    // Must not be the server so kick it off...\n    return ParallaxController.init(options);\n  }\n  return null;\n};\n", "import React, { <PERSON>ps<PERSON><PERSON><PERSON><PERSON>dren, useEffect, useRef } from 'react';\n\nimport { ParallaxContext } from '../../context/ParallaxContext';\nimport { ParallaxController, ScrollAxis } from 'parallax-controller';\nimport { ParallaxProviderProps } from './types';\nimport { createController } from './helpers';\n\nexport function ParallaxProvider(\n  props: PropsWithChildren<ParallaxProviderProps>\n) {\n  const controller = useRef<null | ParallaxController>(null);\n\n  if (!controller.current) {\n    controller.current = createController({\n      scrollAxis: props.scrollAxis || ScrollAxis.vertical,\n      scrollContainer: props.scrollContainer,\n      disabled: props.isDisabled,\n    });\n  }\n\n  // update scroll container\n  useEffect(() => {\n    if (props.scrollContainer && controller.current) {\n      controller.current.updateScrollContainer(props.scrollContainer);\n    }\n  }, [props.scrollContainer, controller.current]);\n\n  // disable/enable parallax\n  useEffect(() => {\n    if (props.isDisabled && controller.current) {\n      controller.current.disableParallaxController();\n    }\n    if (!props.isDisabled && controller.current) {\n      controller.current.enableParallaxController();\n    }\n  }, [props.isDisabled, controller.current]);\n\n  // remove the controller when unmounting\n  useEffect(() => {\n    return () => {\n      controller?.current && controller?.current.destroy();\n    };\n  }, []);\n\n  return (\n    <ParallaxContext.Provider value={controller.current}>\n      {props.children}\n    </ParallaxContext.Provider>\n  );\n}\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAOA,QAAI,oBAAoB;AACxB,QAAI,mBAAmB;AACvB,QAAI,wBAAwB;AAC5B,QAAI,6BAA6B;AAEjC,QAAI,mBAAmB;AACvB,QAAI,kBAAkB,KAAO,mBAAmB;AAEhD,QAAI,wBAAwB,OAAO,iBAAiB;AAEpD,aAAS,EAAG,KAAK,KAAK;AAAE,aAAO,IAAM,IAAM,MAAM,IAAM;AAAA,IAAK;AAC5D,aAAS,EAAG,KAAK,KAAK;AAAE,aAAO,IAAM,MAAM,IAAM;AAAA,IAAK;AACtD,aAAS,EAAG,KAAU;AAAE,aAAO,IAAM;AAAA,IAAK;AAG1C,aAAS,WAAY,IAAI,KAAK,KAAK;AAAE,eAAS,EAAE,KAAK,GAAG,IAAI,KAAK,EAAE,KAAK,GAAG,KAAK,KAAK,EAAE,GAAG,KAAK;AAAA,IAAI;AAGnG,aAAS,SAAU,IAAI,KAAK,KAAK;AAAE,aAAO,IAAM,EAAE,KAAK,GAAG,IAAI,KAAK,KAAK,IAAM,EAAE,KAAK,GAAG,IAAI,KAAK,EAAE,GAAG;AAAA,IAAG;AAEzG,aAAS,gBAAiB,IAAI,IAAI,IAAI,KAAK,KAAK;AAC9C,UAAI,UAAU,UAAU,IAAI;AAC5B,SAAG;AACD,mBAAW,MAAM,KAAK,MAAM;AAC5B,mBAAW,WAAW,UAAU,KAAK,GAAG,IAAI;AAC5C,YAAI,WAAW,GAAK;AAClB,eAAK;AAAA,QACP,OAAO;AACL,eAAK;AAAA,QACP;AAAA,MACF,SAAS,KAAK,IAAI,QAAQ,IAAI,yBAAyB,EAAE,IAAI;AAC7D,aAAO;AAAA,IACT;AAEA,aAAS,qBAAsB,IAAI,SAAS,KAAK,KAAK;AACrD,eAAS,IAAI,GAAG,IAAI,mBAAmB,EAAE,GAAG;AAC1C,YAAI,eAAe,SAAS,SAAS,KAAK,GAAG;AAC7C,YAAI,iBAAiB,GAAK;AACxB,iBAAO;AAAA,QACT;AACA,YAAI,WAAW,WAAW,SAAS,KAAK,GAAG,IAAI;AAC/C,mBAAW,WAAW;AAAA,MACxB;AACA,aAAO;AAAA,IACR;AAEA,aAAS,aAAc,GAAG;AACxB,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,SAASA,QAAQ,KAAK,KAAK,KAAK,KAAK;AACpD,UAAI,EAAE,KAAK,OAAO,OAAO,KAAK,KAAK,OAAO,OAAO,IAAI;AACnD,cAAM,IAAI,MAAM,yCAAyC;AAAA,MAC3D;AAEA,UAAI,QAAQ,OAAO,QAAQ,KAAK;AAC9B,eAAO;AAAA,MACT;AAGA,UAAI,eAAe,wBAAwB,IAAI,aAAa,gBAAgB,IAAI,IAAI,MAAM,gBAAgB;AAC1G,eAAS,IAAI,GAAG,IAAI,kBAAkB,EAAE,GAAG;AACzC,qBAAa,CAAC,IAAI,WAAW,IAAI,iBAAiB,KAAK,GAAG;AAAA,MAC5D;AAEA,eAAS,SAAU,IAAI;AACrB,YAAI,gBAAgB;AACpB,YAAI,gBAAgB;AACpB,YAAI,aAAa,mBAAmB;AAEpC,eAAO,kBAAkB,cAAc,aAAa,aAAa,KAAK,IAAI,EAAE,eAAe;AACzF,2BAAiB;AAAA,QACnB;AACA,UAAE;AAGF,YAAI,QAAQ,KAAK,aAAa,aAAa,MAAM,aAAa,gBAAgB,CAAC,IAAI,aAAa,aAAa;AAC7G,YAAI,YAAY,gBAAgB,OAAO;AAEvC,YAAI,eAAe,SAAS,WAAW,KAAK,GAAG;AAC/C,YAAI,gBAAgB,kBAAkB;AACpC,iBAAO,qBAAqB,IAAI,WAAW,KAAK,GAAG;AAAA,QACrD,WAAW,iBAAiB,GAAK;AAC/B,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,gBAAgB,IAAI,eAAe,gBAAgB,iBAAiB,KAAK,GAAG;AAAA,QACrF;AAAA,MACF;AAEA,aAAO,SAAS,aAAc,GAAG;AAE/B,YAAI,MAAM,GAAG;AACX,iBAAO;AAAA,QACT;AACA,YAAI,MAAM,GAAG;AACX,iBAAO;AAAA,QACT;AACA,eAAO,WAAW,SAAS,CAAC,GAAG,KAAK,GAAG;AAAA,MACzC;AAAA,IACF;AAAA;AAAA;;;;IC/FaC,SAYX,SAAAA,QAAYC,YAAZ;AACE,OAAKC,SAASD,WAAWC;AACzB,OAAKC,SAASF,WAAWE;AACzB,OAAKC,OAAOH,WAAWG;AACvB,OAAKC,OAAOJ,WAAWI;AAEvB,OAAKC,SAAS,KAAKF,OAAO,KAAKF;AAC/B,OAAKK,SAAS,KAAKF,OAAO,KAAKF;AAG/B,OAAKK,mBAAmBP,WAAWO,oBAAoB;AACvD,OAAKC,iBAAiBR,WAAWQ,kBAAkB;AACnD,OAAKC,mBAAmBT,WAAWS,oBAAoB;AACvD,OAAKC,iBAAiBV,WAAWU,kBAAkB;AACpD;;;;;;;;;;;;;;;ICpBSC;CAAZ,SAAYA,kBAAAA;AACVA,EAAAA,iBAAAA,OAAAA,IAAA;AACAA,EAAAA,iBAAAA,YAAAA,IAAA;AACAA,EAAAA,iBAAAA,YAAAA,IAAA;AACAA,EAAAA,iBAAAA,QAAAA,IAAA;AACAA,EAAAA,iBAAAA,SAAAA,IAAA;AACAA,EAAAA,iBAAAA,SAAAA,IAAA;AACAA,EAAAA,iBAAAA,SAAAA,IAAA;AACAA,EAAAA,iBAAAA,OAAAA,IAAA;AACAA,EAAAA,iBAAAA,QAAAA,IAAA;AACAA,EAAAA,iBAAAA,QAAAA,IAAA;AACAA,EAAAA,iBAAAA,QAAAA,IAAA;AACAA,EAAAA,iBAAAA,SAAAA,IAAA;AACD,GAbWA,oBAAAA,kBAAe,CAAA,EAA3B;AAeA,IAAYC;CAAZ,SAAYA,QAAAA;AACVA,EAAAA,OAAAA,IAAAA,IAAA;AACAA,EAAAA,OAAAA,GAAAA,IAAA;AACAA,EAAAA,OAAAA,IAAAA,IAAA;AACAA,EAAAA,OAAAA,IAAAA,IAAA;AACD,GALWA,UAAAA,QAAK,CAAA,EAAjB;AAQA,IAAYC;CAAZ,SAAYA,gBAAAA;AACVA,EAAAA,eAAAA,KAAAA,IAAA;AACAA,EAAAA,eAAAA,MAAAA,IAAA;AACAA,EAAAA,eAAAA,KAAAA,IAAA;AACD,GAJWA,kBAAAA,gBAAa,CAAA,EAAzB;AAMA,IAAYC;CAAZ,SAAYA,aAAAA;AACVA,EAAAA,YAAAA,EAAAA,IAAA;AACD,GAFWA,eAAAA,aAAU,CAAA,EAAtB;AAUA,IAAYC;CAAZ,SAAYA,aAAAA;AACVA,EAAAA,YAAAA,UAAAA,IAAA;AACAA,EAAAA,YAAAA,YAAAA,IAAA;AACD,GAHWA,eAAAA,aAAU,CAAA,EAAtB;AAsEA,IAAYC;CAAZ,SAAYA,eAAAA;AACVA,EAAAA,cAAAA,MAAAA,IAAA;AACAA,EAAAA,cAAAA,QAAAA,IAAA;AACAA,EAAAA,cAAAA,SAAAA,IAAA;AACAA,EAAAA,cAAAA,WAAAA,IAAA;AACAA,EAAAA,cAAAA,YAAAA,IAAA;AACAA,EAAAA,cAAAA,aAAAA,IAAA;AACAA,EAAAA,cAAAA,aAAAA,IAAA;AACAA,EAAAA,cAAAA,aAAAA,IAAA;AACAA,EAAAA,cAAAA,YAAAA,IAAA;AACAA,EAAAA,cAAAA,YAAAA,IAAA;AACAA,EAAAA,cAAAA,YAAAA,IAAA;AACAA,EAAAA,cAAAA,aAAAA,IAAA;AACAA,EAAAA,cAAAA,cAAAA,IAAA;AACAA,EAAAA,cAAAA,cAAAA,IAAA;AACAA,EAAAA,cAAAA,cAAAA,IAAA;AACAA,EAAAA,cAAAA,aAAAA,IAAA;AACAA,EAAAA,cAAAA,aAAAA,IAAA;AACAA,EAAAA,cAAAA,aAAAA,IAAA;AACAA,EAAAA,cAAAA,eAAAA,IAAA;AACAA,EAAAA,cAAAA,gBAAAA,IAAA;AACAA,EAAAA,cAAAA,gBAAAA,IAAA;AACAA,EAAAA,cAAAA,gBAAAA,IAAA;AACAA,EAAAA,cAAAA,eAAAA,IAAA;AACAA,EAAAA,cAAAA,eAAAA,IAAA;AACAA,EAAAA,cAAAA,eAAAA,IAAA;AACAA,EAAAA,cAAAA,YAAAA,IAAA;AACAA,EAAAA,cAAAA,aAAAA,IAAA;AACAA,EAAAA,cAAAA,eAAAA,IAAA;AACD,GA7BWA,iBAAAA,eAAY,CAAA,EAAxB;AC1HA,IAAIC,KAAK;AAET,SAAgBC,WAAAA;AACd,IAAED;AACF,SAAOA;AACR;ICNYE,OAAb,WAAA;AAQE,WAAAA,MAAYC,SAAZ;AAKE,QAAIC,OAAOD,QAAQE,GAAGC,sBAAX;AAGX,QAAIH,QAAQI,KAAKC,iBAAiB;AAChC,UAAMC,aAAaN,QAAQI,KAAKC,gBAAgBF,sBAA7B;AACnBF,aAAI,SAAA,CAAA,GACCA,MADD;QAEFM,KAAKN,KAAKM,MAAMD,WAAWC;QAC3BC,OAAOP,KAAKO,QAAQF,WAAWG;QAC/BC,QAAQT,KAAKS,SAASJ,WAAWC;QACjCE,MAAMR,KAAKQ,OAAOH,WAAWG;MAL3B,CAAA;IAOL;AACD,SAAKE,SAASX,QAAQE,GAAGU;AACzB,SAAKC,QAAQb,QAAQE,GAAGY;AACxB,SAAKL,OAAOR,KAAKQ;AACjB,SAAKD,QAAQP,KAAKO;AAClB,SAAKD,MAAMN,KAAKM;AAChB,SAAKG,SAAST,KAAKS;AAEnB,QAAIV,QAAQe,YAAY;AACtB,WAAKC,uBAAuBhB,QAAQe,UAApC;IACD;EACF;AApCH,MAAA,SAAAhB,MAAA;AAAA,SAyCEiB,yBAAA,SAAA,uBAAuBD,YAAvB;AACE,QAAIE,aAAaF,WAAWR,MAAMQ,WAAWL;AAC7C,QAAIQ,aAAaH,WAAWN,OAAOM,WAAWP;AAC9C,SAAKD,OAAOQ,WAAWR;AACvB,SAAKC,SAASO,WAAWP;AACzB,SAAKE,UAAUK,WAAWL;AAC1B,SAAKD,QAAQM,WAAWN;AACxB,SAAKE,UAAUM;AACf,SAAKJ,SAASK;EACf;AAlDH,SAAAnB;AAAA,EAAA;ACKO,IAAMoB,cAAc,CACzBzB,WAAW,EAAD,GACVF,MAAM4B,IACN5B,MAAM,GAAD,GACLA,MAAM,IAAD,GACLA,MAAM,IAAD,GACLC,cAAc4B,KACd5B,cAAc6B,MACd7B,cAAc8B,GARW;AAe3B,SAAgBC,kBACdC,KACAC,aAAAA;MAAAA,gBAAAA,QAAAA;AAAAA,kBAA6BlC,MAAM,GAAD;;AAElC,MAAImC,MAAwB;IAAEC,OAAO;IAAGC,MAAMH;EAAlB;AAE5B,MAAI,OAAOD,QAAQ,YAAa,QAAOE;AAEvC,MAAMG,UAAU,OAAOL,QAAQ,YAAY,OAAOA,QAAQ;AAE1D,MAAI,CAACK,SAAS;AACZ,UAAM,IAAIC,MACR,oEADI;EAGP;AAEDN,QAAMO,OAAOP,GAAD;AACZE,MAAIC,QAAQK,WAAWR,GAAD;AAGtBE,MAAIE,OAAOJ,IAAIS,MAAM,kBAAV,EAA8B,CAA9B,KAAoCR;AAG/C,MAAMS,cAAuBhB,YAAYiB,SAAST,IAAIE,IAAzB;AAE7B,MAAI,CAACM,aAAa;AAChB,UAAM,IAAIJ,MAAM,wBAAV;EACP;AAED,SAAOJ;AACR;ACjDM,IAAMU,gBAA8B;EACzCC,MAAM,CAAC,MAAM,KAAK,MAAM,CAAlB;EACNC,QAAQ,CAAC,MAAM,GAAK,GAAK,CAAjB;EACRC,SAAS,CAAC,GAAK,GAAK,MAAM,CAAjB;EACTC,WAAW,CAAC,MAAM,GAAK,MAAM,CAAlB;;EAEXC,YAAY,CAAC,MAAM,OAAO,MAAM,IAApB;EACZC,aAAa,CAAC,MAAM,OAAO,OAAO,IAArB;EACbC,aAAa,CAAC,OAAO,MAAM,OAAO,IAArB;EACbC,aAAa,CAAC,OAAO,MAAM,OAAO,IAArB;EACbC,YAAY,CAAC,MAAM,GAAK,OAAO,KAAnB;EACZC,YAAY,CAAC,MAAM,MAAM,OAAO,KAApB;EACZC,YAAY,CAAC,KAAK,MAAM,MAAM,KAAlB;;EAEZC,aAAa,CAAC,MAAM,MAAM,MAAM,IAAnB;EACbC,cAAc,CAAC,OAAO,MAAM,OAAO,CAArB;EACdC,cAAc,CAAC,OAAO,MAAM,MAAM,CAApB;EACdC,cAAc,CAAC,MAAM,GAAK,MAAM,CAAlB;EACdC,aAAa,CAAC,MAAM,OAAO,OAAO,CAArB;EACbC,aAAa,CAAC,MAAM,GAAK,MAAM,CAAlB;EACbC,aAAa,CAAC,OAAO,MAAM,OAAO,CAArB;;EAEbC,eAAe,CAAC,OAAO,MAAM,OAAO,KAArB;EACfC,gBAAgB,CAAC,OAAO,OAAO,OAAO,CAAtB;EAChBC,gBAAgB,CAAC,MAAM,GAAK,OAAO,CAAnB;EAChBC,gBAAgB,CAAC,MAAM,GAAK,MAAM,CAAlB;EAChBC,eAAe,CAAC,OAAO,MAAM,MAAM,IAApB;EACfC,eAAe,CAAC,GAAK,GAAK,GAAK,CAAhB;EACfC,eAAe,CAAC,OAAO,OAAO,MAAM,IAArB;;EAEfC,YAAY,CAAC,KAAK,OAAO,OAAO,KAApB;EACZC,aAAa,CAAC,OAAO,OAAO,MAAM,KAArB;EACbC,eAAe,CAAC,MAAM,OAAO,OAAO,IAArB;AAhC0B;SCA3BC,qBACdC,QAAAA;AAEA,MAAIC,MAAMC,QAAQF,MAAd,GAAuB;AACzB,eAAOG,qBAAAA,SAAOH,OAAO,CAAD,GAAKA,OAAO,CAAD,GAAKA,OAAO,CAAD,GAAKA,OAAO,CAAD,CAAxC;EACd;AACD,MACE,OAAOA,WAAW,YAClB,OAAO9B,cAAc8B,MAAD,MAAa,aACjC;AACA,QAAMI,SAAmBlC,cAAc8B,MAAD;AACtC,eAAOG,qBAAAA,SAAOC,OAAO,CAAD,GAAKA,OAAO,CAAD,GAAKA,OAAO,CAAD,GAAKA,OAAO,CAAD,CAAxC;EACd;AACD;AACD;ACPM,IAAMC,mBAAmBC,OAAOC,OAAOnF,eAAd;AAEzB,IAAMoF,6BAET;EACFC,OAAO;EACPC,YAAY;EACZC,YAAY;EACZC,QAAQ;EACRC,SAAS;EACTC,SAAS;EACTC,SAAS;EACTC,OAAO;EACPC,QAAQ;EACRC,QAAQ;EACRC,QAAQ;EACRC,SAAS;AAZP;AAiBJ,SAAgBC,8BACdC,OACAC,YAAAA;AAEA,MAAMC,gBAAsD,CAAA;AAE5DnB,mBAAiBoB,QAAQ,SAACC,KAAD;AACvB,QAAMC,eAA8BnB,2BAA2BkB,GAAD;AAI9D,QAAI,QAAOJ,SAAP,OAAA,SAAOA,MAAQI,GAAH,OAAY,UAAU;AACpC,UAAMjE,QAAQ6D,SAAH,OAAA,SAAGA,MAAQI,GAAH;AACnB,UAAME,cAAiBnE,SAAS,KAAK,KAArB;AAChB,UAAMoE,YAAepE,SAAS,KAAK,MAArB;AAEd,UAAMqE,cAAczE,kBAAkBuE,UAAD;AACrC,UAAMG,YAAY1E,kBAAkBwE,QAAD;AAEnC,UAAMG,cAAc;QAClBC,OAAOH,YAAYrE;QACnByE,KAAKH,UAAUtE;QACfC,MAAMoE,YAAYpE;MAHA;AAOpB,UAAI6D,eAAe/F,WAAW2G,UAAU;AACtCX,sBAAcb,aAAaqB;MAC5B;AAGD,UAAIT,eAAe/F,WAAW4G,YAAY;AACxCZ,sBAAcd,aAAasB;MAC5B;IACF;AAGD,QAAI/B,MAAMC,QAAQoB,SAAd,OAAA,SAAcA,MAAQI,GAAH,CAAnB,GAA6B;AAC/B,UAAMjE,SAAQ6D,SAAH,OAAA,SAAGA,MAAQI,GAAH;AAEnB,UAAI,OAAOjE,OAAM,CAAD,MAAQ,eAAe,OAAOA,OAAM,CAAD,MAAQ,aAAa;AACtE,YAAMqE,eAAczE,kBAAkBI,UAAD,OAAA,SAACA,OAAQ,CAAH,GAAOkE,YAAb;AACrC,YAAMI,aAAY1E,kBAAkBI,UAAD,OAAA,SAACA,OAAQ,CAAH,GAAOkE,YAAb;AAEnC,YAAM3B,SAASD,qBAAqBtC,UAAD,OAAA,SAACA,OAAQ,CAAH,CAAN;AAEnC+D,sBAAcE,GAAD,IAAQ;UACnBO,OAAOH,aAAYrE;UACnByE,KAAKH,WAAUtE;UACfC,MAAMoE,aAAYpE;UAClBsC;QAJmB;AAOrB,YAAI8B,aAAYpE,SAASqE,WAAUrE,MAAM;AACvC,gBAAM,IAAIE,MACR,6EADI;QAGP;MACF;IACF;EACF,CAtDD;AAwDA,SAAO4D;AACR;ACzFD,SAAgBa,kBAIdJ,OAIAK,WAIAC,eAIAvC,QAAAA;AAGA,MAAMwC,sBAAsBD,gBAAgBN;AAG5C,MAAIQ,SAASD,sBAAsBF;AAGnC,MAAItC,QAAQ;AACVyC,aAASzC,OAAOyC,MAAD;EAChB;AAED,SAAOA;AACR;AC5BD,SAAgBC,gBACdT,OACAC,KACAS,QAAAA;AAEA,MAAMC,WAAWD,UAAUV,SAASU,UAAUT;AAE9C,SAAOU;AACR;ACfD,SAAgBC,aACdpF,OACAqF,QACAC,QACAC,QACAC,QAAAA;AAEA,UAASF,SAASD,WAAWrF,QAAQuF,WAAYC,SAASD,UAAUF;AACrE;ACFD,SAAgBI,sBACdC,QACAC,UAAAA;AAKA,MAAM3F,QAAQoF,aACZ,OAAOM,OAAOnD,WAAW,aAAamD,OAAOnD,OAAOoD,QAAd,IAA0BA,WAChED,UAAM,OAAN,SAAAA,OAAQlB,UAAS,IACjBkB,UAAM,OAAN,SAAAA,OAAQjB,QAAO,GACf,GACA,CALwB;AAQ1B,SAAO;IACLzE;IACAC,MAAMyF,UAAF,OAAA,SAAEA,OAAQzF;EAFT;AAIR;ACrBD,IAAM2F,oBAAoB/C,OAAOC,OAAOnF,eAAd,EAA+BkI,OACvD,SAAAC,GAAC;AAAA,SAAIA,MAAM;AAAV,CADuB;AAI1B,SAAgBC,oBACdzH,IACA0H,SAAAA;AAEA,MAAMC,OAAOpD,OAAOoD,KAAKD,OAAZ;AACb,MAAME,aAAaD,KAAKzF,SAAS,SAAd;AACnB,MAAM2F,aAAU,eAAeD,aAAa,aAAa;AACzD5H,KAAG8H,MAAMD,aAAaA;AACvB;AAED,SAAgBE,iBACdL,SACAL,UACArH,IAAAA;AAEA,MAAI,CAACA,GAAI;AACT,MAAMgI,YAAYC,mBAAmBP,SAASL,QAAV;AACpC,MAAMhC,UAAU6C,iBAAiBR,SAASL,QAAV;AAChCrH,KAAG8H,MAAME,YAAYA;AACrBhI,KAAG8H,MAAMzC,UAAUA;AACpB;AAED,SAAgB6C,iBACdR,SACAL,UAAAA;AAEA,MAAMc,gBACJT,QAAQ,SAAD,KAAeP,sBAAsBO,QAAQ,SAAD,GAAaL,QAArB;AAE7C,MACE,OAAOc,kBAAkB,eACzB,OAAOA,cAAczG,UAAU,eAC/B,OAAOyG,cAAcxG,SAAS,aAC9B;AACA,WAAO;EACR;AAED,MAAMyG,WAAQ,KAAMD,cAAczG;AAElC,SAAO0G;AACR;AAED,SAAgBH,mBACdP,SACAL,UAAAA;AAEA,MAAMW,YAAoBV,kBAAkBe,OAAO,SAACC,KAAK3C,KAAN;AACjD,QAAM4C;;MAEJb,QAAQ/B,GAAD,KAASwB,sBAAsBO,QAAQ/B,GAAD,GAAO0B,QAAf;;AAEvC,QACE,OAAOkB,iBAAiB,eACxB,OAAOA,aAAa7G,UAAU,eAC9B,OAAO6G,aAAa5G,SAAS,aAC7B;AACA,aAAO2G;IACR;AAED,QAAMF,WAAczC,MAAN,MAAa4C,aAAa7G,QAAQ6G,aAAa5G,OAA/C;AAEd,WAAO2G,MAAMF;EACd,GAAE,EAhBuB;AAkB1B,SAAOJ;AACR;AAMD,SAAgBQ,YAAYC,SAAAA;AAC1B,MAAMzI,KAAKyI,QAAQzI;AACnB,MAAI,CAACA,GAAI;AACTA,KAAG8H,MAAME,YAAY;AACrBhI,KAAG8H,MAAMzC,UAAU;AACpB;SClFeqD,gCACd3I,MACAG,MACA0G,QACA+B,+BAAAA;AAEA,MAAI/J,SAASmB,KAAKM,MAAMH,KAAKO;AAC7B,MAAI9B,SAASoB,KAAKQ,OAAOL,KAAKS;AAC9B,MAAI7B,OAAOiB,KAAKS;AAChB,MAAI3B,OAAOkB,KAAKO;AAGhB3B,YAAUiI,OAAOgC;AACjB/J,UAAQ+H,OAAOgC;AACfhK,YAAUgI,OAAOiC;AACjB/J,UAAQ8H,OAAOiC;AAEf,MAAIF,+BAA+B;AACjC,QAAI/B,OAAOiC,IAAI9I,KAAKM,MAAMH,KAAKO,QAAQ;AACrC7B,eAAS;IACV;AACD,QAAIgI,OAAOgC,IAAI7I,KAAKQ,OAAOL,KAAKS,OAAO;AACrChC,eAAS;IACV;AACD,QAAIG,OAAOoB,KAAK4I,eAAe5I,KAAKO,QAAQ;AAC1C3B,aAAOoB,KAAK4I,eAAe5I,KAAKO;IACjC;AACD,QAAI5B,OAAOqB,KAAK6I,cAAc7I,KAAKS,OAAO;AACxC9B,aAAOqB,KAAK6I,cAAc7I,KAAKS;IAChC;EACF;AAED,MAAMqI,SAAS,IAAIvK,OAAO;IACxBE;IACAC;IACAC;IACAC;EAJwB,CAAX;AAOf,SAAOkK;AACR;SC3CeC,mBACdC,kBACAC,gBACA5C,WAAAA;AAEA,MAAM6C,OAAOD,iBAAiBD;AAG9B,MAAMG,eACHC,KAAKC,IAAIL,gBAAT,IAA6BI,KAAKC,IAAIJ,cAAT,MAA6BC,OAAO,KAAK;AACzE,MAAMI,gBAAgBjD,YAAY8C;AAGlC,MAAMpE,QAAQqE,KAAKG,IAAIlD,YAAYiD,eAAe,CAApC;AAEd,SAAOvE;AACR;ACXD,SAAgByE,qBACdC,WACAC,aAAAA;AAEA,MAAM1D,QAAqByD,UAArBzD,OAAOC,MAAcwD,UAAdxD,KAAKxE,OAASgI,UAAThI;AAElB,MAAIA,SAAS,KAAK;AAChB,QAAMsD,QAAQ2E,cAAc;AAC5B1D,YAAQA,QAAQjB;AAChBkB,UAAMA,MAAMlB;EACb;AAED,MAAItD,SAAS,MAAM;AACjB,QAAMkI,aAAa3D,QAAQ;AAC3B,QAAM4D,WAAW3D,MAAM;AACvBD,YAAQ6D,OAAOC,aAAaH;AAC5B1D,UAAM4D,OAAOC,aAAaF;EAC3B;AAED,MAAInI,SAAS,MAAM;AACjB,QAAMkI,cAAa3D,QAAQ;AAC3B,QAAM4D,YAAW3D,MAAM;AACvBD,YAAQ6D,OAAOE,cAAcJ;AAC7B1D,UAAM4D,OAAOE,cAAcH;EAC5B;AAED,SAAO;IACL5D;IACAC;EAFK;AAIR;ACzBD,IAAM+D,gBAAmC;EACvChE,OAAO;EACPC,KAAK;EACLxE,MAAM;AAHiC;AAMzC,SAAgBwI,gDACdpK,MACAG,MACAwH,SACAd,QACApB,YACAmD,+BAAAA;AAGA,MAAMhE,aAAgC+C,QAAQ/C,cAAcuF;AAC5D,MAAMtF,aAAgC8C,QAAQ9C,cAAcsF;AAE5D,MAAA,wBAGIR,qBAAqB/E,YAAY5E,KAAKY,KAAlB,GAFfyJ,oBADT,sBACElE,OACKmE,kBAFP,sBAEElE;AAEF,MAAA,yBAGIuD,qBAAqB9E,YAAY7E,KAAKU,MAAlB,GAFf6J,oBADT,uBACEpE,OACKqE,kBAFP,uBAEEpE;AAIF,MAAIvH,SAASmB,KAAKM,MAAMH,KAAKO;AAC7B,MAAI9B,SAASoB,KAAKQ,OAAOL,KAAKS;AAC9B,MAAI7B,OAAOiB,KAAKS;AAChB,MAAI3B,OAAOkB,KAAKO;AAEhB,MAAInB,mBAAmB;AACvB,MAAIC,iBAAiB;AACrB,MAAIoG,eAAe/F,WAAW2G,UAAU;AACtCjH,uBAAmB8J,mBACjBqB,mBACAC,iBACArK,KAAKO,SAASV,KAAKU,MAHgB;AAKrCrB,qBAAiBD;EAClB;AACD,MAAIF,mBAAmB;AACvB,MAAIC,iBAAiB;AACrB,MAAIsG,eAAe/F,WAAW4G,YAAY;AACxCpH,uBAAmBgK,mBACjBmB,mBACAC,iBACAnK,KAAKS,QAAQZ,KAAKY,KAHiB;AAKrCzB,qBAAiBD;EAClB;AAGD,MAAIqL,oBAAoB,GAAG;AACzB1L,aAASA,SAAS0L,oBAAoBnL;EACvC;AACD,MAAIoL,kBAAkB,GAAG;AACvBzL,WAAOA,OAAOyL,kBAAkBnL;EACjC;AACD,MAAIgL,oBAAoB,GAAG;AACzBzL,aAASA,SAASyL,oBAAoBnL;EACvC;AACD,MAAIoL,kBAAkB,GAAG;AACvBxL,WAAOA,OAAOwL,kBAAkBnL;EACjC;AAGDP,YAAUiI,OAAOgC;AACjB/J,UAAQ+H,OAAOgC;AACfhK,YAAUgI,OAAOiC;AACjB/J,UAAQ8H,OAAOiC;AAGf,MAAIF,+BAA+B;AACjC,QAAM6B,kBAAkB5D,OAAOiC,IAAI9I,KAAKM,MAAMH,KAAKO;AACnD,QAAMgK,mBAAmB7D,OAAOgC,IAAI7I,KAAKQ,OAAOL,KAAKS;AACrD,QAAM+J,mBACJ9D,OAAOiC,IAAI9I,KAAKS,SAASN,KAAK4I,eAAe5I,KAAKO;AACpD,QAAMkK,kBACJ/D,OAAOgC,IAAI7I,KAAKO,QAAQJ,KAAK6I,cAAc7I,KAAKO;AAElD,QAAI+J,mBAAmBE,kBAAkB;AACvCvL,yBAAmB;AACnBC,uBAAiB;AACjBR,eAAS;AACTE,aAAOoB,KAAK4I,eAAe5I,KAAKO;IACjC;AACD,QAAIgK,oBAAoBE,iBAAiB;AACvC1L,yBAAmB;AACnBC,uBAAiB;AACjBP,eAAS;AACTE,aAAOqB,KAAK6I,cAAc7I,KAAKS;IAChC;AAED,QAAI,CAAC6J,mBAAmBE,kBAAkB;AACxC9L,eAASmB,KAAKM,MAAMH,KAAKO,SAASmG,OAAOiC;AACzC/J,aAAOoB,KAAK4I,eAAe5I,KAAKO;AAChC,UAAM8F,YAAYzH,OAAOF;AACzBO,yBAAmB8J,mBACjBqB,mBACAC,iBACAhE,SAHmC;AAKrCnH,uBAAiB;AACjB,UAAIkL,oBAAoB,GAAG;AACzB1L,iBAASA,SAAS0L,oBAAoBnL;MACvC;IACF;AACD,QAAI,CAACsL,oBAAoBE,iBAAiB;AACxChM,eAASoB,KAAKQ,OAAOL,KAAKS,QAAQiG,OAAOgC;AACzC/J,aAAOqB,KAAK6I,cAAc7I,KAAKS;AAC/B,UAAM4F,aAAY1H,OAAOF;AACzBM,yBAAmBgK,mBACjBmB,mBACAC,iBACA9D,UAHmC;AAKrCrH,uBAAiB;AACjB,UAAIkL,oBAAoB,GAAG;AACzBzL,iBAASA,SAASyL,oBAAoBnL;MACvC;IACF;AAED,QAAIuL,mBAAmB,CAACE,kBAAkB;AACxC9L,eAAS;AACTE,aAAOiB,KAAKS,SAASoG,OAAOiC;AAC5B,UAAMtC,cAAYzH,OAAOF;AACzBO,yBAAmB;AACnBC,uBAAiB6J,mBACfqB,mBACAC,iBACAhE,WAHiC;AAKnC,UAAIgE,kBAAkB,GAAG;AACvBzL,eAAOA,OAAOyL,kBAAkBnL;MACjC;IACF;AACD,QAAIqL,oBAAoB,CAACE,iBAAiB;AACxChM,eAAS;AACTE,aAAOkB,KAAKO,QAAQsG,OAAOgC;AAC3B,UAAMrC,cAAY1H,OAAOF;AACzBM,yBAAmB;AACnBC,uBAAiB+J,mBACfmB,mBACAC,iBACA9D,WAHiC;AAKnC,UAAI8D,kBAAkB,GAAG;AACvBxL,eAAOA,OAAOwL,kBAAkBnL;MACjC;IACF;EACF;AAED,MAAM8J,SAAS,IAAIvK,OAAO;IACxBE;IACAC;IACAC;IACAC;IACAG;IACAC;IACAC;IACAC;EARwB,CAAX;AAWf,SAAO4J;AACR;SC9Ke4B,qCACdlD,SACAsB,QAAAA;AAEA,MAAM6B,cAAW,SAAA,CAAA,GACZnD,OADY;AAIjB,MAAImD,YAAYlG,YAAY;AAC1BkG,gBAAYlG,aAAZ,SAAA,CAAA,GACK+C,QAAQ/C,YADb;MAEEuB,OAAO2E,YAAYlG,WAAWuB,QAAQ8C,OAAO/J;MAC7CkH,KAAK0E,YAAYlG,WAAWwB,MAAM6C,OAAO9J;IAH3C,CAAA;EAKD;AACD,MAAI2L,YAAYjG,YAAY;AAC1BiG,gBAAYjG,aAAZ,SAAA,CAAA,GACK8C,QAAQ9C,YADb;MAEEsB,OAAO2E,YAAYjG,WAAWsB,QAAQ8C,OAAO7J;MAC7CgH,KAAK0E,YAAYjG,WAAWuB,MAAM6C,OAAO5J;IAH3C,CAAA;EAKD;AAED,SAAOyL;AACR;SCxBeC,+BACdvF,OACAmC,SACAlC,YAAAA;AAEA,MACED,MAAM1E,cACN0E,MAAMwF,iBACNxF,MAAMyF,kCACN;AACA,WAAO;EACR;AAED,MACG,CAAC,CAACtD,QAAQ/C,cAAca,eAAe/F,WAAW4G,cAClD,CAAC,CAACqB,QAAQ9C,cAAcY,eAAe/F,WAAW2G,UACnD;AACA,WAAO;EACR;AAED,SAAO;AACR;ACxBM,IAAM6E,QAAQ,SAARA,OAASC,KAAaC,KAAa1B,KAA3B;AAAA,SACnBH,KAAK6B,IAAI7B,KAAKG,IAAIyB,KAAKC,GAAd,GAAoB1B,GAA7B;AADmB;ICoCR2B,UAAb,WAAA;AAeE,WAAAA,SAAYtL,SAAZ;AACE,SAAKE,KAAKF,QAAQE;AAClB,SAAKuF,QAAQzF,QAAQyF;AACrB,SAAKC,aAAa1F,QAAQ0F;AAC1B,SAAK6F,6BACHvL,QAAQuL,8BAA8B;AACxC,SAAK1L,KAAKC,SAAQ;AAClB,SAAK8H,UAAUpC,8BAA8B,KAAKC,OAAO,KAAKC,UAAlB;AAC5C,SAAKqB,WAAW;AAChB,SAAKQ,WAAW;AAEhB,SAAKiE,kBAAkBxL,QAAQyF,MAAMtB,MAArC;AAEAwD,wBAAoB3H,QAAQE,IAAI,KAAK0H,OAAlB;EACpB;AA7BH,MAAA,SAAA0D,SAAA;AAAA,SA+BEG,cAAA,SAAA,YAAYC,WAAZ;AACE,SAAKjG,QAAL,SAAA,CAAA,GAAkB,KAAKA,OAAUiG,SAAjC;AACA,SAAK9D,UAAUpC,8BAA8BkG,WAAW,KAAKhG,UAAjB;AAC5C,SAAK8F,kBAAkBE,UAAUvH,MAAjC;AAEA,WAAO;EACR;AArCH,SAuCEwH,sBAAA,SAAA,oBAAoBvL,MAAY0G,QAAhC;AAEE4B,gBAAY,IAAD;AAEX,SAAKzI,OAAO,IAAIF,KAAK;MACnBG,IAAI,KAAKuF,MAAMwF,iBAAiB,KAAK/K;MACrCa,YAAY,KAAK0E,MAAM1E;MACvBX;IAHmB,CAAT;AAMZ,QAAMwL,8BAA8BZ,+BAClC,KAAKvF,OACL,KAAKmC,SACL,KAAKlC,UAH2D;AAMlE,QACE,OAAO,KAAKD,MAAMoG,gBAAgB,YAClC,OAAO,KAAKpG,MAAMqG,cAAc,UAChC;AACA,WAAK5C,SAAS,IAAIvK,OAAO;QACvBE,QAAQ,KAAK4G,MAAMoG;QACnB/M,QAAQ,KAAK2G,MAAMoG;QACnB9M,MAAM,KAAK0G,MAAMqG;QACjB9M,MAAM,KAAKyG,MAAMqG;MAJM,CAAX;AAQd,WAAKC,kBAAL;AAEA,aAAO;IACR;AAED,QAAIH,6BAA6B;AAC/B,WAAK1C,SAASmB,gDACZ,KAAKpK,MACLG,MACA,KAAKwH,SACLd,QACA,KAAKpB,YACL,KAAKD,MAAMoD,6BANgD;AAS7D,WAAKmD,gBAAgBlB,qCACnB,KAAKlD,SACL,KAAKsB,MAFkD;IAI1D,OAAM;AACL,WAAKA,SAASN,gCACZ,KAAK3I,MACLG,MACA0G,QACA,KAAKrB,MAAMoD,6BAJgC;IAM9C;AAGD,SAAKkD,kBAAL;AAEA,WAAO;EACR;AAnGH,SAqGEE,yBAAA,SAAA,uBAAuBC,cAAvB;AAEE,QAAMC,gBAAgB,KAAKpF,aAAa;AACxC,QAAImF,iBAAiB,KAAKnF,UAAU;AAClC,UAAImF,cAAc;AAChB,aAAKzG,MAAM2G,WAAW,KAAK3G,MAAM2G,QAAQ,IAAnB;MACvB,WAAU,CAACD,eAAe;AACzB,aAAKE,kBAAL;AACA,aAAKN,kBAAL;AACA,aAAKtG,MAAM6G,UAAU,KAAK7G,MAAM6G,OAAO,IAAlB;MACtB;IACF;AACD,SAAKvF,WAAWmF;EACjB;AAlHH,SAoHEG,oBAAA,SAAA,oBAAA;AACE,QAAME,gBAAgBpB,MAAM3B,KAAKgD,MAAM,KAAKjF,QAAhB,GAA2B,GAAG,CAA/B;AAC3B,SAAKkF,uBAAuBF,aAA5B;EACD;AAvHH,SAyHER,oBAAA,SAAA,oBAAA;AACE,QAAI,KAAKtG,MAAMiH,YAAY,KAAKnB,2BAA4B;AAC5D,QAAM3D,UAAU,KAAKoE,iBAAiB,KAAKpE;AAC3CK,qBAAiBL,SAAS,KAAKL,UAAU,KAAKrH,EAA9B;EACjB;AA7HH,SA+HEuM,yBAAA,SAAA,uBAAuBE,cAAvB;AACE,SAAKpF,WAAWoF;AAChB,SAAKlH,MAAMmH,oBAAoB,KAAKnH,MAAMmH,iBAAiB,KAAKrF,QAAjC;AAC/B,SAAK9B,MAAMoH,YAAY,KAAKpH,MAAMoH,SAAS,IAApB;EACxB;AAnIH,SAqIErB,oBAAA,SAAA,kBAAkBrH,QAAlB;AACE,SAAKA,SAASD,qBAAqBC,MAAD;EACnC;AAvIH,SAyIE2I,uBAAA,SAAA,qBAAqB9M,SAArB;AACE,SAAK0F,aAAa1F,QAAQ0F;AAC1B,SAAK6F,6BACHvL,QAAQuL,8BAA8B;EACzC;AA7IH,SA+IEwB,iBAAA,SAAA,eAAejG,QAAf;AACE,QAAI,CAAC,KAAKoC,OAAQ,QAAO;AAEzB,QAAM8D,aAAa,KAAKtH,eAAe/F,WAAW2G;AAClD,QAAM6F,gBAAgB,KAAKpF,aAAa;AAExC,QAAMX,QAAQ4G,aAAa,KAAK9D,OAAOpK,SAAS,KAAKoK,OAAOrK;AAC5D,QAAMwH,MAAM2G,aAAa,KAAK9D,OAAOlK,OAAO,KAAKkK,OAAOnK;AACxD,QAAMkO,QAAQD,aAAa,KAAK9D,OAAOhK,SAAS,KAAKgK,OAAOjK;AAC5D,QAAMiO,IAAIF,aAAalG,OAAOiC,IAAIjC,OAAOgC;AAGzC,QAAMoD,eAAerF,gBAAgBT,OAAOC,KAAK6G,CAAb;AACpC,SAAKjB,uBAAuBC,YAA5B;AAGA,QAAIA,cAAc;AAChB,UAAMS,eAAenG,kBAAkBJ,OAAO6G,OAAOC,GAAG,KAAK/I,MAAvB;AACtC,WAAKsI,uBAAuBE,YAA5B;AACA,WAAKZ,kBAAL;IACD,WAAUI,eAAe;AAExB,WAAK5E,WAAW4D,MACd3B,KAAKgD,MAAMhG,kBAAkBJ,OAAO6G,OAAOC,GAAG,KAAK/I,MAAvB,CAA5B,GACA,GACA,CAHmB;AAKrB,WAAK4H,kBAAL;IACD;AAED,WAAO;EACR;AA9KH,SAAAT;AAAA,EAAA;IC9Ba6B,OAAb,WAAA;AAOE,WAAAA,MAAYC,QAAZ;AAOE,SAAK/M,kBAAkB+M,OAAO/M;AAC9B,SAAKQ,QAAQuM,OAAOvM;AACpB,SAAKF,SAASyM,OAAOzM;AACrB,SAAKqI,eAAeoE,OAAOpE;AAC3B,SAAKC,cAAcmE,OAAOnE;EAC3B;AAnBH,MAAA,SAAAkE,MAAA;AAAA,SAqBEE,aAAA,SAAA,WAAW9I,QAAX;AACE,QACEA,OAAO1D,UAAU,KAAKA,SACtB0D,OAAO5D,WAAW,KAAKA,UACvB4D,OAAO0E,gBAAgB,KAAKA,eAC5B1E,OAAOyE,iBAAiB,KAAKA,cAC7B;AACA,aAAO;IACR;AACD,WAAO;EACR;AA/BH,SAiCEsE,UAAA,SAAA,QAAQ/I,QAAR;AACE,SAAK1D,QAAQ0D,OAAO1D;AACpB,SAAKF,SAAS4D,OAAO5D;AACrB,SAAKqI,eAAezE,OAAOyE;AAC3B,SAAKC,cAAc1E,OAAO0E;AAC1B,WAAO;EACR;AAvCH,SAAAkE;AAAA,EAAA;ICNaI,SAAb,WAAA;AAME,WAAAA,QAAYzE,GAAWC,GAAvB;AACE,SAAKD,IAAIA;AACT,SAAKC,IAAIA;AACT,SAAKyE,KAAK;AACV,SAAKC,KAAK;EACX;AAXH,MAAA,SAAAF,QAAA;AAAA,SAaEG,YAAA,SAAA,UAAU5E,GAAWC,GAArB;AACE,SAAKyE,KAAK1E,IAAI,KAAKA;AACnB,SAAK2E,KAAK1E,IAAI,KAAKA;AACnB,SAAKD,IAAIA;AACT,SAAKC,IAAIA;AACT,WAAO;EACR;AAnBH,SAAAwE;AAAA,EAAA;SCAgBI,uBAAAA;AACd,MAAIC,wBAAwB;AAC5B,MAAI;AACF,QAAMC,OAAOpJ,OAAOqJ,eAAe,CAAA,GAAI,WAAW;MAChDC,KADgD,SAAA,MAAA;AAE9CH,gCAAwB;AACxB,eAAO;MACR;IAJ+C,CAArC;AAOb3D,WAAO+D,iBAAiB,QAAQ,MAAMH,IAAtC;AAEA5D,WAAOgE,oBAAoB,QAAQ,MAAMJ,IAAzC;EACD,SAAQK,GAAG;EAAA;AACZ,SAAON;AACR;ACQD,IAAaO,qBAAb,WAAA;AA4BE,WAAAA,oBAAA,MAAA;+BACEzI,YAAAA,aAAAA,oBAAAA,SAAa/F,WAAW2G,WAAAA,iBACxBjG,kBAAAA,KAAAA,sCACAqM,UAAAA,WAAAA,kBAAAA,SAAW,QAAA;AAEX,SAAKA,WAAWA;AAChB,SAAKhH,aAAaA;AAElB,SAAK0I,WAAW,CAAA;AAEhB,SAAKC,sBAAsB,CAAC,CAAChO;AAC7B,SAAKiO,SAASjO,mBAAd,OAAcA,kBAAmB4J;AAGjC,QAAA,wBAAe,KAAKsE,mBAAL,GAARzF,IAAP,sBAAA,CAAA,GAAUC,IAAV,sBAAA,CAAA;AACA,SAAKjC,SAAS,IAAIyG,OAAOzE,GAAGC,CAAd;AAEd,SAAK3I,OAAO,IAAI+M,KAAK;MACnBtM,OAAO;MACPF,QAAQ;MACRsI,aAAa;MACbD,cAAc;MACd3I,iBAAiB,KAAKgO,sBAAsBhO,kBAAkBmO;IAL3C,CAAT;AASZ,SAAKC,WAAW;AAGhB,SAAKC,mBAAmBf,qBAAoB;AAG5C,SAAKgB,gBAAL;AAGA,QAAI,KAAKjC,SAAU;AAEnB,SAAKkC,cAAc,KAAKN,MAAxB;AACA,SAAKO,mBAAL;AACA,SAAKC,aAAL;EACD;AApEH,EAAAX,oBAgBSY,OAAP,SAAA,KAAY/O,SAAZ;AACE,QAAMgP,YAAY,OAAO/E,WAAW;AAEpC,QAAI,CAAC+E,WAAW;AACd,YAAM,IAAIjN,MACR,0GADI;IAGP;AAED,WAAO,IAAIoM,oBAAmBnO,OAAvB;EACR;AA1BH,MAAA,SAAAmO,oBAAA;AAAA,SAsEEQ,kBAAA,SAAA,kBAAA;;AACE,KACE,iBACA,oBACA,sBACA,iBACA,sBACA,sBACA,0BACA,gBACA,sBACA,0BACA,kBACA,eACA,iBACA,qBACA,sBACA,0BACA,UACA,yBACA,SAnBF,EAoBE/I,QAAQ,SAACqJ,QAAD;AAER,YAAKA,MAAD,IAAW,MAAKA,MAAD,EAASC,KAAK,KAAlB;IAChB,CAvBD;EAwBD;AA/FH,SAiGEN,gBAAA,SAAA,cAAc1O,IAAd;AACEA,OAAG8N,iBACD,UACA,KAAKmB,eACL,KAAKT,mBAAmB;MAAEU,SAAS;IAAX,IAAoB,KAH9C;AAKAnF,WAAO+D,iBAAiB,UAAU,KAAKqB,oBAAoB,KAA3D;AACApF,WAAO+D,iBAAiB,QAAQ,KAAKqB,oBAAoB,KAAzD;AACApF,WAAO+D,iBAAiB,SAAS,KAAKqB,oBAAoB,KAA1D;AACApF,WAAO+D,iBAAiB,QAAQ,KAAKqB,oBAAoB,KAAzD;EACD;AA3GH,SA6GEC,mBAAA,SAAA,iBAAiBpP,IAAjB;;AACEA,OAAG+N,oBAAoB,UAAU,KAAKkB,eAAe,KAArD;AACAlF,WAAOgE,oBAAoB,UAAU,KAAKoB,oBAAoB,KAA9D;AACApF,WAAOgE,oBAAoB,QAAQ,KAAKoB,oBAAoB,KAA5D;AACApF,WAAOgE,oBAAoB,SAAS,KAAKoB,oBAAoB,KAA7D;AACApF,WAAOgE,oBAAoB,QAAQ,KAAKoB,oBAAoB,KAA5D;AACA,KAAA,wBAAA,KAAKE,oBAAL,OAAA,SAAA,sBAAsBC,WAAtB;EACD;AApHH,SAsHEX,qBAAA,SAAA,qBAAA;;AACE,QAAI;AACF,UAAMY,aAA0B,KAAKpB,sBAChC,KAAKC,SACNoB,SAASC;AACb,WAAKJ,kBAAkB,IAAIK,eAAe,WAAA;AAAA,eAAM,OAAKC,OAAL;MAAN,CAAnB;AACvB,WAAKN,gBAAgBO,QAAQL,UAA7B;IACD,SAAQvB,GAAG;AACV6B,cAAQC,KACN,+DADF;IAGD;EACF;AAlIH,SAoIEzB,qBAAA,SAAA,qBAAA;AAGE,QAAM0B,KAAK,KAAK5B;;MAEZ,KAAKC,OAAO4B;QACZjG,OAAOkG;AACX,QAAMC,KAAK,KAAK/B;;MAEZ,KAAKC,OAAO+B;QACZpG,OAAOqG;AAEX,WAAO,CAACL,IAAIG,EAAL;EACR;AAjJH,SAuJEjB,gBAAA,SAAA,gBAAA;;AACE,QAAA,yBAAiB,KAAKZ,mBAAL,GAAV0B,KAAP,uBAAA,CAAA,GAAWG,KAAX,uBAAA,CAAA;AACA,SAAKtJ,OAAO4G,UAAUuC,IAAIG,EAA1B;AAIA,QAAI,CAAC,KAAK3B,cAAY,iBAAA,KAAKL,aAAL,OAAA,SAAA,eAAemC,UAAS,GAAG;AAC/C,WAAK9B,WAAW;AAEhBxE,aAAOuG,sBAAsB,KAAKC,kBAAlC;IACD;EACF;AAlKH,SAwKEpB,qBAAA,SAAA,qBAAA;AACE,SAAKP,aAAL;AACA,SAAK2B,mBAAmB;MAAEC,aAAa;IAAf,CAAxB;EACD;AA3KH,SAkLED,qBAAA,SAAA,mBAAA,OAAA;;mCAAgE,CAAA,IAAA,OAA3CC,cAAAA,MAAAA;AACnB,QAAI,KAAKtC,UAAU;AACjB,WAAKA,SAASxI,QAAQ,SAAA+C,SAAO;AAC3B,YAAI+H,aAAa;AACf/H,kBAAQgD,oBAAoB,OAAKvL,MAAM,OAAK0G,MAA5C;QACD;AACD,eAAK6J,uBAAuBhI,OAA5B;MACD,CALD;IAMD;AAED,SAAK8F,WAAW;EACjB;AA7LH,SAoMEkC,yBAAA,SAAA,uBAAuBhI,SAAvB;AACE,QAAIA,QAAQlD,MAAMiH,YAAY,KAAKA,SAAU;AAC7C/D,YAAQoE,eAAe,KAAKjG,MAA5B;EACD;AAvMH,SA4ME8J,iBAAA,SAAA,iBAAA;AAME,QAAI,KAAKvC,qBAAqB;AAE5B,UAAMxN,SAAQ,KAAKyN,OAAOxN;AAE1B,UAAMH,UAAS,KAAK2N,OAAO1N;AAE3B,UAAMoI,gBAAe,KAAKsF,OAAOtF;AAEjC,UAAMC,eAAc,KAAKqF,OAAOrF;AAChC,aAAO,KAAK7I,KAAKkN,QAAQ;QACvBzM,OAAAA;QACAF,QAAAA;QACAqI,cAAAA;QACAC,aAAAA;MAJuB,CAAlB;IAMR;AAED,QAAM4H,OAAOnB,SAASC;AACtB,QAAM9O,QAAQoJ,OAAOC,cAAc2G,KAAKC;AACxC,QAAMnQ,SAASsJ,OAAOE,eAAe0G,KAAKE;AAC1C,QAAM/H,eAAe6H,KAAK7H;AAC1B,QAAMC,cAAc4H,KAAK5H;AAEzB,WAAO;MAAEpI;MAAOF;MAAQqI;MAAcC;IAA/B;EACR;AA1OH,SA+OE6F,eAAA,SAAA,eAAA;AACE,WAAO,KAAK1O,KAAKkN,QAAQ,KAAKsD,eAAL,CAAlB;EACR;AAjPH,SAuPEI,yBAAA,SAAA,yBAAA;AACE,WAAO,KAAK5Q,KAAKiN,WAAW,KAAKuD,eAAL,CAArB;EACR;AAzPH,SAoQEK,cAAA,SAAA,cAAA;AACE,WAAO,KAAK7C;EACb;AAtQH,SA2QE8C,gBAAA,SAAA,cAAclR,SAAd;AACE,QAAMmR,aAAa,IAAI7F,QAAJ,SAAA,CAAA,GACdtL,SADc;MAEjB0F,YAAY,KAAKA;MACjB6F,4BAA4B,KAAKmB;IAHhB,CAAA,CAAA;AAKnByE,eAAWxF,oBAAoB,KAAKvL,MAAM,KAAK0G,MAA/C;AACA,SAAKsH,WAAW,KAAKA,WAAL,CAAA,EAAA,OACR,KAAKA,UADG,CACO+C,UADP,CAAA,IAEZ,CAACA,UAAD;AACJ,SAAKR,uBAAuBQ,UAA5B;AAKA,QAAI,KAAKH,uBAAL,GAA+B;AACjC,WAAKnB,OAAL;IACD;AACD,WAAOsB;EACR;AA9RH,SAmSEC,oBAAA,SAAA,kBAAkBvR,KAAlB;AACE,QAAI,CAAC,KAAKuO,SAAU;AACpB,SAAKA,WAAW,KAAKA,SAAS3G,OAAO,SAAAvH,IAAE;AAAA,aAAIA,GAAGL,OAAOA;IAAd,CAAvB;EACjB;AAtSH,SA2SEwR,yBAAA,SAAA,uBAAuBxR,KAAY4F,OAAnC;AACE,QAAI,KAAK2I,UAAU;AACjB,WAAKA,WAAW,KAAKA,SAASkD,IAAI,SAAApR,IAAE;AAClC,YAAIA,GAAGL,OAAOA,KAAI;AAChB,iBAAOK,GAAGuL,YAAYhG,KAAf;QACR;AACD,eAAOvF;MACR,CALe;IAMjB;AAED,SAAK2P,OAAL;EACD;AAtTH,SA2TE0B,qBAAA,SAAA,mBAAmB5I,SAAnB;AACED,gBAAYC,OAAD;EACZ;AA7TH,SAkUEkH,SAAA,SAAA,SAAA;AAGE,QAAA,yBAAiB,KAAKtB,mBAAL,GAAV0B,KAAP,uBAAA,CAAA,GAAWG,KAAX,uBAAA,CAAA;AACA,SAAKtJ,OAAO4G,UAAUuC,IAAIG,EAA1B;AAEA,SAAKtB,aAAL;AACA,SAAK2B,mBAAmB;MAAEC,aAAa;IAAf,CAAxB;EACD;AA1UH,SA8UEc,wBAAA,SAAA,sBAAsBtR,IAAtB;AAEE,SAAKoP,iBAAiB,KAAKhB,MAA3B;AAEA,SAAKA,SAASpO;AACd,SAAKmO,sBAAsB,CAAC,CAACnO;AAC7B,SAAKE,OAAO,IAAI+M,KAAK;MACnBtM,OAAO;MACPF,QAAQ;MACRsI,aAAa;MACbD,cAAc;MACd3I,iBAAiBH;IALE,CAAT;AAOZ,SAAK4O,aAAL;AACA,SAAKF,cAAc,KAAKN,MAAxB;AACA,SAAKmC,mBAAmB;MAAEC,aAAa;IAAf,CAAxB;EACD;AA9VH,SAgWEe,4BAAA,SAAA,4BAAA;AACE,SAAK/E,WAAW;AAEhB,SAAK4C,iBAAiB,KAAKhB,MAA3B;AAEA,QAAI,KAAKF,UAAU;AACjB,WAAKA,SAASxI,QAAQ,SAAA+C,SAAO;AAAA,eAAID,YAAYC,OAAD;MAAf,CAA7B;IACD;EACF;AAxWH,SA0WE+I,2BAAA,SAAA,2BAAA;;AACE,SAAKhF,WAAW;AAChB,QAAI,KAAK0B,UAAU;AACjB,WAAKA,SAASxI,QAAQ,SAAA+C,SAAO;AAAA,eAC3BA,QAAQmE,qBAAqB;UAC3BvB,4BAA4B;UAC5B7F,YAAY,OAAKA;QAFU,CAA7B;MAD2B,CAA7B;IAMD;AAED,SAAKkJ,cAAc,KAAKN,MAAxB;AACA,SAAKO,mBAAL;AACA,SAAKC,aAAL;EACD;AAxXH,SA6XE6C,qBAAA,SAAA,qBAAA;AACE5B,YAAQC,KAAK,qDAAb;AACA,QAAI,KAAK5B,UAAU;AACjB,WAAKA,WAAW,KAAKA,SAASkD,IAAI,SAAApR,IAAE;AAClC,eAAOA,GAAGuL,YAAY;UAAEiB,UAAU;QAAZ,CAAf;MACR,CAFe;IAGjB;AACD,SAAKmD,OAAL;EACD;AArYH,SA0YE+B,oBAAA,SAAA,oBAAA;AACE7B,YAAQC,KAAK,oDAAb;AACA,QAAI,KAAK5B,UAAU;AACjB,WAAKA,WAAW,KAAKA,SAASkD,IAAI,SAAApR,IAAE;AAClC,eAAOA,GAAGuL,YAAY;UAAEiB,UAAU;QAAZ,CAAf;MACR,CAFe;IAGjB;AACD,SAAKmD,OAAL;EACD;AAlZH,SAuZEgC,UAAA,SAAA,UAAA;AACE,SAAKvC,iBAAiB,KAAKhB,MAA3B;AACA,QAAI,KAAKF,UAAU;AACjB,WAAKA,SAASxI,QAAQ,SAAA+C,SAAO;AAAA,eAAID,YAAYC,OAAD;MAAf,CAA7B;IACD;AAED,SAAKyF,WAAWI;EACjB;AA9ZH,SAAAL;AAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SCvBgB2D,0BAA0BC,KAAAA;AACxCC,SAAOC,KAAKF,GAAZ,EAAiBG,QAAQ,SAACC,KAAD;AAAA,WACvBJ,IAAII,GAAD,MAAUC,UAAa,OAAOL,IAAII,GAAD;EADb,CAAzB;AAGA,SAAOJ;AACR;;ACJD,SAEgBM,yBAAyBC,OAAAA;AAIvC,MACEC,WAyBED,MAzBFC,UACAC,SAwBEF,MAxBFE,QACAC,YAuBEH,MAvBFG,WACAC,WAsBEJ,MAtBFI,UACAC,UAqBEL,MArBFK,SACAC,SAoBEN,MApBFM,QACAC,mBAmBEP,MAnBFO,kBACAC,UAkBER,MAlBFQ,SACAC,aAiBET,MAjBFS,YACAC,SAgBEV,MAhBFU,QACAC,UAeEX,MAfFW,SACAC,UAcEZ,MAdFY,SACAC,UAaEb,MAbFa,SACAC,QAYEd,MAZFc,OACAC,SAWEf,MAXFe,QACAC,SAUEhB,MAVFgB,QACAC,SASEjB,MATFiB,QACAC,gCAQElB,MARFkB,+BACAC,mCAOEnB,MAPFmB,kCACAC,QAMEpB,MANFoB,OACAC,cAKErB,MALFqB,aACAC,gBAIEtB,MAJFsB,eACAC,aAGEvB,MAHFuB,YACAC,aAEExB,MAFFwB,YACGC,OAzBL,8BA0BIzB,OA1BJ,SAAA;AA4BA,MAAM0B,gBAAgBlC,0BAA0B;IAC9CS;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EAxB8C,CAAD;AA2B/C,SAAO;IACLE;IACAD;EAFK;AAIR;SC/DeE,oBAAoBC,YAAAA;AAClCC,8BAAU,WAAA;AACR,QAAMC,WAAW,OAAOC,WAAW;AAEnC,QAAMC,aAAaJ,sBAAsBK;AAEzC,QAAI,CAACH,YAAY,CAACF,cAAc,CAACI,YAAY;AAC3C,YAAM,IAAIE,MACR,iFADI;IAGP;EACF,GAAE,CAACN,UAAD,CAVM;AAWV;ICZYO,kBAAkBC,aAAAA,QAAMC,cACnC,IAD6B;SCCfC,wBAAAA;AACd,MAAMC,yBAAqBC,yBAAWL,eAAD;AACrC,MAAML,WAAW,OAAOC,WAAW;AACnC,MAAID,UAAU;AACZ,WAAO;EACR;AAED,MAAI,CAACS,oBAAoB;AACvB,UAAM,IAAIL,MACR,sHADI;EAGP;AAED,SAAOK;AACR;SCXeE,YAAmCzC,OAAAA;AACjD,MAAM4B,aAAaU,sBAAqB;AACxC,MAAMI,UAAMC,qBAAU,IAAJ;AAClB,MAAA,wBAA0B5C,yBAAyBC,KAAD,GAA1C0B,gBAAR,sBAAQA;AAERC,sBAAoBC,UAAD;AAEnB,MAAA,gBAA8BgB,uBAAQ,GAA/BC,UAAP,UAAA,CAAA,GAAgBC,aAAhB,UAAA,CAAA;AAGAjB,8BAAU,WAAA;AACR,QAAIkB;AACJ,QAAIL,IAAIM,mBAAmBC,aAAa;AACtC,UAAMC,UAAgC;QACpCC,IAAIT,IAAIM;QACRhD,OAAO0B;MAF6B;AAItCqB,mBAAanB,cAAH,OAAA,SAAGA,WAAYwB,cAAcF,OAA1B;AACbJ,iBAAWC,UAAD;IACX,OAAM;AACL,YAAM,IAAIb,MACR,gFADI;IAGP;AAED,WAAO,WAAA;AACL,UAAIa,YAAY;AACdnB,sBAAU,OAAV,SAAAA,WAAYyB,kBAAkBN,WAAWO,EAAzC;MACD;IACF;EACF,GAAE,CAAA,CApBM;AAuBTzB,8BAAU,WAAA;AACR,QAAIgB,SAAS;AACX,UAAI7C,MAAMC,UAAU;AAClB2B,sBAAU,OAAV,SAAAA,WAAY2B,mBAAmBV,OAA/B;AACAjB,sBAAU,OAAV,SAAAA,WAAY4B,uBAAuBX,QAAQS,IAAI5B,aAA/C;MACD,OAAM;AACLE,sBAAU,OAAV,SAAAA,WAAY4B,uBAAuBX,QAAQS,IAAI5B,aAA/C;MACD;IACF;EACF,GAAE,CACD1B,MAAMC,UACND,MAAME,QACNF,MAAMG,WACNH,MAAMI,UACNJ,MAAMK,SACNL,MAAMM,QACNN,MAAMO,kBACNP,MAAMQ,SACNR,MAAMS,YACNT,MAAMU,QACNV,MAAMW,SACNX,MAAMY,SACNZ,MAAMa,SACNb,MAAMc,OACNd,MAAMe,QACNf,MAAMgB,QACNhB,MAAMiB,QACNjB,MAAMkB,+BACNlB,MAAMmB,kCACNnB,MAAMoB,OACNpB,MAAMqB,aACNrB,MAAMsB,eACNtB,MAAMuB,YACNvB,MAAMwB,UAxBL,CATM;AAoCT,SAAO;IAAEkB;IAAKd;IAAYiB;EAAnB;AACR;SCxEeY,SAASzD,OAAAA;AACvB,MAAA,wBAAgCD,yBAAyBC,KAAD,GAAhD0B,gBAAR,sBAAQA,eAAeD,OAAvB,sBAAuBA;AACvB,MAAA,eAAgBgB,YAA4Bf,aAAjB,GAAnBgB,MAAR,aAAQA;AACR,SACEN,aAAAA,QAAAA,cAAA,OAAA,OAAA,OAAA;IAAKM;KAAcjB,IAAAA,GAChBzB,MAAM0D,QADT;AAIH;ACVD,IAAMC,gBAAgB;EACpBC,QAAQ;AADY;AAStB,SAAgBC,iBAAiBC,OAAAA;AAC/B,MAAIC,MAAMC,QAAQF,MAAMtC,UAApB,GAAiC;AACnC,QAAMyC,kBAAkBC,kBAAkBJ,MAAMtC,WAAW,CAAjB,CAAD;AACzC,QAAM2C,gBAAgBD,kBAAkBJ,MAAMtC,WAAW,CAAjB,CAAD;AAEvC,QAAIyC,gBAAgBG,SAAS,QAAQD,cAAcC,SAAS,MAAM;AAChE,aAAO;QACLC,KAAQC,KAAKC,IAAIJ,cAAcK,KAAvB,IAAgC,KAArC;QACHC,QAAWH,KAAKC,IAAIN,gBAAgBO,KAAzB,IAAkC,KAAvC;MAFD;IAIR;AAED,QAAIP,gBAAgBG,SAAS,OAAOD,cAAcC,SAAS,KAAK;AAAA,UAAA,uBAAA;AAC9D,UAAMM,cAAU,yBAAA,uBACdZ,MAAMxC,kBADQ,OAAA,SACd,qBAAqBqD,sBAArB,MADc,OAAA,wBACkChB;AAClD,UAAMU,MAAMC,KAAKC,IAAIG,WAAWd,SAAS,OAAOO,cAAcK,KAAlD,IAA2D;AACvE,UAAMC,SACJH,KAAKC,IAAIG,WAAWd,SAAS,OAAOK,gBAAgBO,KAApD,IAA6D;AAC/D,aAAO;QACLH,KAAQA,MAAL;QACHI,QAAWA,SAAL;MAFD;IAIR;EACF;AAED,MAAIX,MAAM1C,OAAO;AACf,QAAMA,QAAQ0C,MAAM1C,SAAS;AAC7B,QAAMwD,WAAWN,KAAKC,IAAInD,KAAT,IAAkB,KAAK;AAExC,WAAO;MACLiD,KAAQO,WAAL;MACHH,QAAWG,WAAL;IAFD;EAIR;AAED,SAAO,CAAA;AACR;SC9CeC,cAAcf,OAAAA;AAC5B,SAAOA,MAAMgB,QACT;IACEC,iBAAe,SAASjB,MAAMgB,QAAf;IACfE,oBAAoB;IACpBC,gBAAgB;EAHlB,IAKA,CAAA;AACL;;ACHD,IAAMC,gBAA+B;EACnCC,UAAU;EACVd,KAAK;EACLe,MAAM;EACNC,OAAO;EACPZ,QAAQ;AAL2B;AAQrC,IAAaa,sBAAsB,SAAtBA,qBACXtF,OADiC;AAGjC,MAAA,wBAAgCD,yBAAyBC,KAAD,GAAhD0B,gBAAR,sBAAQA,eAAeD,OAAvB,sBAAuBA;AACvB,MAGE8D,QAKE9D,KALF8D,OAHF,iBAQI9D,KAJF+D,UAAAA,WAJF,mBAAA,SAIa,OAJb,gBAMEC,SAEEhE,KAFFgE,QACGC,WAPL,8BAQIjE,MARJ,WAAA;AAUA,MAAMkE,aAAad,cAAc7E,KAAD;AAChC,MAAM4F,gBAAgBJ,WAAW3B,iBAAiB7D,KAAD,IAAU,CAAA;AAC3D,MAAM6F,WAAWpD,YAAWqD,UAAA;IAC1BxE,eAAetB,MAAMsB;IACrBH,kCAAkC;EAFR,GAGvBO,aAHuB,CAAA;AAM5B,SACEU,aAAAA,QAAAA,cAAA,OAAA,OAAA,OAAA;mBACeqD;IACb/C,KAAKmD,SAASnD;IACd6C,OAAKO,UAAA,CAAA,GACAH,YACAT,eACAU,eACAL,KAJA;KAMDG,QAAAA,GAEHjE,KAAKiC,QAXR;AAcH;;ACzCD,IAAMqC,iBAAgC;EACpCZ,UAAU;EACVa,UAAU;EACVC,OAAO;AAH6B;AAMtC,IAAaC,iBAAiB,SAAjBA,gBACXlG,OAD4B;AAG5B,MAAA,gBACE4C,uBAAgC,IAAxB,GADHtB,gBAAP,UAAA,CAAA,GAAsB6E,mBAAtB,UAAA,CAAA;AAEA,MAAMC,mBAAezD,qBAAuB,IAAjB;AAC3Bd,8BAAU,WAAA;AACRsE,qBAAiBC,aAAapD,OAAd;EACjB,GAAE,CAAA,CAFM;AAGT,MAESqD,YAGLrG,MAHFuF,OAFF,gBAKIvF,MAFFsG,QAAAA,SAHF,kBAAA,SAGW,CAAA,IAHX,eAIKC,WAJL,8BAKIvG,OALJ,WAAA;AAOA,WAASwG,eAAT;AACE,QAAIlF,eAAe;AACjB,UAAMmF,kBAAkBH,UAAUA,OAAOI,SAAS;AAClD,UAAID,iBAAiB;AACnB,eAAOH,OAAOK,IAAI,SAAC7C,OAAO8C,GAAR;AAAA,iBAChBxE,aAAAA,QAAAA,cAACkD,qBAAD,OAAA,OAAA,CAAA,GACMxB,OAAAA;YACJxC;YACAzB,KAAG,WAAW+G;YACdnB,QAAM,WAAWmB;YAJnB;QADgB,CAAX;MAQR;IACF;AACD,WAAO;EACR;AAED,WAASC,iBAAT;AACE,QAAIvF,eAAe;AACjB,aAAOc,aAAAA,QAAM0E,SAASH,IAAI3G,MAAM0D,UAAU,SAACqD,OAAD;AACxC,YAAMC,OAAOD;AAIb,aAAIC,QAAI,OAAJ,SAAAA,KAAMC,UAAS3B,qBAAqB;AACtC,cAAM4B,QAAQ9E,aAAAA,QAAM+E,aAAaH,MAAM;YACrC1F;UADqC,CAAzB;AAGd,iBAAO4F;QACR;AACD,eAAOH;MACR,CAZM;IAaR;AACD,WAAO;EACR;AACD,SACE3E,aAAAA,QAAAA,cAAA,OAAA,OAAA,OAAA;IACEM,KAAK0D;IACLb,OAAKO,UAAA,CAAA,GAAOC,gBAAmBM,SAA1B;KACDE,QAAAA,GAGHC,aAAY,GAEZK,eAAc,CARjB;AAWH;AC3EM,IAAMO,mBAAmB,SAAnBA,kBAAoBlE,SAAD;AAE9B,MAAMpB,WAAW,OAAOC,WAAW;AAEnC,MAAI,CAACD,UAAU;AAEb,WAAOG,mBAAmBoF,KAAKnE,OAAxB;EACR;AACD,SAAO;AACR;SCPeoE,iBACdtH,OAAAA;AAEA,MAAM4B,iBAAae,qBAAkC,IAA5B;AAEzB,MAAI,CAACf,WAAWoB,SAAS;AACvBpB,eAAWoB,UAAUoE,iBAAiB;MACpCG,YAAYvH,MAAMuH,cAAcC,WAAWC;MAC3CC,iBAAiB1H,MAAM0H;MACvBzH,UAAUD,MAAM2H;IAHoB,CAAD;EAKtC;AAGD9F,8BAAU,WAAA;AACR,QAAI7B,MAAM0H,mBAAmB9F,WAAWoB,SAAS;AAC/CpB,iBAAWoB,QAAQ4E,sBAAsB5H,MAAM0H,eAA/C;IACD;EACF,GAAE,CAAC1H,MAAM0H,iBAAiB9F,WAAWoB,OAAnC,CAJM;AAOTnB,8BAAU,WAAA;AACR,QAAI7B,MAAM2H,cAAc/F,WAAWoB,SAAS;AAC1CpB,iBAAWoB,QAAQ6E,0BAAnB;IACD;AACD,QAAI,CAAC7H,MAAM2H,cAAc/F,WAAWoB,SAAS;AAC3CpB,iBAAWoB,QAAQ8E,yBAAnB;IACD;EACF,GAAE,CAAC9H,MAAM2H,YAAY/F,WAAWoB,OAA9B,CAPM;AAUTnB,8BAAU,WAAA;AACR,WAAO,WAAA;AACL,OAAAD,cAAU,OAAV,SAAAA,WAAYoB,aAAWpB,cAAvB,OAAA,SAAuBA,WAAYoB,QAAQ+E,QAApB;IACxB;EACF,GAAE,CAAA,CAJM;AAMT,SACE3F,aAAAA,QAAAA,cAACD,gBAAgB6F,UAAjB;IAA0BxD,OAAO5C,WAAWoB;KACzChD,MAAM0D,QADT;AAIH;", "names": ["bezier", "Limits", "properties", "startX", "startY", "endX", "endY", "totalX", "totalY", "startMultiplierX", "endMultiplierX", "startMultiplierY", "endMultiplierY", "ValidCSSEffects", "Units", "RotationUnits", "ScaleUnits", "ScrollAxis", "EasingPreset", "id", "createId", "Rect", "options", "rect", "el", "getBoundingClientRect", "view", "scrollContainer", "scrollRect", "top", "right", "left", "bottom", "height", "offsetHeight", "width", "offsetWidth", "rootMargin", "_setRectWithRootMargin", "totalRootY", "totalRootX", "VALID_UNITS", "px", "deg", "turn", "rad", "parseValueAndUnit", "str", "defaultUnit", "out", "value", "unit", "<PERSON><PERSON><PERSON><PERSON>", "Error", "String", "parseFloat", "match", "isValidUnit", "includes", "easingPresets", "ease", "easeIn", "easeOut", "easeInOut", "easeInQuad", "easeInCubic", "easeInQuart", "easeInQuint", "easeInSine", "easeInExpo", "easeInCirc", "easeOutQuad", "easeOutCubic", "easeOutQuart", "easeOutQuint", "easeOutSine", "easeOutExpo", "easeOutCirc", "easeInOutQuad", "easeInOutCubic", "easeInOutQuart", "easeInOutQuint", "easeInOutSine", "easeInOutExpo", "easeInOutCirc", "easeInBack", "easeOutBack", "easeInOutBack", "createEasingFunction", "easing", "Array", "isArray", "bezier", "params", "PARALLAX_EFFECTS", "Object", "values", "MAP_EFFECT_TO_DEFAULT_UNIT", "speed", "translateX", "translateY", "rotate", "rotateX", "rotateY", "rotateZ", "scale", "scaleX", "scaleY", "scaleZ", "opacity", "parseElementTransitionEffects", "props", "scrollAxis", "parsedEffects", "for<PERSON>ach", "key", "defaultValue", "startSpeed", "endSpeed", "startParsed", "endParsed", "speedConfig", "start", "end", "vertical", "horizontal", "getProgressAmount", "totalDist", "currentScroll", "startAdjustedScroll", "amount", "isElementInView", "scroll", "isInView", "scaleBetween", "newMin", "newMax", "old<PERSON>in", "oldMax", "scaleEffectByProgress", "effect", "progress", "TRANSFORM_EFFECTS", "filter", "v", "setWillChangeStyles", "effects", "keys", "hasOpacity", "<PERSON><PERSON><PERSON><PERSON>", "style", "setElementStyles", "transform", "getTransformStyles", "getOpacityStyles", "scaledOpacity", "styleStr", "reduce", "acc", "scaledEffect", "resetStyles", "element", "createLimitsForRelativeElements", "shouldAlwaysCompleteAnimation", "x", "y", "scrollHeight", "scrollWidth", "limits", "getTranslateScalar", "startTranslatePx", "endTranslatePx", "slow", "totalAbsOff", "Math", "abs", "totalDistTrue", "max", "getStartEndValueInPx", "translate", "elementSize", "startScale", "endScale", "window", "innerWidth", "innerHeight", "DEFAULT_VALUE", "createLimitsWithTranslationsForRelativeElements", "startTranslateXPx", "endTranslateXPx", "startTranslateYPx", "endTranslateYPx", "topBeginsInView", "leftBeginsInView", "bottomEndsInView", "rightEndsInView", "scaleTranslateEffectsForSlowerScroll", "effectsCopy", "getShouldScaleTranslateEffects", "targetElement", "shouldDisableScalingTranslations", "clamp", "num", "min", "Element", "disabledParallaxController", "_setElementEasing", "updateProps", "nextProps", "setCachedAttributes", "shouldScaleTranslateEffects", "startScroll", "endScroll", "_setElementStyles", "scaledEffects", "_updateElementIsInView", "nextIsInView", "isFirstChange", "onEnter", "_setFinalProgress", "onExit", "finalProgress", "round", "_updateElementProgress", "disabled", "nextProgress", "onProgressChange", "onChange", "updateElementOptions", "updatePosition", "isVertical", "total", "s", "View", "config", "has<PERSON><PERSON>ed", "setSize", "<PERSON><PERSON>", "dx", "dy", "setScroll", "testForPassiveScroll", "supportsPassiveOption", "opts", "defineProperty", "get", "addEventListener", "removeEventListener", "e", "ParallaxController", "elements", "_hasScrollContainer", "viewEl", "_getScrollPosition", "undefined", "_ticking", "_supportsPassive", "_bindAllMethods", "_addListeners", "_addResizeObserver", "_setViewSize", "init", "hasW<PERSON>ow", "method", "bind", "_handleScroll", "passive", "_handleUpdateCache", "_removeListeners", "_resizeObserver", "disconnect", "observedEl", "document", "documentElement", "ResizeObserver", "update", "observe", "console", "warn", "nx", "scrollLeft", "pageXOffset", "ny", "scrollTop", "pageYOffset", "length", "requestAnimationFrame", "_updateAllElements", "updateCache", "_updateElementPosition", "_getViewParams", "html", "clientWidth", "clientHeight", "_checkIfViewHasChanged", "getElements", "createElement", "newElement", "removeElementById", "updateElementPropsById", "map", "resetElementStyles", "updateScrollContainer", "disableParallaxController", "enableParallaxController", "disableAllElements", "enableAllElements", "destroy", "removeUndefinedObjectKeys", "obj", "Object", "keys", "for<PERSON>ach", "key", "undefined", "getIsolatedParallaxProps", "props", "disabled", "easing", "endScroll", "onChange", "onEnter", "onExit", "onProgressChange", "opacity", "rootMargin", "rotate", "rotateX", "rotateY", "rotateZ", "scale", "scaleX", "scaleY", "scaleZ", "shouldAlwaysCompleteAnimation", "shouldDisableScalingTranslations", "speed", "startScroll", "targetElement", "translateX", "translateY", "rest", "parallaxProps", "useVerifyController", "controller", "useEffect", "isServer", "window", "isInstance", "ParallaxController", "Error", "ParallaxContext", "React", "createContext", "useParallaxController", "parallaxController", "useContext", "useParallax", "ref", "useRef", "useState", "element", "setElement", "newElement", "current", "HTMLElement", "options", "el", "createElement", "removeElementById", "id", "resetElementStyles", "updateElementPropsById", "Parallax", "children", "FALLBACK_RECT", "height", "getExpandedStyle", "layer", "Array", "isArray", "translateYStart", "parseValueAndUnit", "translateYEnd", "unit", "top", "Math", "abs", "value", "bottom", "clientRect", "getBoundingClientRect", "absSpeed", "getImageStyle", "image", "backgroundImage", "backgroundPosition", "backgroundSize", "absoluteStyle", "position", "left", "right", "ParallaxBanner<PERSON><PERSON>er", "style", "expanded", "testId", "divProps", "imageStyle", "expandedStyle", "parallax", "_extends", "containerStyle", "overflow", "width", "ParallaxBanner", "setTargetElement", "containerRef", "rootStyle", "layers", "rootRest", "renderLayers", "shouldUseLayers", "length", "map", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Children", "child", "item", "type", "clone", "cloneElement", "createController", "init", "ParallaxProvider", "scrollAxis", "ScrollAxis", "vertical", "scrollContainer", "isDisabled", "updateScrollContainer", "disableParallaxController", "enableParallaxController", "destroy", "Provider"]}