export declare enum EasingType {
    easeInBack = "ease-in-back",
    easeInCirc = "ease-in-circ",
    easeInCubic = "ease-in-cubic",
    easeInLinear = "ease-in-linear",
    easeInQuad = "ease-in-quad",
    easeInQuart = "ease-in-quart",
    easeInQuint = "ease-in-quint",
    easeInExpo = "ease-in-expo",
    easeInSine = "ease-in-sine",
    easeOutBack = "ease-out-back",
    easeOutCirc = "ease-out-circ",
    easeOutCubic = "ease-out-cubic",
    easeOutLinear = "ease-out-linear",
    easeOutQuad = "ease-out-quad",
    easeOutQuart = "ease-out-quart",
    easeOutQuint = "ease-out-quint",
    easeOutExpo = "ease-out-expo",
    easeOutSine = "ease-out-sine",
    easeInOutBack = "ease-in-out-back",
    easeInOutCirc = "ease-in-out-circ",
    easeInOutCubic = "ease-in-out-cubic",
    easeInOutLinear = "ease-in-out-linear",
    easeInOutQuad = "ease-in-out-quad",
    easeInOutQuart = "ease-in-out-quart",
    easeInOutQuint = "ease-in-out-quint",
    easeInOutExpo = "ease-in-out-expo",
    easeInOutSine = "ease-in-out-sine"
}
export type EasingTypeAlt = "ease-in-back" | "ease-out-back" | "ease-in-out-back" | "ease-in-circ" | "ease-out-circ" | "ease-in-out-circ" | "ease-in-cubic" | "ease-out-cubic" | "ease-in-out-cubic" | "ease-in-quad" | "ease-out-quad" | "ease-in-out-quad" | "ease-in-quart" | "ease-out-quart" | "ease-in-out-quart" | "ease-in-quint" | "ease-out-quint" | "ease-in-out-quint" | "ease-in-expo" | "ease-out-expo" | "ease-in-out-expo" | "ease-in-sine" | "ease-out-sine" | "ease-in-out-sine";
