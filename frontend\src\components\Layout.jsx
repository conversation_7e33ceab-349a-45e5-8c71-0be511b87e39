import React, { useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { ParallaxProvider } from 'react-scroll-parallax';
import { AnimatePresence } from 'framer-motion';
import Navbar from './Navbar';
import { Footer2 } from './ui/shadcnblocks-com-footer2';
import { initSmoothScroll } from '../utils/animations';

const Layout = () => {
  useEffect(() => {
    // Initialize smooth scrolling for anchor links
    initSmoothScroll();

    // Add a class to the body for global styling
    document.body.classList.add('bg-black');

    return () => {
      document.body.classList.remove('bg-black');
    };
  }, []);

  return (
    <ParallaxProvider>
      <div className="min-h-screen overflow-x-hidden">
        <Navbar />
        <main>
          <AnimatePresence mode="wait">
            <Outlet />
          </AnimatePresence>
        </main>
        <Footer2 />
      </div>
    </ParallaxProvider>
  );
};

export default Layout;
