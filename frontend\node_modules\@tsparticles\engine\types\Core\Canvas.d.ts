import type { Container } from "./Container.js";
import type { Engine } from "./Engine.js";
import type { IContainerPlugin } from "./Interfaces/IContainerPlugin.js";
import type { IDelta } from "./Interfaces/IDelta.js";
import type { IDimension } from "./Interfaces/IDimension.js";
import type { Particle } from "./Particle.js";
export declare class Canvas {
    private readonly container;
    element?: HTMLCanvasElement;
    readonly size: IDimension;
    private _colorPlugins;
    private _context;
    private _coverColorStyle?;
    private _coverImage?;
    private readonly _engine;
    private _generated;
    private _mutationObserver?;
    private _originalStyle?;
    private _postDrawUpdaters;
    private _preDrawUpdaters;
    private _resizePlugins;
    private readonly _standardSize;
    private _trailFill?;
    constructor(container: Container, engine: Engine);
    private get _fullScreen();
    clear(): void;
    destroy(): void;
    draw<T>(cb: (context: CanvasRenderingContext2D) => T): T | undefined;
    drawAsync<T>(cb: (context: CanvasRenderingContext2D) => T): T | undefined;
    drawParticle(particle: Particle, delta: IDelta): void;
    drawParticlePlugin(plugin: IContainerPlugin, particle: Particle, delta: IDelta): void;
    drawPlugin(plugin: IContainerPlugin, delta: IDelta): void;
    init(): Promise<void>;
    initBackground(): void;
    initPlugins(): void;
    initUpdaters(): void;
    loadCanvas(canvas: HTMLCanvasElement): void;
    paint(): void;
    resize(): boolean;
    stop(): void;
    windowResize(): Promise<void>;
    private readonly _applyPostDrawUpdaters;
    private readonly _applyPreDrawUpdaters;
    private readonly _applyResizePlugins;
    private readonly _getPluginParticleColors;
    private readonly _initCover;
    private readonly _initStyle;
    private readonly _initTrail;
    private readonly _paintBase;
    private readonly _paintImage;
    private readonly _repairStyle;
    private readonly _resetOriginalStyle;
    private readonly _safeMutationObserver;
    private readonly _setFullScreenStyle;
}
