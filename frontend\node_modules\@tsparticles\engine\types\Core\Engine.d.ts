import type { EasingType, EasingTypeAlt } from "../Enums/Types/EasingType.js";
import { Container } from "./Container.js";
import type { CustomEventArgs } from "../Types/CustomEventArgs.js";
import type { CustomEventListener } from "../Types/CustomEventListener.js";
import type { EasingFunction } from "../Types/EasingFunction.js";
import type { IColorManager } from "./Interfaces/IColorManager.js";
import type { IContainerPlugin } from "./Interfaces/IContainerPlugin.js";
import type { IEffectDrawer } from "./Interfaces/IEffectDrawer.js";
import type { IInteractor } from "./Interfaces/IInteractor.js";
import type { ILoadParams } from "./Interfaces/ILoadParams.js";
import type { IMovePathGenerator } from "./Interfaces/IMovePathGenerator.js";
import type { IParticleMover } from "./Interfaces/IParticleMover.js";
import type { IParticleUpdater } from "./Interfaces/IParticleUpdater.js";
import type { IParticlesOptions } from "../Options/Interfaces/Particles/IParticlesOptions.js";
import type { IPlugin } from "./Interfaces/IPlugin.js";
import type { IShapeDrawer } from "./Interfaces/IShapeDrawer.js";
import type { ISourceOptions } from "../Types/ISourceOptions.js";
import type { Options } from "../Options/Classes/Options.js";
import type { Particle } from "./Particle.js";
import type { ParticlesOptions } from "../Options/Classes/Particles/ParticlesOptions.js";
import type { RecursivePartial } from "../Types/RecursivePartial.js";
import type { SingleOrMultiple } from "../Types/SingleOrMultiple.js";
declare global {
    interface Window {
        tsParticles: Engine;
    }
}
type GenericInitializer<T> = (container: Container) => Promise<T>;
type InteractorInitializer = GenericInitializer<IInteractor>;
type MoverInitializer = GenericInitializer<IParticleMover>;
type UpdaterInitializer = GenericInitializer<IParticleUpdater>;
export declare class Engine {
    readonly colorManagers: Map<string, IColorManager>;
    readonly easingFunctions: Map<EasingTypeAlt | EasingType, EasingFunction>;
    readonly effectDrawers: Map<string, IEffectDrawer<Particle>>;
    readonly interactors: Map<Container, IInteractor<Particle>[]>;
    readonly movers: Map<Container, IParticleMover[]>;
    readonly pathGenerators: Map<string, IMovePathGenerator>;
    readonly plugins: IPlugin[];
    readonly presets: Map<string, RecursivePartial<import("../export-types.js").IOptions>>;
    readonly shapeDrawers: Map<string, IShapeDrawer<Particle>>;
    readonly updaters: Map<Container, IParticleUpdater[]>;
    private readonly _configs;
    private readonly _domArray;
    private readonly _eventDispatcher;
    private _initialized;
    private readonly _initializers;
    constructor();
    get configs(): Record<string, ISourceOptions>;
    get items(): Container[];
    get version(): string;
    addColorManager(manager: IColorManager, refresh?: boolean): Promise<void>;
    addConfig(config: ISourceOptions): void;
    addEasing(name: EasingType | EasingTypeAlt, easing: EasingFunction, refresh?: boolean): Promise<void>;
    addEffect(effect: SingleOrMultiple<string>, drawer: IEffectDrawer, refresh?: boolean): Promise<void>;
    addEventListener(type: string, listener: CustomEventListener): void;
    addInteractor(name: string, interactorInitializer: InteractorInitializer, refresh?: boolean): Promise<void>;
    addMover(name: string, moverInitializer: MoverInitializer, refresh?: boolean): Promise<void>;
    addParticleUpdater(name: string, updaterInitializer: UpdaterInitializer, refresh?: boolean): Promise<void>;
    addPathGenerator(name: string, generator: IMovePathGenerator, refresh?: boolean): Promise<void>;
    addPlugin(plugin: IPlugin, refresh?: boolean): Promise<void>;
    addPreset(preset: string, options: Readonly<ISourceOptions>, override?: boolean, refresh?: boolean): Promise<void>;
    addShape(drawer: IShapeDrawer, refresh?: boolean): Promise<void>;
    checkVersion(pluginVersion: string): void;
    clearPlugins(container: Container): void;
    dispatchEvent(type: string, args: CustomEventArgs): void;
    dom(): Container[];
    domItem(index: number): Container | undefined;
    getAvailablePlugins(container: Container): Promise<Map<string, IContainerPlugin>>;
    getEasing(name: EasingType | EasingTypeAlt): EasingFunction;
    getEffectDrawer(type: string): IEffectDrawer | undefined;
    getInteractors(container: Container, force?: boolean): Promise<IInteractor[]>;
    getMovers(container: Container, force?: boolean): Promise<IParticleMover[]>;
    getPathGenerator(type: string): IMovePathGenerator | undefined;
    getPlugin(plugin: string): IPlugin | undefined;
    getPreset(preset: string): ISourceOptions | undefined;
    getShapeDrawer(type: string): IShapeDrawer | undefined;
    getSupportedEffects(): IterableIterator<string>;
    getSupportedShapes(): IterableIterator<string>;
    getUpdaters(container: Container, force?: boolean): Promise<IParticleUpdater[]>;
    init(): void;
    item(index: number): Container | undefined;
    load(params: ILoadParams): Promise<Container | undefined>;
    loadOptions(options: Options, sourceOptions: ISourceOptions): void;
    loadParticlesOptions(container: Container, options: ParticlesOptions, ...sourceOptions: (RecursivePartial<IParticlesOptions> | undefined)[]): void;
    refresh(refresh?: boolean): Promise<void>;
    removeEventListener(type: string, listener: CustomEventListener): void;
    setOnClickHandler(callback: (e: Event, particles?: Particle[]) => void): void;
}
export {};
