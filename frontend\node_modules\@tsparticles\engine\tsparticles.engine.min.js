/*! For license information please see tsparticles.engine.min.js.LICENSE.txt */
!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var i=e();for(var s in i)("object"==typeof exports?exports:t)[s]=i[s]}}(this,(()=>(()=>{var t={d:(e,i)=>{for(var s in i)t.o(i,s)&&!t.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:i[s]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{AlterType:()=>di,AnimatableColor:()=>ks,AnimationMode:()=>Oe,AnimationOptions:()=>zs,AnimationStatus:()=>Pe,AnimationValueWithRandom:()=>Ts,Background:()=>rs,BackgroundMask:()=>hs,BackgroundMaskCover:()=>cs,BaseRange:()=>pn,Circle:()=>fn,ClickEvent:()=>ds,CollisionMode:()=>gi,Collisions:()=>Ls,CollisionsAbsorb:()=>Ss,CollisionsOverlap:()=>Cs,ColorAnimation:()=>Os,DestroyType:()=>ke,DivEvent:()=>us,DivType:()=>fi,EasingType:()=>Rn,EventType:()=>pi,Events:()=>vs,ExternalInteractorBase:()=>Pn,FullScreen:()=>ls,GradientType:()=>Cn,HoverEvent:()=>fs,HslAnimation:()=>Ps,Interactivity:()=>ys,InteractivityDetect:()=>ui,InteractorType:()=>wi,LimitMode:()=>_i,ManualParticle:()=>_s,Modes:()=>gs,Move:()=>Ns,MoveAngle:()=>As,MoveAttract:()=>Bs,MoveCenter:()=>Vs,MoveDirection:()=>Ut,MoveGravity:()=>Hs,MovePath:()=>qs,MoveTrail:()=>Us,Opacity:()=>Qs,OpacityAnimation:()=>js,Options:()=>cn,OptionsColor:()=>as,OutMode:()=>yi,OutModeDirection:()=>Se,OutModes:()=>$s,Parallax:()=>ps,ParticleOutType:()=>bi,ParticlesBounce:()=>Es,ParticlesBounceFactor:()=>Is,ParticlesDensity:()=>Xs,ParticlesInteractorBase:()=>kn,ParticlesNumber:()=>Zs,ParticlesNumberLimit:()=>Ys,ParticlesOptions:()=>on,PixelMode:()=>Ce,Point:()=>un,RangedAnimationOptions:()=>Ms,RangedAnimationValueWithRandom:()=>Ds,Rectangle:()=>mn,ResizeEvent:()=>ms,Responsive:()=>ws,ResponsiveMode:()=>mi,RotateDirection:()=>Sn,Shadow:()=>Js,Shape:()=>Ks,Size:()=>en,SizeAnimation:()=>tn,Spin:()=>Gs,StartValueType:()=>Re,Stroke:()=>sn,Theme:()=>xs,ThemeDefault:()=>bs,ThemeMode:()=>vi,ValueWithRandom:()=>Rs,Vector:()=>Jt,Vector3d:()=>Zt,ZIndex:()=>nn,alterHsl:()=>ts,animate:()=>ne,areBoundsInside:()=>Ge,arrayRandomIndex:()=>We,calcExactPositionOrRandomFromSize:()=>xe,calcExactPositionOrRandomFromSizeRanged:()=>ze,calcPositionFromSize:()=>_e,calcPositionOrRandomFromSize:()=>we,calcPositionOrRandomFromSizeRanged:()=>be,calculateBounds:()=>Ne,cancelAnimation:()=>oe,canvasFirstIndex:()=>at,canvasTag:()=>S,circleBounce:()=>Ke,circleBounceDataFromParticle:()=>Je,clamp:()=>ae,clear:()=>ji,clickRadius:()=>K,cloneStyle:()=>hi,collisionVelocity:()=>ye,colorMix:()=>Fi,colorToHsl:()=>Pi,colorToRgb:()=>Oi,countOffset:()=>wt,decayOffset:()=>dt,deepExtend:()=>je,defaultAlpha:()=>O,defaultAngle:()=>Ct,defaultDensityFactor:()=>Ot,defaultFps:()=>M,defaultFpsLimit:()=>nt,defaultLoops:()=>qt,defaultOpacity:()=>J,defaultRadius:()=>vt,defaultRatio:()=>I,defaultReduceFactor:()=>E,defaultRemoveQuantity:()=>D,defaultRetryCount:()=>C,defaultRgbMin:()=>Vt,defaultTime:()=>Wt,defaultTransform:()=>_,defaultTransformValue:()=>Q,defaultVelocity:()=>Ht,degToRad:()=>me,deleteCount:()=>Pt,divMode:()=>Ze,divModeExecute:()=>Xe,double:()=>x,doublePI:()=>z,drawEffect:()=>Xi,drawLine:()=>$i,drawParticle:()=>Qi,drawParticlePlugin:()=>Ki,drawPlugin:()=>Ji,drawShape:()=>Yi,drawShapeAfterDraw:()=>Zi,empty:()=>$,errorPrefix:()=>f,executeOnSingleOrMultiple:()=>ei,findItemFromSingleOrMultiple:()=>si,generatedAttribute:()=>i,generatedFalse:()=>k,generatedTrue:()=>P,getDistance:()=>fe,getDistances:()=>pe,getFullScreenStyle:()=>li,getHslAnimationFromHsl:()=>Hi,getHslFromAnimation:()=>Vi,getLinkColor:()=>Ai,getLinkRandomColor:()=>Bi,getLogger:()=>Ie,getParticleBaseVelocity:()=>ge,getParticleDirectionAngle:()=>ve,getPosition:()=>ai,getRandom:()=>ie,getRandomRgbColor:()=>Ii,getRangeMax:()=>de,getRangeMin:()=>le,getRangeValue:()=>he,getSize:()=>ri,getStyleFromHsl:()=>Li,getStyleFromRgb:()=>Ei,hMax:()=>B,hMin:()=>q,hPhase:()=>U,half:()=>v,hasMatchMedia:()=>Fe,hslToRgb:()=>Ti,hslaToRgba:()=>Di,identity:()=>Rt,initParticleNumericAnimationValue:()=>ni,inverseFactorNumerator:()=>F,isArray:()=>Xt,isBoolean:()=>$t,isDivModeEnabled:()=>Qe,isFunction:()=>jt,isInArray:()=>He,isNull:()=>Yt,isNumber:()=>Nt,isObject:()=>Qt,isPointInside:()=>$e,isSsr:()=>Le,isString:()=>Gt,itemFromArray:()=>Ue,itemFromSingleOrMultiple:()=>ii,lFactor:()=>Dt,lMax:()=>H,lMin:()=>It,lengthOffset:()=>Mt,loadFont:()=>qe,loadMinIndex:()=>ct,loadOptions:()=>an,loadParticlesOptions:()=>rn,loadRandomFactor:()=>rt,manualCount:()=>zt,manualDefaultPosition:()=>St,midColorValue:()=>b,millisecondsToSeconds:()=>g,minCoordinate:()=>et,minCount:()=>bt,minFpsLimit:()=>ot,minIndex:()=>xt,minLimit:()=>_t,minRetries:()=>pt,minStrokeWidth:()=>Tt,minVelocity:()=>j,minZ:()=>mt,minimumLength:()=>Y,minimumSize:()=>X,mix:()=>re,mouseDownEvent:()=>s,mouseLeaveEvent:()=>o,mouseMoveEvent:()=>r,mouseOutEvent:()=>a,mouseUpEvent:()=>n,none:()=>lt,one:()=>ht,originPoint:()=>y,paintBase:()=>Gi,paintImage:()=>Ni,parseAlpha:()=>Me,percentDenominator:()=>m,phaseNumerator:()=>Bt,posOffset:()=>gt,qTreeCapacity:()=>T,quarter:()=>G,randomColorValue:()=>w,randomInRange:()=>ce,rangeColorToHsl:()=>ki,rangeColorToRgb:()=>Mi,rectBounce:()=>ti,removeDeleteCount:()=>it,removeMinIndex:()=>st,resizeEvent:()=>u,rgbFactor:()=>Et,rgbMax:()=>A,rgbToHsl:()=>Si,rollFactor:()=>ft,sMax:()=>V,sMin:()=>W,sNormalizedOffset:()=>At,safeIntersectionObserver:()=>Be,safeMatchMedia:()=>Ae,safeMutationObserver:()=>Ve,setAnimationFunctions:()=>se,setLogger:()=>De,setRandom:()=>ee,setRangeValue:()=>ue,sextuple:()=>Ft,singleDivModeExecute:()=>Ye,sizeFactor:()=>yt,squareExp:()=>R,stringToAlpha:()=>Ci,stringToRgb:()=>Ri,subdivideCount:()=>L,threeQuarter:()=>N,touchCancelEvent:()=>d,touchDelay:()=>kt,touchEndEvent:()=>h,touchEndLengthOffset:()=>tt,touchMoveEvent:()=>l,touchStartEvent:()=>c,triple:()=>Lt,tryCountIncrement:()=>ut,tsParticles:()=>Tn,updateAnimation:()=>ci,updateColor:()=>Ui,updateColorValue:()=>Wi,visibilityChangeEvent:()=>p,zIndexFactorOffset:()=>Z});const i="generated",s="pointerdown",n="pointerup",o="pointerleave",a="pointerout",r="pointermove",c="touchstart",h="touchend",l="touchmove",d="touchcancel",u="resize",p="visibilitychange",f="tsParticles - Error",m=100,v=.5,g=1e3,y={x:0,y:0,z:0},_={a:1,b:0,c:0,d:1},w="random",b="mid",x=2,z=Math.PI*x,M=60,O=1,P="true",k="false",S="canvas",C=0,R=2,T=4,D=1,I=1,E=1,L=4,F=1,A=255,B=360,V=100,H=100,q=0,W=0,U=60,$=0,G=.25,N=v+G,j=0,Q=1,X=0,Y=0,Z=1,J=1,K=1,tt=1,et=0,it=1,st=0,nt=120,ot=0,at=0,rt=1e4,ct=0,ht=1,lt=0,dt=1,ut=1,pt=0,ft=1,mt=0,vt=0,gt=-G,yt=1.5,_t=0,wt=1,bt=0,xt=0,zt=0,Mt=1,Ot=1,Pt=1,kt=500,St=50,Ct=0,Rt=1,Tt=0,Dt=1,It=0,Et=255,Lt=3,Ft=6,At=1,Bt=1,Vt=0,Ht=0,qt=0,Wt=0;var Ut;function $t(t){return"boolean"==typeof t}function Gt(t){return"string"==typeof t}function Nt(t){return"number"==typeof t}function jt(t){return"function"==typeof t}function Qt(t){return"object"==typeof t&&null!==t}function Xt(t){return Array.isArray(t)}function Yt(t){return null==t}!function(t){t.bottom="bottom",t.bottomLeft="bottom-left",t.bottomRight="bottom-right",t.left="left",t.none="none",t.right="right",t.top="top",t.topLeft="top-left",t.topRight="top-right",t.outside="outside",t.inside="inside"}(Ut||(Ut={}));class Zt{constructor(t,e,i){if(this._updateFromAngle=(t,e)=>{this.x=Math.cos(t)*e,this.y=Math.sin(t)*e},!Nt(t)&&t){this.x=t.x,this.y=t.y;const e=t;this.z=e.z?e.z:y.z}else{if(void 0===t||void 0===e)throw new Error(`${f} Vector3d not initialized correctly`);this.x=t,this.y=e,this.z=i??y.z}}static get origin(){return Zt.create(y.x,y.y,y.z)}get angle(){return Math.atan2(this.y,this.x)}set angle(t){this._updateFromAngle(t,this.length)}get length(){return Math.sqrt(this.getLengthSq())}set length(t){this._updateFromAngle(this.angle,t)}static clone(t){return Zt.create(t.x,t.y,t.z)}static create(t,e,i){return new Zt(t,e,i)}add(t){return Zt.create(this.x+t.x,this.y+t.y,this.z+t.z)}addTo(t){this.x+=t.x,this.y+=t.y,this.z+=t.z}copy(){return Zt.clone(this)}distanceTo(t){return this.sub(t).length}distanceToSq(t){return this.sub(t).getLengthSq()}div(t){return Zt.create(this.x/t,this.y/t,this.z/t)}divTo(t){this.x/=t,this.y/=t,this.z/=t}getLengthSq(){return this.x**R+this.y**R}mult(t){return Zt.create(this.x*t,this.y*t,this.z*t)}multTo(t){this.x*=t,this.y*=t,this.z*=t}normalize(){const t=this.length;t!=lt&&this.multTo(F/t)}rotate(t){return Zt.create(this.x*Math.cos(t)-this.y*Math.sin(t),this.x*Math.sin(t)+this.y*Math.cos(t),y.z)}setTo(t){this.x=t.x,this.y=t.y;const e=t;this.z=e.z?e.z:y.z}sub(t){return Zt.create(this.x-t.x,this.y-t.y,this.z-t.z)}subFrom(t){this.x-=t.x,this.y-=t.y,this.z-=t.z}}class Jt extends Zt{constructor(t,e){super(t,e,y.z)}static get origin(){return Jt.create(y.x,y.y)}static clone(t){return Jt.create(t.x,t.y)}static create(t,e){return new Jt(t,e)}}let Kt=Math.random;const te={nextFrame:t=>requestAnimationFrame(t),cancel:t=>cancelAnimationFrame(t)};function ee(t=Math.random){Kt=t}function ie(){return ae(Kt(),0,1-Number.EPSILON)}function se(t,e){te.nextFrame=e=>t(e),te.cancel=t=>e(t)}function ne(t){return te.nextFrame(t)}function oe(t){te.cancel(t)}function ae(t,e,i){return Math.min(Math.max(t,e),i)}function re(t,e,i,s){return Math.floor((t*i+e*s)/(i+s))}function ce(t){const e=de(t);let i=le(t);return e===i&&(i=0),ie()*(e-i)+i}function he(t){return Nt(t)?t:ce(t)}function le(t){return Nt(t)?t:t.min}function de(t){return Nt(t)?t:t.max}function ue(t,e){if(t===e||void 0===e&&Nt(t))return t;const i=le(t),s=de(t);return void 0!==e?{min:Math.min(i,e),max:Math.max(s,e)}:ue(i,s)}function pe(t,e){const i=t.x-e.x,s=t.y-e.y;return{dx:i,dy:s,distance:Math.sqrt(i**2+s**2)}}function fe(t,e){return pe(t,e).distance}function me(t){return t*Math.PI/180}function ve(t,e,i){if(Nt(t))return me(t);switch(t){case Ut.top:return-Math.PI*v;case Ut.topRight:return-Math.PI*G;case Ut.right:return $;case Ut.bottomRight:return Math.PI*G;case Ut.bottom:return Math.PI*v;case Ut.bottomLeft:return Math.PI*N;case Ut.left:return Math.PI;case Ut.topLeft:return-Math.PI*N;case Ut.inside:return Math.atan2(i.y-e.y,i.x-e.x);case Ut.outside:return Math.atan2(e.y-i.y,e.x-i.x);default:return ie()*z}}function ge(t){const e=Jt.origin;return e.length=1,e.angle=t,e}function ye(t,e,i,s){return Jt.create(t.x*(i-s)/(i+s)+e.x*x*s/(i+s),t.y)}function _e(t){return void 0!==t.position?.x&&void 0!==t.position.y?{x:t.position.x*t.size.width/m,y:t.position.y*t.size.height/m}:void 0}function we(t){return{x:(t.position?.x??ie()*m)*t.size.width/m,y:(t.position?.y??ie()*m)*t.size.height/m}}function be(t){const e={x:void 0!==t.position?.x?he(t.position.x):void 0,y:void 0!==t.position?.y?he(t.position.y):void 0};return we({size:t.size,position:e})}function xe(t){return{x:t.position?.x??ie()*t.size.width,y:t.position?.y??ie()*t.size.height}}function ze(t){const e={x:void 0!==t.position?.x?he(t.position.x):void 0,y:void 0!==t.position?.y?he(t.position.y):void 0};return xe({size:t.size,position:e})}function Me(t){return t?t.endsWith("%")?parseFloat(t)/m:parseFloat(t):1}var Oe,Pe,ke,Se,Ce,Re;!function(t){t.auto="auto",t.increase="increase",t.decrease="decrease",t.random="random"}(Oe||(Oe={})),function(t){t.increasing="increasing",t.decreasing="decreasing"}(Pe||(Pe={})),function(t){t.none="none",t.max="max",t.min="min"}(ke||(ke={})),function(t){t.bottom="bottom",t.left="left",t.right="right",t.top="top"}(Se||(Se={})),function(t){t.precise="precise",t.percent="percent"}(Ce||(Ce={})),function(t){t.max="max",t.min="min",t.random="random"}(Re||(Re={}));const Te={debug:console.debug,error:console.error,info:console.info,log:console.log,verbose:console.log,warning:console.warn};function De(t){Te.debug=t.debug||Te.debug,Te.error=t.error||Te.error,Te.info=t.info||Te.info,Te.log=t.log||Te.log,Te.verbose=t.verbose||Te.verbose,Te.warning=t.warning||Te.warning}function Ie(){return Te}function Ee(t){const e={bounced:!1},{pSide:i,pOtherSide:s,rectSide:n,rectOtherSide:o,velocity:a,factor:r}=t;return s.min<o.min||s.min>o.max||s.max<o.min||s.max>o.max||(i.max>=n.min&&i.max<=(n.max+n.min)*v&&a>j||i.min<=n.max&&i.min>(n.max+n.min)*v&&a<j)&&(e.velocity=a*-r,e.bounced=!0),e}function Le(){return"undefined"==typeof window||!window||void 0===window.document||!window.document}function Fe(){return!Le()&&"undefined"!=typeof matchMedia}function Ae(t){if(Fe())return matchMedia(t)}function Be(t){if(!Le()&&"undefined"!=typeof IntersectionObserver)return new IntersectionObserver(t)}function Ve(t){if(!Le()&&"undefined"!=typeof MutationObserver)return new MutationObserver(t)}function He(t,e){return t===e||Xt(e)&&e.indexOf(t)>-1}async function qe(t,e){try{await document.fonts.load(`${e??"400"} 36px '${t??"Verdana"}'`)}catch{}}function We(t){return Math.floor(ie()*t.length)}function Ue(t,e,i=!0){return t[void 0!==e&&i?e%t.length:We(t)]}function $e(t,e,i,s,n){return Ge(Ne(t,s??0),e,i,n)}function Ge(t,e,i,s){let n=!0;return s&&s!==Se.bottom||(n=t.top<e.height+i.x),!n||s&&s!==Se.left||(n=t.right>i.x),!n||s&&s!==Se.right||(n=t.left<e.width+i.y),!n||s&&s!==Se.top||(n=t.bottom>i.y),n}function Ne(t,e){return{bottom:t.y+e,left:t.x-e,right:t.x+e,top:t.y-e}}function je(t,...e){for(const i of e){if(null==i)continue;if(!Qt(i)){t=i;continue}const e=Array.isArray(i);!e||!Qt(t)&&t&&Array.isArray(t)?e||!Qt(t)&&t&&!Array.isArray(t)||(t={}):t=[];for(const e in i){if("__proto__"===e)continue;const s=i[e],n=t;n[e]=Qt(s)&&Array.isArray(s)?s.map((t=>je(n[e],t))):je(n[e],s)}}return t}function Qe(t,e){return!!si(e,(e=>e.enable&&He(t,e.mode)))}function Xe(t,e,i){ei(e,(e=>{const s=e.mode;e.enable&&He(t,s)&&Ye(e,i)}))}function Ye(t,e){ei(t.selectors,(i=>{e(i,t)}))}function Ze(t,e){if(e&&t)return si(t,(t=>function(t,e){const i=ei(e,(e=>t.matches(e)));return Xt(i)?i.some((t=>t)):i}(e,t.selectors)))}function Je(t){return{position:t.getPosition(),radius:t.getRadius(),mass:t.getMass(),velocity:t.velocity,factor:Jt.create(he(t.options.bounce.horizontal.value),he(t.options.bounce.vertical.value))}}function Ke(t,e){const{x:i,y:s}=t.velocity.sub(e.velocity),[n,o]=[t.position,e.position],{dx:a,dy:r}=pe(o,n);if(i*a+s*r<0)return;const c=-Math.atan2(r,a),h=t.mass,l=e.mass,d=t.velocity.rotate(c),u=e.velocity.rotate(c),p=ye(d,u,h,l),f=ye(u,d,h,l),m=p.rotate(-c),v=f.rotate(-c);t.velocity.x=m.x*t.factor.x,t.velocity.y=m.y*t.factor.y,e.velocity.x=v.x*e.factor.x,e.velocity.y=v.y*e.factor.y}function ti(t,e){const i=Ne(t.getPosition(),t.getRadius()),s=t.options.bounce,n=Ee({pSide:{min:i.left,max:i.right},pOtherSide:{min:i.top,max:i.bottom},rectSide:{min:e.left,max:e.right},rectOtherSide:{min:e.top,max:e.bottom},velocity:t.velocity.x,factor:he(s.horizontal.value)});n.bounced&&(void 0!==n.velocity&&(t.velocity.x=n.velocity),void 0!==n.position&&(t.position.x=n.position));const o=Ee({pSide:{min:i.top,max:i.bottom},pOtherSide:{min:i.left,max:i.right},rectSide:{min:e.top,max:e.bottom},rectOtherSide:{min:e.left,max:e.right},velocity:t.velocity.y,factor:he(s.vertical.value)});o.bounced&&(void 0!==o.velocity&&(t.velocity.y=o.velocity),void 0!==o.position&&(t.position.y=o.position))}function ei(t,e){return Xt(t)?t.map(((t,i)=>e(t,i))):e(t,0)}function ii(t,e,i){return Xt(t)?Ue(t,e,i):t}function si(t,e){if(Xt(t))return t.find(((t,i)=>e(t,i)));return e(t,0)?t:void 0}function ni(t,e){const i=t.value,s=t.animation,n={delayTime:he(s.delay)*g,enable:s.enable,value:he(t.value)*e,max:de(i)*e,min:le(i)*e,loops:0,maxLoops:he(s.count),time:0};if(s.enable){switch(n.decay=1-he(s.decay),s.mode){case Oe.increase:n.status=Pe.increasing;break;case Oe.decrease:n.status=Pe.decreasing;break;case Oe.random:n.status=ie()>=v?Pe.increasing:Pe.decreasing}const t=s.mode===Oe.auto;switch(s.startValue){case Re.min:n.value=n.min,t&&(n.status=Pe.increasing);break;case Re.max:n.value=n.max,t&&(n.status=Pe.decreasing);break;case Re.random:default:n.value=ce(n),t&&(n.status=ie()>=v?Pe.increasing:Pe.decreasing)}}return n.initialValue=n.value,n}function oi(t,e){if(!(t.mode===Ce.percent)){const{mode:e,...i}=t;return i}return"x"in t?{x:t.x/m*e.width,y:t.y/m*e.height}:{width:t.width/m*e.width,height:t.height/m*e.height}}function ai(t,e){return oi(t,e)}function ri(t,e){return oi(t,e)}function ci(t,e,i,s,n){if(t.destroyed||!e||!e.enable||(e.maxLoops??0)>0&&(e.loops??0)>(e.maxLoops??0))return;const o=(e.velocity??0)*n.factor,a=e.min,r=e.max,c=e.decay??1;if(e.time||(e.time=0),(e.delayTime??0)>0&&e.time<(e.delayTime??0)&&(e.time+=n.value),!((e.delayTime??0)>0&&e.time<(e.delayTime??0))){switch(e.status){case Pe.increasing:e.value>=r?(i?e.status=Pe.decreasing:e.value-=r,e.loops||(e.loops=0),e.loops++):e.value+=o;break;case Pe.decreasing:e.value<=a?(i?e.status=Pe.increasing:e.value+=r,e.loops||(e.loops=0),e.loops++):e.value-=o}e.velocity&&1!==c&&(e.velocity*=c),function(t,e,i,s,n){switch(e){case ke.max:i>=n&&t.destroy();break;case ke.min:i<=s&&t.destroy()}}(t,s,e.value,a,r),t.destroyed||(e.value=ae(e.value,a,r))}}function hi(t){const e=document.createElement("div").style;if(!t)return e;for(const i in t){const s=t[i];if(!Object.prototype.hasOwnProperty.call(t,i)||Yt(s))continue;const n=t.getPropertyValue?.(s);if(!n)continue;const o=t.getPropertyPriority?.(s);o?e.setProperty?.(s,n,o):e.setProperty?.(s,n)}return e}const li=function(t){const e=new Map;return(...i)=>{const s=JSON.stringify(i);if(e.has(s))return e.get(s);const n=t(...i);return e.set(s,n),n}}((function(t){const e=document.createElement("div").style,i={width:"100%",height:"100%",margin:"0",padding:"0",borderWidth:"0",position:"fixed",zIndex:t.toString(10),"z-index":t.toString(10),top:"0",left:"0"};for(const t in i){const s=i[t];e.setProperty(t,s)}return e}));var di,ui,pi,fi,mi,vi,gi,yi,_i,wi,bi,xi;function zi(t,e){if(e)for(const i of t.colorManagers.values())if(e.startsWith(i.stringPrefix))return i.parseString(e)}function Mi(t,e,i,s=!0){if(!e)return;const n=Gt(e)?{value:e}:e;if(Gt(n.value))return Oi(t,n.value,i,s);if(Xt(n.value))return Mi(t,{value:Ue(n.value,i,s)});for(const e of t.colorManagers.values()){const t=e.handleRangeColor(n);if(t)return t}}function Oi(t,e,i,s=!0){if(!e)return;const n=Gt(e)?{value:e}:e;if(Gt(n.value))return n.value===w?Ii():Ri(t,n.value);if(Xt(n.value))return Oi(t,{value:Ue(n.value,i,s)});for(const e of t.colorManagers.values()){const t=e.handleColor(n);if(t)return t}}function Pi(t,e,i,s=!0){const n=Oi(t,e,i,s);return n?Si(n):void 0}function ki(t,e,i,s=!0){const n=Mi(t,e,i,s);return n?Si(n):void 0}function Si(t){const e=t.r/A,i=t.g/A,s=t.b/A,n=Math.max(e,i,s),o=Math.min(e,i,s),a={h:q,l:(n+o)*v,s:W};return n!==o&&(a.s=a.l<v?(n-o)/(n+o):(n-o)/(x-n-o),a.h=e===n?(i-s)/(n-o):a.h=i===n?x+(s-e)/(n-o):x*x+(e-i)/(n-o)),a.l*=H,a.s*=V,a.h*=U,a.h<q&&(a.h+=B),a.h>=B&&(a.h-=B),a}function Ci(t,e){return zi(t,e)?.a}function Ri(t,e){return zi(t,e)}function Ti(t){const e=(t.h%B+B)%B,i=Math.max(W,Math.min(V,t.s)),s=Math.max(It,Math.min(H,t.l)),n=e/B,o=i/V,a=s/H;if(i===W){const t=Math.round(a*Et);return{r:t,g:t,b:t}}const r=(t,e,i)=>{if(i<0&&i++,i>1&&i--,i*Ft<1)return t+(e-t)*Ft*i;if(i*x<1)return e;if(i*Lt<1*x){return t+(e-t)*(x/Lt-i)*Ft}return t},c=a<v?a*(At+o):a+o-a*o,h=x*a-c,l=Bt/Lt,d=Math.min(Et,Et*r(h,c,n+l)),u=Math.min(Et,Et*r(h,c,n)),p=Math.min(Et,Et*r(h,c,n-l));return{r:Math.round(d),g:Math.round(u),b:Math.round(p)}}function Di(t){const e=Ti(t);return{a:t.a,b:e.b,g:e.g,r:e.r}}function Ii(t){const e=t??Vt,i=A+Rt;return{b:Math.floor(ce(ue(e,i))),g:Math.floor(ce(ue(e,i))),r:Math.floor(ce(ue(e,i)))}}function Ei(t,e){return`rgba(${t.r}, ${t.g}, ${t.b}, ${e??J})`}function Li(t,e){return`hsla(${t.h}, ${t.s}%, ${t.l}%, ${e??J})`}function Fi(t,e,i,s){let n=t,o=e;return void 0===n.r&&(n=Ti(t)),void 0===o.r&&(o=Ti(e)),{b:re(n.b,o.b,i,s),g:re(n.g,o.g,i,s),r:re(n.r,o.r,i,s)}}function Ai(t,e,i){if(i===w)return Ii();if(i!==b)return i;{const i=t.getFillColor()??t.getStrokeColor(),s=e?.getFillColor()??e?.getStrokeColor();if(i&&s&&e)return Fi(i,s,t.getRadius(),e.getRadius());{const t=i??s;if(t)return Ti(t)}}}function Bi(t,e,i,s){const n=Gt(e)?e:e.value;return n===w?s?Mi(t,{value:n}):i?w:b:n===b?b:Mi(t,{value:n})}function Vi(t){return void 0!==t?{h:t.h.value,s:t.s.value,l:t.l.value}:void 0}function Hi(t,e,i){const s={h:{enable:!1,value:t.h},s:{enable:!1,value:t.s},l:{enable:!1,value:t.l}};return e&&(qi(s.h,e.h,i),qi(s.s,e.s,i),qi(s.l,e.l,i)),s}function qi(t,e,i){t.enable=e.enable,t.enable?(t.velocity=he(e.speed)/m*i,t.decay=dt-he(e.decay),t.status=Pe.increasing,t.loops=qt,t.maxLoops=he(e.count),t.time=Wt,t.delayTime=he(e.delay)*g,e.sync||(t.velocity*=ie(),t.value*=ie()),t.initialValue=t.value,t.offset=ue(e.offset)):t.velocity=Ht}function Wi(t,e,i,s){if(!t||!t.enable||(t.maxLoops??0)>0&&(t.loops??0)>(t.maxLoops??0))return;if(t.time||(t.time=0),(t.delayTime??0)>0&&t.time<(t.delayTime??0)&&(t.time+=s.value),(t.delayTime??0)>0&&t.time<(t.delayTime??0))return;const n=t.offset?ce(t.offset):0,o=(t.velocity??0)*s.factor+3.6*n,a=t.decay??1,r=de(e),c=le(e);if(i&&t.status!==Pe.increasing){t.value-=o;const e=0;t.value<e&&(t.loops||(t.loops=0),t.loops++,t.status=Pe.increasing)}else t.value+=o,t.value>r&&(t.loops||(t.loops=0),t.loops++,i?t.status=Pe.decreasing:t.value-=r);t.velocity&&1!==a&&(t.velocity*=a),t.value=ae(t.value,c,r)}function Ui(t,e){if(!t)return;const{h:i,s,l:n}=t,o={min:0,max:100},a={min:0,max:100};i&&Wi(i,{min:0,max:360},!1,e),s&&Wi(s,o,!0,e),n&&Wi(n,a,!0,e)}function $i(t,e,i){t.beginPath(),t.moveTo(e.x,e.y),t.lineTo(i.x,i.y),t.closePath()}function Gi(t,e,i){t.fillStyle=i??"rgba(0,0,0,0)",t.fillRect(y.x,y.y,e.width,e.height)}function Ni(t,e,i,s){i&&(t.globalAlpha=s,t.drawImage(i,y.x,y.y,e.width,e.height),t.globalAlpha=1)}function ji(t,e){t.clearRect(y.x,y.y,e.width,e.height)}function Qi(t){const{container:e,context:i,particle:s,delta:n,colorStyles:o,backgroundMask:a,composite:r,radius:c,opacity:h,shadow:l,transform:d}=t,u=s.getPosition(),p=s.rotation+(s.pathRotation?s.velocity.angle:Ct),f=Math.sin(p),m=Math.cos(p),v=!!p,g={a:m*(d.a??_.a),b:v?f*(d.b??Rt):d.b??_.b,c:v?-f*(d.c??Rt):d.c??_.c,d:m*(d.d??_.d)};i.setTransform(g.a,g.b,g.c,g.d,u.x,u.y),a&&(i.globalCompositeOperation=r);const y=s.shadowColor;l.enable&&y&&(i.shadowBlur=l.blur,i.shadowColor=Ei(y),i.shadowOffsetX=l.offset.x,i.shadowOffsetY=l.offset.y),o.fill&&(i.fillStyle=o.fill);const w=s.strokeWidth??Tt;i.lineWidth=w,o.stroke&&(i.strokeStyle=o.stroke);const b={container:e,context:i,particle:s,radius:c,opacity:h,delta:n,transformData:g,strokeWidth:w};Yi(b),Zi(b),Xi(b),i.globalCompositeOperation="source-over",i.resetTransform()}function Xi(t){const{container:e,context:i,particle:s,radius:n,opacity:o,delta:a,transformData:r}=t;if(!s.effect)return;const c=e.effectDrawers.get(s.effect);c&&c.draw({context:i,particle:s,radius:n,opacity:o,delta:a,pixelRatio:e.retina.pixelRatio,transformData:{...r}})}function Yi(t){const{container:e,context:i,particle:s,radius:n,opacity:o,delta:a,strokeWidth:r,transformData:c}=t;if(!s.shape)return;const h=e.shapeDrawers.get(s.shape);h&&(i.beginPath(),h.draw({context:i,particle:s,radius:n,opacity:o,delta:a,pixelRatio:e.retina.pixelRatio,transformData:{...c}}),s.shapeClose&&i.closePath(),r>Tt&&i.stroke(),s.shapeFill&&i.fill())}function Zi(t){const{container:e,context:i,particle:s,radius:n,opacity:o,delta:a,transformData:r}=t;if(!s.shape)return;const c=e.shapeDrawers.get(s.shape);c?.afterDraw&&c.afterDraw({context:i,particle:s,radius:n,opacity:o,delta:a,pixelRatio:e.retina.pixelRatio,transformData:{...r}})}function Ji(t,e,i){e.draw&&e.draw(t,i)}function Ki(t,e,i,s){e.drawParticle&&e.drawParticle(t,i,s)}function ts(t,e,i){return{h:t.h,s:t.s,l:t.l+(e===di.darken?-Dt:Dt)*i}}function es(t,e,i){const s=e[i];void 0!==s&&(t[i]=(t[i]??Q)*s)}function is(t,e,i=!1){if(!e)return;if(!t)return;const s=t.style;if(!s)return;const n=new Set;for(const t in s)Object.prototype.hasOwnProperty.call(s,t)&&n.add(s[t]);for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.add(e[t]);for(const t of n){const n=e.getPropertyValue(t);n?s.setProperty(t,n,i?"important":""):s.removeProperty(t)}}!function(t){t.darken="darken",t.enlighten="enlighten"}(di||(di={}));class ss{constructor(t,e){this.container=t,this._applyPostDrawUpdaters=t=>{for(const e of this._postDrawUpdaters)e.afterDraw?.(t)},this._applyPreDrawUpdaters=(t,e,i,s,n,o)=>{for(const a of this._preDrawUpdaters){if(a.getColorStyles){const{fill:o,stroke:r}=a.getColorStyles(e,t,i,s);o&&(n.fill=o),r&&(n.stroke=r)}if(a.getTransformValues){const t=a.getTransformValues(e);for(const e in t)es(o,t,e)}a.beforeDraw?.(e)}},this._applyResizePlugins=()=>{for(const t of this._resizePlugins)t.resize?.()},this._getPluginParticleColors=t=>{let e,i;for(const s of this._colorPlugins)if(!e&&s.particleFillColor&&(e=ki(this._engine,s.particleFillColor(t))),!i&&s.particleStrokeColor&&(i=ki(this._engine,s.particleStrokeColor(t))),e&&i)break;return[e,i]},this._initCover=async()=>{const t=this.container.actualOptions.backgroundMask.cover,e=t.color;if(e){const i=Mi(this._engine,e);if(i){const e={...i,a:t.opacity};this._coverColorStyle=Ei(e,e.a)}}else await new Promise(((e,i)=>{if(!t.image)return;const s=document.createElement("img");s.addEventListener("load",(()=>{this._coverImage={image:s,opacity:t.opacity},e()})),s.addEventListener("error",(t=>{i(t.error)})),s.src=t.image}))},this._initStyle=()=>{const t=this.element,e=this.container.actualOptions;if(t){this._fullScreen?this._setFullScreenStyle():this._resetOriginalStyle();for(const i in e.style){if(!i||!e.style||!Object.prototype.hasOwnProperty.call(e.style,i))continue;const s=e.style[i];s&&t.style.setProperty(i,s,"important")}}},this._initTrail=async()=>{const t=this.container.actualOptions.particles.move.trail,e=t.fill;if(!t.enable)return;const i=F/t.length;if(e.color){const t=Mi(this._engine,e.color);if(!t)return;this._trailFill={color:{...t},opacity:i}}else await new Promise(((t,s)=>{if(!e.image)return;const n=document.createElement("img");n.addEventListener("load",(()=>{this._trailFill={image:n,opacity:i},t()})),n.addEventListener("error",(t=>{s(t.error)})),n.src=e.image}))},this._paintBase=t=>{this.draw((e=>Gi(e,this.size,t)))},this._paintImage=(t,e)=>{this.draw((i=>Ni(i,this.size,t,e)))},this._repairStyle=()=>{const t=this.element;t&&(this._safeMutationObserver((t=>t.disconnect())),this._initStyle(),this.initBackground(),this._safeMutationObserver((e=>{t&&t instanceof Node&&e.observe(t,{attributes:!0})})))},this._resetOriginalStyle=()=>{const t=this.element,e=this._originalStyle;t&&e&&is(t,e,!0)},this._safeMutationObserver=t=>{this._mutationObserver&&t(this._mutationObserver)},this._setFullScreenStyle=()=>{const t=this.element;t&&is(t,li(this.container.actualOptions.fullScreen.zIndex),!0)},this._engine=e,this._standardSize={height:0,width:0};const i=t.retina.pixelRatio,s=this._standardSize;this.size={height:s.height*i,width:s.width*i},this._context=null,this._generated=!1,this._preDrawUpdaters=[],this._postDrawUpdaters=[],this._resizePlugins=[],this._colorPlugins=[]}get _fullScreen(){return this.container.actualOptions.fullScreen.enable}clear(){const t=this.container.actualOptions,e=t.particles.move.trail,i=this._trailFill;t.backgroundMask.enable?this.paint():e.enable&&e.length>Y&&i?i.color?this._paintBase(Ei(i.color,i.opacity)):i.image&&this._paintImage(i.image,i.opacity):t.clear&&this.draw((t=>{ji(t,this.size)}))}destroy(){if(this.stop(),this._generated){const t=this.element;t?.remove(),this.element=void 0}else this._resetOriginalStyle();this._preDrawUpdaters=[],this._postDrawUpdaters=[],this._resizePlugins=[],this._colorPlugins=[]}draw(t){const e=this._context;if(e)return t(e)}drawAsync(t){const e=this._context;if(e)return t(e)}drawParticle(t,e){if(t.spawning||t.destroyed)return;const i=t.getRadius();if(i<=X)return;const s=t.getFillColor(),n=t.getStrokeColor()??s;let[o,a]=this._getPluginParticleColors(t);o||(o=s),a||(a=n),(o||a)&&this.draw((s=>{const n=this.container,r=n.actualOptions,c=t.options.zIndex,h=Z-t.zIndexFactor,l=h**c.opacityRate,d=t.bubble.opacity??t.opacity?.value??J,u=d*l,p=(t.strokeOpacity??d)*l,f={},m={fill:o?Li(o,u):void 0};m.stroke=a?Li(a,p):m.fill,this._applyPreDrawUpdaters(s,t,i,u,m,f),Qi({container:n,context:s,particle:t,delta:e,colorStyles:m,backgroundMask:r.backgroundMask.enable,composite:r.backgroundMask.composite,radius:i*h**c.sizeRate,opacity:u,shadow:t.options.shadow,transform:f}),this._applyPostDrawUpdaters(t)}))}drawParticlePlugin(t,e,i){this.draw((s=>Ki(s,t,e,i)))}drawPlugin(t,e){this.draw((i=>Ji(i,t,e)))}async init(){this._safeMutationObserver((t=>t.disconnect())),this._mutationObserver=Ve((t=>{for(const e of t)"attributes"===e.type&&"style"===e.attributeName&&this._repairStyle()})),this.resize(),this._initStyle(),await this._initCover();try{await this._initTrail()}catch(t){Ie().error(t)}this.initBackground(),this._safeMutationObserver((t=>{this.element&&this.element instanceof Node&&t.observe(this.element,{attributes:!0})})),this.initUpdaters(),this.initPlugins(),this.paint()}initBackground(){const t=this.container.actualOptions.background,e=this.element;if(!e)return;const i=e.style;if(i){if(t.color){const e=Mi(this._engine,t.color);i.backgroundColor=e?Ei(e,t.opacity):""}else i.backgroundColor="";i.backgroundImage=t.image||"",i.backgroundPosition=t.position||"",i.backgroundRepeat=t.repeat||"",i.backgroundSize=t.size||""}}initPlugins(){this._resizePlugins=[];for(const t of this.container.plugins.values())t.resize&&this._resizePlugins.push(t),(t.particleFillColor??t.particleStrokeColor)&&this._colorPlugins.push(t)}initUpdaters(){this._preDrawUpdaters=[],this._postDrawUpdaters=[];for(const t of this.container.particles.updaters)t.afterDraw&&this._postDrawUpdaters.push(t),(t.getColorStyles??t.getTransformValues??t.beforeDraw)&&this._preDrawUpdaters.push(t)}loadCanvas(t){this._generated&&this.element&&this.element.remove(),this._generated=t.dataset&&i in t.dataset?"true"===t.dataset[i]:this._generated,this.element=t,this.element.ariaHidden="true",this._originalStyle=hi(this.element.style);const e=this._standardSize;e.height=t.offsetHeight,e.width=t.offsetWidth;const s=this.container.retina.pixelRatio,n=this.size;t.height=n.height=e.height*s,t.width=n.width=e.width*s,this._context=this.element.getContext("2d"),this._safeMutationObserver((t=>t.disconnect())),this.container.retina.init(),this.initBackground(),this._safeMutationObserver((t=>{this.element&&this.element instanceof Node&&t.observe(this.element,{attributes:!0})}))}paint(){const t=this.container.actualOptions;this.draw((e=>{t.backgroundMask.enable&&t.backgroundMask.cover?(ji(e,this.size),this._coverImage?this._paintImage(this._coverImage.image,this._coverImage.opacity):this._coverColorStyle?this._paintBase(this._coverColorStyle):this._paintBase()):this._paintBase()}))}resize(){if(!this.element)return!1;const t=this.container,e=t.canvas._standardSize,i={width:this.element.offsetWidth,height:this.element.offsetHeight},s=t.retina.pixelRatio,n={width:i.width*s,height:i.height*s};if(i.height===e.height&&i.width===e.width&&n.height===this.element.height&&n.width===this.element.width)return!1;const o={...e};e.height=i.height,e.width=i.width;const a=this.size;return this.element.width=a.width=n.width,this.element.height=a.height=n.height,this.container.started&&t.particles.setResizeFactor({width:e.width/o.width,height:e.height/o.height}),!0}stop(){this._safeMutationObserver((t=>t.disconnect())),this._mutationObserver=void 0,this.draw((t=>ji(t,this.size)))}async windowResize(){if(!this.element||!this.resize())return;const t=this.container,e=t.updateActualOptions();t.particles.setDensity(),this._applyResizePlugins(),e&&await t.refresh()}}function ns(t,e,i,s,n){if(s){let s={passive:!0};$t(n)?s.capture=n:void 0!==n&&(s=n),t.addEventListener(e,i,s)}else{const s=n;t.removeEventListener(e,i,s)}}!function(t){t.canvas="canvas",t.parent="parent",t.window="window"}(ui||(ui={}));class os{constructor(t){this.container=t,this._doMouseTouchClick=t=>{const e=this.container,i=e.actualOptions;if(this._canPush){const t=e.interactivity.mouse,s=t.position;if(!s)return;t.clickPosition={...s},t.clickTime=(new Date).getTime();ei(i.interactivity.events.onClick.mode,(t=>this.container.handleClickMode(t)))}"touchend"===t.type&&setTimeout((()=>this._mouseTouchFinish()),kt)},this._handleThemeChange=t=>{const e=t,i=this.container,s=i.options,n=s.defaultThemes,o=e.matches?n.dark:n.light,a=s.themes.find((t=>t.name===o));a?.default.auto&&i.loadTheme(o)},this._handleVisibilityChange=()=>{const t=this.container,e=t.actualOptions;this._mouseTouchFinish(),e.pauseOnBlur&&(document?.hidden?(t.pageHidden=!0,t.pause()):(t.pageHidden=!1,t.animationStatus?t.play(!0):t.draw(!0)))},this._handleWindowResize=()=>{this._resizeTimeout&&(clearTimeout(this._resizeTimeout),delete this._resizeTimeout);const t=async()=>{const t=this.container.canvas;await(t?.windowResize())};this._resizeTimeout=setTimeout((()=>{t()}),this.container.actualOptions.interactivity.events.resize.delay*g)},this._manageInteractivityListeners=(t,e)=>{const i=this._handlers,o=this.container,a=o.actualOptions,u=o.interactivity.element;if(!u)return;const p=u,f=o.canvas.element;f&&(f.style.pointerEvents=p===f?"initial":"none"),(a.interactivity.events.onHover.enable||a.interactivity.events.onClick.enable)&&(ns(u,r,i.mouseMove,e),ns(u,c,i.touchStart,e),ns(u,l,i.touchMove,e),a.interactivity.events.onClick.enable?(ns(u,h,i.touchEndClick,e),ns(u,n,i.mouseUp,e),ns(u,s,i.mouseDown,e)):ns(u,h,i.touchEnd,e),ns(u,t,i.mouseLeave,e),ns(u,d,i.touchCancel,e))},this._manageListeners=t=>{const e=this._handlers,i=this.container,s=i.actualOptions.interactivity.detectsOn,n=i.canvas.element;let r=o;s===ui.window?(i.interactivity.element=window,r=a):s===ui.parent&&n?i.interactivity.element=n.parentElement??n.parentNode:i.interactivity.element=n,this._manageMediaMatch(t),this._manageResize(t),this._manageInteractivityListeners(r,t),document&&ns(document,p,e.visibilityChange,t,!1)},this._manageMediaMatch=t=>{const e=this._handlers,i=Ae("(prefers-color-scheme: dark)");i&&(void 0===i.addEventListener?void 0!==i.addListener&&(t?i.addListener(e.oldThemeChange):i.removeListener(e.oldThemeChange)):ns(i,"change",e.themeChange,t))},this._manageResize=t=>{const e=this._handlers,i=this.container;if(!i.actualOptions.interactivity.events.resize)return;if("undefined"==typeof ResizeObserver)return void ns(window,u,e.resize,t);const s=i.canvas.element;this._resizeObserver&&!t?(s&&this._resizeObserver.unobserve(s),this._resizeObserver.disconnect(),delete this._resizeObserver):!this._resizeObserver&&t&&s&&(this._resizeObserver=new ResizeObserver((t=>{t.find((t=>t.target===s))&&this._handleWindowResize()})),this._resizeObserver.observe(s))},this._mouseDown=()=>{const{interactivity:t}=this.container;if(!t)return;const{mouse:e}=t;e.clicking=!0,e.downPosition=e.position},this._mouseTouchClick=t=>{const e=this.container,i=e.actualOptions,{mouse:s}=e.interactivity;s.inside=!0;let n=!1;const o=s.position;if(o&&i.interactivity.events.onClick.enable){for(const t of e.plugins.values())if(t.clickPositionValid&&(n=t.clickPositionValid(o),n))break;n||this._doMouseTouchClick(t),s.clicking=!1}},this._mouseTouchFinish=()=>{const t=this.container.interactivity;if(!t)return;const e=t.mouse;delete e.position,delete e.clickPosition,delete e.downPosition,t.status=o,e.inside=!1,e.clicking=!1},this._mouseTouchMove=t=>{const e=this.container,i=e.actualOptions,s=e.interactivity,n=e.canvas.element;if(!s?.element)return;let o;if(s.mouse.inside=!0,t.type.startsWith("pointer")){this._canPush=!0;const e=t;if(s.element===window){if(n){const t=n.getBoundingClientRect();o={x:e.clientX-t.left,y:e.clientY-t.top}}}else if(i.interactivity.detectsOn===ui.parent){const t=e.target,i=e.currentTarget;if(t&&i&&n){const s=t.getBoundingClientRect(),a=i.getBoundingClientRect(),r=n.getBoundingClientRect();o={x:e.offsetX+x*s.left-(a.left+r.left),y:e.offsetY+x*s.top-(a.top+r.top)}}else o={x:e.offsetX??e.clientX,y:e.offsetY??e.clientY}}else e.target===n&&(o={x:e.offsetX??e.clientX,y:e.offsetY??e.clientY})}else if(this._canPush="touchmove"!==t.type,n){const e=t,i=e.touches[e.touches.length-Mt],s=n.getBoundingClientRect();o={x:i.clientX-(s.left??et),y:i.clientY-(s.top??et)}}const a=e.retina.pixelRatio;o&&(o.x*=a,o.y*=a),s.mouse.position=o,s.status=r},this._touchEnd=t=>{const e=t,i=Array.from(e.changedTouches);for(const t of i)this._touches.delete(t.identifier);this._mouseTouchFinish()},this._touchEndClick=t=>{const e=t,i=Array.from(e.changedTouches);for(const t of i)this._touches.delete(t.identifier);this._mouseTouchClick(t)},this._touchStart=t=>{const e=t,i=Array.from(e.changedTouches);for(const t of i)this._touches.set(t.identifier,performance.now());this._mouseTouchMove(t)},this._canPush=!0,this._touches=new Map,this._handlers={mouseDown:()=>this._mouseDown(),mouseLeave:()=>this._mouseTouchFinish(),mouseMove:t=>this._mouseTouchMove(t),mouseUp:t=>this._mouseTouchClick(t),touchStart:t=>this._touchStart(t),touchMove:t=>this._mouseTouchMove(t),touchEnd:t=>this._touchEnd(t),touchCancel:t=>this._touchEnd(t),touchEndClick:t=>this._touchEndClick(t),visibilityChange:()=>this._handleVisibilityChange(),themeChange:t=>this._handleThemeChange(t),oldThemeChange:t=>this._handleThemeChange(t),resize:()=>{this._handleWindowResize()}}}addListeners(){this._manageListeners(!0)}removeListeners(){this._manageListeners(!1)}}!function(t){t.configAdded="configAdded",t.containerInit="containerInit",t.particlesSetup="particlesSetup",t.containerStarted="containerStarted",t.containerStopped="containerStopped",t.containerDestroyed="containerDestroyed",t.containerPaused="containerPaused",t.containerPlay="containerPlay",t.containerBuilt="containerBuilt",t.particleAdded="particleAdded",t.particleDestroyed="particleDestroyed",t.particleRemoved="particleRemoved"}(pi||(pi={}));class as{constructor(){this.value=""}static create(t,e){const i=new as;return i.load(t),void 0!==e&&(Gt(e)||Xt(e)?i.load({value:e}):i.load(e)),i}load(t){Yt(t)||Yt(t.value)||(this.value=t.value)}}class rs{constructor(){this.color=new as,this.color.value="",this.image="",this.position="",this.repeat="",this.size="",this.opacity=1}load(t){Yt(t)||(void 0!==t.color&&(this.color=as.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image),void 0!==t.position&&(this.position=t.position),void 0!==t.repeat&&(this.repeat=t.repeat),void 0!==t.size&&(this.size=t.size),void 0!==t.opacity&&(this.opacity=t.opacity))}}class cs{constructor(){this.opacity=1}load(t){Yt(t)||(void 0!==t.color&&(this.color=as.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image),void 0!==t.opacity&&(this.opacity=t.opacity))}}class hs{constructor(){this.composite="destination-out",this.cover=new cs,this.enable=!1}load(t){if(!Yt(t)){if(void 0!==t.composite&&(this.composite=t.composite),void 0!==t.cover){const e=t.cover,i=Gt(t.cover)?{color:t.cover}:t.cover;this.cover.load(void 0!==e.color||void 0!==e.image?e:{color:i})}void 0!==t.enable&&(this.enable=t.enable)}}}class ls{constructor(){this.enable=!0,this.zIndex=0}load(t){Yt(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.zIndex&&(this.zIndex=t.zIndex))}}class ds{constructor(){this.enable=!1,this.mode=[]}load(t){Yt(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode))}}!function(t){t.circle="circle",t.rectangle="rectangle"}(fi||(fi={}));class us{constructor(){this.selectors=[],this.enable=!1,this.mode=[],this.type=fi.circle}load(t){Yt(t)||(void 0!==t.selectors&&(this.selectors=t.selectors),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.type&&(this.type=t.type))}}class ps{constructor(){this.enable=!1,this.force=2,this.smooth=10}load(t){Yt(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.force&&(this.force=t.force),void 0!==t.smooth&&(this.smooth=t.smooth))}}class fs{constructor(){this.enable=!1,this.mode=[],this.parallax=new ps}load(t){Yt(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),this.parallax.load(t.parallax))}}class ms{constructor(){this.delay=.5,this.enable=!0}load(t){Yt(t)||(void 0!==t.delay&&(this.delay=t.delay),void 0!==t.enable&&(this.enable=t.enable))}}class vs{constructor(){this.onClick=new ds,this.onDiv=new us,this.onHover=new fs,this.resize=new ms}load(t){if(Yt(t))return;this.onClick.load(t.onClick);const e=t.onDiv;void 0!==e&&(this.onDiv=ei(e,(t=>{const e=new us;return e.load(t),e}))),this.onHover.load(t.onHover),this.resize.load(t.resize)}}class gs{constructor(t,e){this._engine=t,this._container=e}load(t){if(Yt(t))return;if(!this._container)return;const e=this._engine.interactors.get(this._container);if(e)for(const i of e)i.loadModeOptions&&i.loadModeOptions(this,t)}}class ys{constructor(t,e){this.detectsOn=ui.window,this.events=new vs,this.modes=new gs(t,e)}load(t){if(Yt(t))return;const e=t.detectsOn;void 0!==e&&(this.detectsOn=e),this.events.load(t.events),this.modes.load(t.modes)}}class _s{load(t){Yt(t)||(t.position&&(this.position={x:t.position.x??St,y:t.position.y??St,mode:t.position.mode??Ce.percent}),t.options&&(this.options=je({},t.options)))}}!function(t){t.screen="screen",t.canvas="canvas"}(mi||(mi={}));class ws{constructor(){this.maxWidth=1/0,this.options={},this.mode=mi.canvas}load(t){Yt(t)||(Yt(t.maxWidth)||(this.maxWidth=t.maxWidth),Yt(t.mode)||(t.mode===mi.screen?this.mode=mi.screen:this.mode=mi.canvas),Yt(t.options)||(this.options=je({},t.options)))}}!function(t){t.any="any",t.dark="dark",t.light="light"}(vi||(vi={}));class bs{constructor(){this.auto=!1,this.mode=vi.any,this.value=!1}load(t){Yt(t)||(void 0!==t.auto&&(this.auto=t.auto),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.value&&(this.value=t.value))}}class xs{constructor(){this.name="",this.default=new bs}load(t){Yt(t)||(void 0!==t.name&&(this.name=t.name),this.default.load(t.default),void 0!==t.options&&(this.options=je({},t.options)))}}class zs{constructor(){this.count=0,this.enable=!1,this.speed=1,this.decay=0,this.delay=0,this.sync=!1}load(t){Yt(t)||(void 0!==t.count&&(this.count=ue(t.count)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=ue(t.speed)),void 0!==t.decay&&(this.decay=ue(t.decay)),void 0!==t.delay&&(this.delay=ue(t.delay)),void 0!==t.sync&&(this.sync=t.sync))}}class Ms extends zs{constructor(){super(),this.mode=Oe.auto,this.startValue=Re.random}load(t){super.load(t),Yt(t)||(void 0!==t.mode&&(this.mode=t.mode),void 0!==t.startValue&&(this.startValue=t.startValue))}}class Os extends zs{constructor(){super(),this.offset=0,this.sync=!0}load(t){super.load(t),Yt(t)||void 0!==t.offset&&(this.offset=ue(t.offset))}}class Ps{constructor(){this.h=new Os,this.s=new Os,this.l=new Os}load(t){Yt(t)||(this.h.load(t.h),this.s.load(t.s),this.l.load(t.l))}}class ks extends as{constructor(){super(),this.animation=new Ps}static create(t,e){const i=new ks;return i.load(t),void 0!==e&&(Gt(e)||Xt(e)?i.load({value:e}):i.load(e)),i}load(t){if(super.load(t),Yt(t))return;const e=t.animation;void 0!==e&&(void 0!==e.enable?this.animation.h.load(e):this.animation.load(t.animation))}}!function(t){t.absorb="absorb",t.bounce="bounce",t.destroy="destroy"}(gi||(gi={}));class Ss{constructor(){this.speed=2}load(t){Yt(t)||void 0!==t.speed&&(this.speed=t.speed)}}class Cs{constructor(){this.enable=!0,this.retries=0}load(t){Yt(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.retries&&(this.retries=t.retries))}}class Rs{constructor(){this.value=0}load(t){Yt(t)||Yt(t.value)||(this.value=ue(t.value))}}class Ts extends Rs{constructor(){super(),this.animation=new zs}load(t){if(super.load(t),Yt(t))return;const e=t.animation;void 0!==e&&this.animation.load(e)}}class Ds extends Ts{constructor(){super(),this.animation=new Ms}load(t){super.load(t)}}class Is extends Rs{constructor(){super(),this.value=1}}class Es{constructor(){this.horizontal=new Is,this.vertical=new Is}load(t){Yt(t)||(this.horizontal.load(t.horizontal),this.vertical.load(t.vertical))}}class Ls{constructor(){this.absorb=new Ss,this.bounce=new Es,this.enable=!1,this.maxSpeed=50,this.mode=gi.bounce,this.overlap=new Cs}load(t){Yt(t)||(this.absorb.load(t.absorb),this.bounce.load(t.bounce),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.maxSpeed&&(this.maxSpeed=ue(t.maxSpeed)),void 0!==t.mode&&(this.mode=t.mode),this.overlap.load(t.overlap))}}class Fs{constructor(){this.close=!0,this.fill=!0,this.options={},this.type=[]}load(t){if(Yt(t))return;const e=t.options;if(void 0!==e)for(const t in e){const i=e[t];i&&(this.options[t]=je(this.options[t]??{},i))}void 0!==t.close&&(this.close=t.close),void 0!==t.fill&&(this.fill=t.fill),void 0!==t.type&&(this.type=t.type)}}class As{constructor(){this.offset=0,this.value=90}load(t){Yt(t)||(void 0!==t.offset&&(this.offset=ue(t.offset)),void 0!==t.value&&(this.value=ue(t.value)))}}class Bs{constructor(){this.distance=200,this.enable=!1,this.rotate={x:3e3,y:3e3}}load(t){if(!Yt(t)&&(void 0!==t.distance&&(this.distance=ue(t.distance)),void 0!==t.enable&&(this.enable=t.enable),t.rotate)){const e=t.rotate.x;void 0!==e&&(this.rotate.x=e);const i=t.rotate.y;void 0!==i&&(this.rotate.y=i)}}}class Vs{constructor(){this.x=50,this.y=50,this.mode=Ce.percent,this.radius=0}load(t){Yt(t)||(void 0!==t.x&&(this.x=t.x),void 0!==t.y&&(this.y=t.y),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.radius&&(this.radius=t.radius))}}class Hs{constructor(){this.acceleration=9.81,this.enable=!1,this.inverse=!1,this.maxSpeed=50}load(t){Yt(t)||(void 0!==t.acceleration&&(this.acceleration=ue(t.acceleration)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.inverse&&(this.inverse=t.inverse),void 0!==t.maxSpeed&&(this.maxSpeed=ue(t.maxSpeed)))}}class qs{constructor(){this.clamp=!0,this.delay=new Rs,this.enable=!1,this.options={}}load(t){Yt(t)||(void 0!==t.clamp&&(this.clamp=t.clamp),this.delay.load(t.delay),void 0!==t.enable&&(this.enable=t.enable),this.generator=t.generator,t.options&&(this.options=je(this.options,t.options)))}}class Ws{load(t){Yt(t)||(void 0!==t.color&&(this.color=as.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image))}}class Us{constructor(){this.enable=!1,this.length=10,this.fill=new Ws}load(t){Yt(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.fill&&this.fill.load(t.fill),void 0!==t.length&&(this.length=t.length))}}!function(t){t.bounce="bounce",t.none="none",t.out="out",t.destroy="destroy",t.split="split"}(yi||(yi={}));class $s{constructor(){this.default=yi.out}load(t){Yt(t)||(void 0!==t.default&&(this.default=t.default),this.bottom=t.bottom??t.default,this.left=t.left??t.default,this.right=t.right??t.default,this.top=t.top??t.default)}}class Gs{constructor(){this.acceleration=0,this.enable=!1}load(t){Yt(t)||(void 0!==t.acceleration&&(this.acceleration=ue(t.acceleration)),void 0!==t.enable&&(this.enable=t.enable),t.position&&(this.position=je({},t.position)))}}class Ns{constructor(){this.angle=new As,this.attract=new Bs,this.center=new Vs,this.decay=0,this.distance={},this.direction=Ut.none,this.drift=0,this.enable=!1,this.gravity=new Hs,this.path=new qs,this.outModes=new $s,this.random=!1,this.size=!1,this.speed=2,this.spin=new Gs,this.straight=!1,this.trail=new Us,this.vibrate=!1,this.warp=!1}load(t){if(Yt(t))return;this.angle.load(Nt(t.angle)?{value:t.angle}:t.angle),this.attract.load(t.attract),this.center.load(t.center),void 0!==t.decay&&(this.decay=ue(t.decay)),void 0!==t.direction&&(this.direction=t.direction),void 0!==t.distance&&(this.distance=Nt(t.distance)?{horizontal:t.distance,vertical:t.distance}:{...t.distance}),void 0!==t.drift&&(this.drift=ue(t.drift)),void 0!==t.enable&&(this.enable=t.enable),this.gravity.load(t.gravity);const e=t.outModes;void 0!==e&&(Qt(e)?this.outModes.load(e):this.outModes.load({default:e})),this.path.load(t.path),void 0!==t.random&&(this.random=t.random),void 0!==t.size&&(this.size=t.size),void 0!==t.speed&&(this.speed=ue(t.speed)),this.spin.load(t.spin),void 0!==t.straight&&(this.straight=t.straight),this.trail.load(t.trail),void 0!==t.vibrate&&(this.vibrate=t.vibrate),void 0!==t.warp&&(this.warp=t.warp)}}class js extends Ms{constructor(){super(),this.destroy=ke.none,this.speed=2}load(t){super.load(t),Yt(t)||void 0!==t.destroy&&(this.destroy=t.destroy)}}class Qs extends Ds{constructor(){super(),this.animation=new js,this.value=1}load(t){if(Yt(t))return;super.load(t);const e=t.animation;void 0!==e&&this.animation.load(e)}}class Xs{constructor(){this.enable=!1,this.width=1920,this.height=1080}load(t){if(Yt(t))return;void 0!==t.enable&&(this.enable=t.enable);const e=t.width;void 0!==e&&(this.width=e);const i=t.height;void 0!==i&&(this.height=i)}}!function(t){t.delete="delete",t.wait="wait"}(_i||(_i={}));class Ys{constructor(){this.mode=_i.delete,this.value=0}load(t){Yt(t)||(void 0!==t.mode&&(this.mode=t.mode),void 0!==t.value&&(this.value=t.value))}}class Zs{constructor(){this.density=new Xs,this.limit=new Ys,this.value=0}load(t){Yt(t)||(this.density.load(t.density),this.limit.load(t.limit),void 0!==t.value&&(this.value=t.value))}}class Js{constructor(){this.blur=0,this.color=new as,this.enable=!1,this.offset={x:0,y:0},this.color.value="#000"}load(t){Yt(t)||(void 0!==t.blur&&(this.blur=t.blur),this.color=as.create(this.color,t.color),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.offset&&(void 0!==t.offset.x&&(this.offset.x=t.offset.x),void 0!==t.offset.y&&(this.offset.y=t.offset.y)))}}class Ks{constructor(){this.close=!0,this.fill=!0,this.options={},this.type="circle"}load(t){if(Yt(t))return;const e=t.options;if(void 0!==e)for(const t in e){const i=e[t];i&&(this.options[t]=je(this.options[t]??{},i))}void 0!==t.close&&(this.close=t.close),void 0!==t.fill&&(this.fill=t.fill),void 0!==t.type&&(this.type=t.type)}}class tn extends Ms{constructor(){super(),this.destroy=ke.none,this.speed=5}load(t){super.load(t),Yt(t)||void 0!==t.destroy&&(this.destroy=t.destroy)}}class en extends Ds{constructor(){super(),this.animation=new tn,this.value=3}load(t){if(super.load(t),Yt(t))return;const e=t.animation;void 0!==e&&this.animation.load(e)}}class sn{constructor(){this.width=0}load(t){Yt(t)||(void 0!==t.color&&(this.color=ks.create(this.color,t.color)),void 0!==t.width&&(this.width=ue(t.width)),void 0!==t.opacity&&(this.opacity=ue(t.opacity)))}}class nn extends Rs{constructor(){super(),this.opacityRate=1,this.sizeRate=1,this.velocityRate=1}load(t){super.load(t),Yt(t)||(void 0!==t.opacityRate&&(this.opacityRate=t.opacityRate),void 0!==t.sizeRate&&(this.sizeRate=t.sizeRate),void 0!==t.velocityRate&&(this.velocityRate=t.velocityRate))}}class on{constructor(t,e){this._engine=t,this._container=e,this.bounce=new Es,this.collisions=new Ls,this.color=new ks,this.color.value="#fff",this.effect=new Fs,this.groups={},this.move=new Ns,this.number=new Zs,this.opacity=new Qs,this.reduceDuplicates=!1,this.shadow=new Js,this.shape=new Ks,this.size=new en,this.stroke=new sn,this.zIndex=new nn}load(t){if(Yt(t))return;if(void 0!==t.groups)for(const e of Object.keys(t.groups)){if(!Object.hasOwn(t.groups,e))continue;const i=t.groups[e];void 0!==i&&(this.groups[e]=je(this.groups[e]??{},i))}void 0!==t.reduceDuplicates&&(this.reduceDuplicates=t.reduceDuplicates),this.bounce.load(t.bounce),this.color.load(ks.create(this.color,t.color)),this.effect.load(t.effect),this.move.load(t.move),this.number.load(t.number),this.opacity.load(t.opacity),this.shape.load(t.shape),this.size.load(t.size),this.shadow.load(t.shadow),this.zIndex.load(t.zIndex),this.collisions.load(t.collisions),void 0!==t.interactivity&&(this.interactivity=je({},t.interactivity));const e=t.stroke;if(e&&(this.stroke=ei(e,(t=>{const e=new sn;return e.load(t),e}))),this._container){const e=this._engine.updaters.get(this._container);if(e)for(const i of e)i.loadOptions&&i.loadOptions(this,t);const i=this._engine.interactors.get(this._container);if(i)for(const e of i)e.loadParticlesOptions&&e.loadParticlesOptions(this,t)}}}function an(t,...e){for(const i of e)t.load(i)}function rn(t,e,...i){const s=new on(t,e);return an(s,...i),s}class cn{constructor(t,e){this._findDefaultTheme=t=>this.themes.find((e=>e.default.value&&e.default.mode===t))??this.themes.find((t=>t.default.value&&t.default.mode===vi.any)),this._importPreset=t=>{this.load(this._engine.getPreset(t))},this._engine=t,this._container=e,this.autoPlay=!0,this.background=new rs,this.backgroundMask=new hs,this.clear=!0,this.defaultThemes={},this.delay=0,this.fullScreen=new ls,this.detectRetina=!0,this.duration=0,this.fpsLimit=120,this.interactivity=new ys(t,e),this.manualParticles=[],this.particles=rn(this._engine,this._container),this.pauseOnBlur=!0,this.pauseOnOutsideViewport=!0,this.responsive=[],this.smooth=!1,this.style={},this.themes=[],this.zLayers=100}load(t){if(Yt(t))return;void 0!==t.preset&&ei(t.preset,(t=>this._importPreset(t))),void 0!==t.autoPlay&&(this.autoPlay=t.autoPlay),void 0!==t.clear&&(this.clear=t.clear),void 0!==t.key&&(this.key=t.key),void 0!==t.name&&(this.name=t.name),void 0!==t.delay&&(this.delay=ue(t.delay));const e=t.detectRetina;void 0!==e&&(this.detectRetina=e),void 0!==t.duration&&(this.duration=ue(t.duration));const i=t.fpsLimit;void 0!==i&&(this.fpsLimit=i),void 0!==t.pauseOnBlur&&(this.pauseOnBlur=t.pauseOnBlur),void 0!==t.pauseOnOutsideViewport&&(this.pauseOnOutsideViewport=t.pauseOnOutsideViewport),void 0!==t.zLayers&&(this.zLayers=t.zLayers),this.background.load(t.background);const s=t.fullScreen;$t(s)?this.fullScreen.enable=s:this.fullScreen.load(s),this.backgroundMask.load(t.backgroundMask),this.interactivity.load(t.interactivity),t.manualParticles&&(this.manualParticles=t.manualParticles.map((t=>{const e=new _s;return e.load(t),e}))),this.particles.load(t.particles),this.style=je(this.style,t.style),this._engine.loadOptions(this,t),void 0!==t.smooth&&(this.smooth=t.smooth);const n=this._engine.interactors.get(this._container);if(n)for(const e of n)e.loadOptions&&e.loadOptions(this,t);if(void 0!==t.responsive)for(const e of t.responsive){const t=new ws;t.load(e),this.responsive.push(t)}if(this.responsive.sort(((t,e)=>t.maxWidth-e.maxWidth)),void 0!==t.themes)for(const e of t.themes){const t=this.themes.find((t=>t.name===e.name));if(t)t.load(e);else{const t=new xs;t.load(e),this.themes.push(t)}}this.defaultThemes.dark=this._findDefaultTheme(vi.dark)?.name,this.defaultThemes.light=this._findDefaultTheme(vi.light)?.name}setResponsive(t,e,i){this.load(i);const s=this.responsive.find((i=>i.mode===mi.screen&&screen?i.maxWidth>screen.availWidth:i.maxWidth*e>t));return this.load(s?.options),s?.maxWidth}setTheme(t){if(t){const e=this.themes.find((e=>e.name===t));e&&this.load(e.options)}else{const t=Ae("(prefers-color-scheme: dark)"),e=t?.matches,i=this._findDefaultTheme(e?vi.dark:vi.light);i&&this.load(i.options)}}}!function(t){t.external="external",t.particles="particles"}(wi||(wi={}));class hn{constructor(t,e){this.container=e,this._engine=t,this._interactors=[],this._externalInteractors=[],this._particleInteractors=[]}externalInteract(t){for(const e of this._externalInteractors)e.isEnabled()&&e.interact(t)}handleClickMode(t){for(const e of this._externalInteractors)e.handleClickMode?.(t)}async init(){this._interactors=await this._engine.getInteractors(this.container,!0),this._externalInteractors=[],this._particleInteractors=[];for(const t of this._interactors){switch(t.type){case wi.external:this._externalInteractors.push(t);break;case wi.particles:this._particleInteractors.push(t)}t.init()}}particlesInteract(t,e){for(const i of this._externalInteractors)i.clear(t,e);for(const i of this._particleInteractors)i.isEnabled(t)&&i.interact(t,e)}reset(t){for(const e of this._externalInteractors)e.isEnabled()&&e.reset(t);for(const e of this._particleInteractors)e.isEnabled(t)&&e.reset(t)}}function ln(t){if(!He(t.outMode,t.checkModes))return;const e=t.radius*x;t.coord>t.maxCoord-e?t.setCb(-t.radius):t.coord<e&&t.setCb(t.radius)}!function(t){t.normal="normal",t.inside="inside",t.outside="outside"}(bi||(bi={}));class dn{constructor(t,e){this.container=e,this._calcPosition=(t,e,i,s=C)=>{for(const s of t.plugins.values()){const t=void 0!==s.particlePosition?s.particlePosition(e,this):void 0;if(t)return Zt.create(t.x,t.y,i)}const n=xe({size:t.canvas.size,position:e}),o=Zt.create(n.x,n.y,i),a=this.getRadius(),r=this.options.move.outModes,c=e=>{ln({outMode:e,checkModes:[yi.bounce],coord:o.x,maxCoord:t.canvas.size.width,setCb:t=>o.x+=t,radius:a})},h=e=>{ln({outMode:e,checkModes:[yi.bounce],coord:o.y,maxCoord:t.canvas.size.height,setCb:t=>o.y+=t,radius:a})};return c(r.left??r.default),c(r.right??r.default),h(r.top??r.default),h(r.bottom??r.default),this._checkOverlap(o,s)?this._calcPosition(t,void 0,i,s+ut):o},this._calculateVelocity=()=>{const t=ge(this.direction).copy(),e=this.options.move;if(e.direction===Ut.inside||e.direction===Ut.outside)return t;const i=me(he(e.angle.value)),s=me(he(e.angle.offset)),n={left:s-i*v,right:s+i*v};return e.straight||(t.angle+=ce(ue(n.left,n.right))),e.random&&"number"==typeof e.speed&&(t.length*=ie()),t},this._checkOverlap=(t,e=C)=>{const i=this.options.collisions,s=this.getRadius();if(!i.enable)return!1;const n=i.overlap;if(n.enable)return!1;const o=n.retries;if(o>=pt&&e>o)throw new Error(`${f} particle is overlapping and can't be placed`);return!!this.container.particles.find((e=>fe(t,e.position)<s+e.getRadius()))},this._getRollColor=t=>{if(!t||!this.roll||!this.backColor&&!this.roll.alter)return t;const e=this.roll.horizontal&&this.roll.vertical?x*ft:ft,i=this.roll.horizontal?Math.PI*v:lt;return Math.floor(((this.roll.angle??lt)+i)/(Math.PI/e))%x?this.backColor?this.backColor:this.roll.alter?ts(t,this.roll.alter.type,this.roll.alter.value):t:t},this._initPosition=t=>{const e=this.container,i=he(this.options.zIndex.value);this.position=this._calcPosition(e,t,ae(i,mt,e.zLayers)),this.initialPosition=this.position.copy();const s=e.canvas.size;switch(this.moveCenter={...ai(this.options.move.center,s),radius:this.options.move.center.radius??vt,mode:this.options.move.center.mode??Ce.percent},this.direction=ve(this.options.move.direction,this.position,this.moveCenter),this.options.move.direction){case Ut.inside:this.outType=bi.inside;break;case Ut.outside:this.outType=bi.outside}this.offset=Jt.origin},this._engine=t}destroy(t){if(this.unbreakable||this.destroyed)return;this.destroyed=!0,this.bubble.inRange=!1,this.slow.inRange=!1;const e=this.container,i=this.pathGenerator,s=e.shapeDrawers.get(this.shape);s?.particleDestroy?.(this);for(const i of e.plugins.values())i.particleDestroyed?.(this,t);for(const i of e.particles.updaters)i.particleDestroyed?.(this,t);i?.reset(this),this._engine.dispatchEvent(pi.particleDestroyed,{container:this.container,data:{particle:this}})}draw(t){const e=this.container,i=e.canvas;for(const s of e.plugins.values())i.drawParticlePlugin(s,this,t);i.drawParticle(this,t)}getFillColor(){return this._getRollColor(this.bubble.color??Vi(this.color))}getMass(){return this.getRadius()**R*Math.PI*v}getPosition(){return{x:this.position.x+this.offset.x,y:this.position.y+this.offset.y,z:this.position.z}}getRadius(){return this.bubble.radius??this.size.value}getStrokeColor(){return this._getRollColor(this.bubble.color??Vi(this.strokeColor))}init(t,e,i,s){const n=this.container,o=this._engine;this.id=t,this.group=s,this.effectClose=!0,this.effectFill=!0,this.shapeClose=!0,this.shapeFill=!0,this.pathRotation=!1,this.lastPathTime=0,this.destroyed=!1,this.unbreakable=!1,this.isRotating=!1,this.rotation=0,this.misplaced=!1,this.retina={maxDistance:{}},this.outType=bi.normal,this.ignoresResizeRatio=!0;const a=n.retina.pixelRatio,r=n.actualOptions,c=rn(this._engine,n,r.particles),{reduceDuplicates:h}=c,l=c.effect.type,d=c.shape.type;this.effect=ii(l,this.id,h),this.shape=ii(d,this.id,h);const u=c.effect,p=c.shape;if(i){if(i.effect?.type){const t=ii(i.effect.type,this.id,h);t&&(this.effect=t,u.load(i.effect))}if(i.shape?.type){const t=ii(i.shape.type,this.id,h);t&&(this.shape=t,p.load(i.shape))}}if(this.effect===w){const t=[...this.container.effectDrawers.keys()];this.effect=t[Math.floor(Math.random()*t.length)]}if(this.shape===w){const t=[...this.container.shapeDrawers.keys()];this.shape=t[Math.floor(Math.random()*t.length)]}this.effectData=function(t,e,i,s){const n=e.options[t];if(n)return je({close:e.close,fill:e.fill},ii(n,i,s))}(this.effect,u,this.id,h),this.shapeData=function(t,e,i,s){const n=e.options[t];if(n)return je({close:e.close,fill:e.fill},ii(n,i,s))}(this.shape,p,this.id,h),c.load(i);const f=this.effectData;f&&c.load(f.particles);const m=this.shapeData;m&&c.load(m.particles);const v=new ys(o,n);v.load(n.actualOptions.interactivity),v.load(c.interactivity),this.interactivity=v,this.effectFill=f?.fill??c.effect.fill,this.effectClose=f?.close??c.effect.close,this.shapeFill=m?.fill??c.shape.fill,this.shapeClose=m?.close??c.shape.close,this.options=c;const y=this.options.move.path;this.pathDelay=he(y.delay.value)*g,y.generator&&(this.pathGenerator=this._engine.getPathGenerator(y.generator),this.pathGenerator&&n.addPath(y.generator,this.pathGenerator)&&this.pathGenerator.init(n)),n.retina.initParticle(this),this.size=ni(this.options.size,a),this.bubble={inRange:!1},this.slow={inRange:!1,factor:1},this._initPosition(e),this.initialVelocity=this._calculateVelocity(),this.velocity=this.initialVelocity.copy(),this.moveDecay=dt-he(this.options.move.decay);const _=n.particles;_.setLastZIndex(this.position.z),this.zIndexFactor=this.position.z/n.zLayers,this.sides=24;let b=n.effectDrawers.get(this.effect);b||(b=this._engine.getEffectDrawer(this.effect),b&&n.effectDrawers.set(this.effect,b)),b?.loadEffect&&b.loadEffect(this);let x=n.shapeDrawers.get(this.shape);x||(x=this._engine.getShapeDrawer(this.shape),x&&n.shapeDrawers.set(this.shape,x)),x?.loadShape&&x.loadShape(this);const z=x?.getSidesCount;z&&(this.sides=z(this)),this.spawning=!1,this.shadowColor=Mi(this._engine,this.options.shadow.color);for(const t of _.updaters)t.init(this);for(const t of _.movers)t.init?.(this);b?.particleInit?.(n,this),x?.particleInit?.(n,this);for(const t of n.plugins.values())t.particleCreated?.(this)}isInsideCanvas(){const t=this.getRadius(),e=this.container.canvas.size,i=this.position;return i.x>=-t&&i.y>=-t&&i.y<=e.height+t&&i.x<=e.width+t}isVisible(){return!this.destroyed&&!this.spawning&&this.isInsideCanvas()}reset(){for(const t of this.container.particles.updaters)t.reset?.(this)}}class un{constructor(t,e){this.position=t,this.particle=e}}!function(t){t.circle="circle",t.rectangle="rectangle"}(xi||(xi={}));class pn{constructor(t,e,i){this.position={x:t,y:e},this.type=i}}class fn extends pn{constructor(t,e,i){super(t,e,xi.circle),this.radius=i}contains(t){return fe(t,this.position)<=this.radius}intersects(t){const e=this.position,i=t.position,s=Math.abs(i.x-e.x),n=Math.abs(i.y-e.y),o=this.radius;if(t instanceof fn||t.type===xi.circle){return o+t.radius>Math.sqrt(s**R+n**R)}if(t instanceof mn||t.type===xi.rectangle){const e=t,{width:i,height:a}=e.size;return Math.pow(s-i,R)+Math.pow(n-a,R)<=o**R||s<=o+i&&n<=o+a||s<=i||n<=a}return!1}}class mn extends pn{constructor(t,e,i,s){super(t,e,xi.rectangle),this.size={height:s,width:i}}contains(t){const e=this.size.width,i=this.size.height,s=this.position;return t.x>=s.x&&t.x<=s.x+e&&t.y>=s.y&&t.y<=s.y+i}intersects(t){if(t instanceof fn)return t.intersects(this);const e=this.size.width,i=this.size.height,s=this.position,n=t.position,o=t instanceof mn?t.size:{width:0,height:0},a=o.width,r=o.height;return n.x<s.x+e&&n.x+a>s.x&&n.y<s.y+i&&n.y+r>s.y}}class vn{constructor(t,e){this.rectangle=t,this.capacity=e,this._subdivide=()=>{const{x:t,y:e}=this.rectangle.position,{width:i,height:s}=this.rectangle.size,{capacity:n}=this;for(let o=0;o<L;o++){const a=o%x;this._subs.push(new vn(new mn(t+i*v*a,e+s*v*(Math.round(o*v)-a),i*v,s*v),n))}this._divided=!0},this._points=[],this._divided=!1,this._subs=[]}insert(t){return!!this.rectangle.contains(t.position)&&(this._points.length<this.capacity?(this._points.push(t),!0):(this._divided||this._subdivide(),this._subs.some((e=>e.insert(t)))))}query(t,e){const i=[];if(!t.intersects(this.rectangle))return[];for(const s of this._points)!t.contains(s.position)&&fe(t.position,s.position)>s.particle.getRadius()&&(!e||e(s.particle))||i.push(s.particle);if(this._divided)for(const s of this._subs)i.push(...s.query(t,e));return i}queryCircle(t,e,i){return this.query(new fn(t.x,t.y,e),i)}queryRectangle(t,e,i){return this.query(new mn(t.x,t.y,e.width,e.height),i)}}const gn=t=>{const{height:e,width:i}=t;return new mn(gt*i,gt*e,yt*i,yt*e)};class yn{constructor(t,e){this._addToPool=(...t)=>{this._pool.push(...t)},this._applyDensity=(t,e,i)=>{const s=t.number;if(!t.number.density?.enable)return void(void 0===i?this._limit=s.limit.value:s.limit&&this._groupLimits.set(i,s.limit.value));const n=this._initDensityFactor(s.density),o=s.value,a=s.limit.value>_t?s.limit.value:o,r=Math.min(o,a)*n+e,c=Math.min(this.count,this.filter((t=>t.group===i)).length);void 0===i?this._limit=s.limit.value*n:this._groupLimits.set(i,s.limit.value*n),c<r?this.push(Math.abs(r-c),void 0,t,i):c>r&&this.removeQuantity(c-r,i)},this._initDensityFactor=t=>{const e=this._container;if(!e.canvas.element||!t.enable)return Ot;const i=e.canvas.element,s=e.retina.pixelRatio;return i.width*i.height/(t.height*t.width*s**R)},this._pushParticle=(t,e,i,s)=>{try{let n=this._pool.pop();n||(n=new dn(this._engine,this._container)),n.init(this._nextId,t,e,i);let o=!0;if(s&&(o=s(n)),!o)return;return this._array.push(n),this._zArray.push(n),this._nextId++,this._engine.dispatchEvent(pi.particleAdded,{container:this._container,data:{particle:n}}),n}catch(t){Ie().warning(`${f} adding particle: ${t}`)}},this._removeParticle=(t,e,i)=>{const s=this._array[t];if(!s||s.group!==e)return!1;const n=this._zArray.indexOf(s);return this._array.splice(t,Pt),this._zArray.splice(n,Pt),s.destroy(i),this._engine.dispatchEvent(pi.particleRemoved,{container:this._container,data:{particle:s}}),this._addToPool(s),!0},this._engine=t,this._container=e,this._nextId=0,this._array=[],this._zArray=[],this._pool=[],this._limit=0,this._groupLimits=new Map,this._needsSort=!1,this._lastZIndex=0,this._interactionManager=new hn(t,e),this._pluginsInitialized=!1;const i=e.canvas.size;this.quadTree=new vn(gn(i),T),this.movers=[],this.updaters=[]}get count(){return this._array.length}addManualParticles(){const t=this._container;t.actualOptions.manualParticles.forEach((e=>this.addParticle(e.position?ai(e.position,t.canvas.size):void 0,e.options)))}addParticle(t,e,i,s){const n=this._container.actualOptions.particles.number.limit.mode,o=void 0===i?this._limit:this._groupLimits.get(i)??this._limit,a=this.count;if(o>_t)switch(n){case _i.delete:{const t=a+wt-o;t>bt&&this.removeQuantity(t);break}case _i.wait:if(a>=o)return}return this._pushParticle(t,e,i,s)}clear(){this._array=[],this._zArray=[],this._pluginsInitialized=!1}destroy(){this._array=[],this._zArray=[],this.movers=[],this.updaters=[]}draw(t){const e=this._container,i=e.canvas;i.clear(),this.update(t);for(const s of e.plugins.values())i.drawPlugin(s,t);for(const e of this._zArray)e.draw(t)}filter(t){return this._array.filter(t)}find(t){return this._array.find(t)}get(t){return this._array[t]}handleClickMode(t){this._interactionManager.handleClickMode(t)}async init(){const t=this._container,e=t.actualOptions;this._lastZIndex=0,this._needsSort=!1,await this.initPlugins();let i=!1;for(const e of t.plugins.values())if(i=e.particlesInitialization?.()??i,i)break;if(this.addManualParticles(),!i){const t=e.particles,i=t.groups;for(const e in i){const s=i[e];for(let i=this.count,n=0;n<s.number?.value&&i<t.number.value;i++,n++)this.addParticle(void 0,s,e)}for(let e=this.count;e<t.number.value;e++)this.addParticle()}}async initPlugins(){if(this._pluginsInitialized)return;const t=this._container;this.movers=await this._engine.getMovers(t,!0),this.updaters=await this._engine.getUpdaters(t,!0),await this._interactionManager.init();for(const e of t.pathGenerators.values())e.init(t)}push(t,e,i,s){for(let n=0;n<t;n++)this.addParticle(e?.position,i,s)}async redraw(){this.clear(),await this.init(),this.draw({value:0,factor:0})}remove(t,e,i){this.removeAt(this._array.indexOf(t),void 0,e,i)}removeAt(t,e=D,i,s){if(t<xt||t>this.count)return;let n=0;for(let o=t;n<e&&o<this.count;o++)this._removeParticle(o,i,s)&&(o--,n++)}removeQuantity(t,e){this.removeAt(xt,t,e)}setDensity(){const t=this._container.actualOptions,e=t.particles.groups;for(const t in e)this._applyDensity(e[t],zt,t);this._applyDensity(t.particles,t.manualParticles.length)}setLastZIndex(t){this._lastZIndex=t,this._needsSort=this._needsSort||this._lastZIndex<t}setResizeFactor(t){this._resizeFactor=t}update(t){const e=this._container,i=new Set;this.quadTree=new vn(gn(e.canvas.size),T);for(const t of e.pathGenerators.values())t.update();for(const i of e.plugins.values())i.update?.(t);const s=this._resizeFactor;for(const e of this._array){s&&!e.ignoresResizeRatio&&(e.position.x*=s.width,e.position.y*=s.height,e.initialPosition.x*=s.width,e.initialPosition.y*=s.height),e.ignoresResizeRatio=!1,this._interactionManager.reset(e);for(const i of this._container.plugins.values()){if(e.destroyed)break;i.particleUpdate?.(e,t)}for(const i of this.movers)i.isEnabled(e)&&i.move(e,t);e.destroyed?i.add(e):this.quadTree.insert(new un(e.getPosition(),e))}if(i.size){const t=t=>!i.has(t);this._array=this.filter(t),this._zArray=this._zArray.filter(t);for(const t of i)this._engine.dispatchEvent(pi.particleRemoved,{container:this._container,data:{particle:t}});this._addToPool(...i)}this._interactionManager.externalInteract(t);for(const e of this._array){for(const i of this.updaters)i.update(e,t);e.destroyed||e.spawning||this._interactionManager.particlesInteract(e,t)}if(delete this._resizeFactor,this._needsSort){const t=this._zArray;t.sort(((t,e)=>e.position.z-t.position.z||t.id-e.id)),this._lastZIndex=t[t.length-Mt].position.z,this._needsSort=!1}}}class _n{constructor(t){this.container=t,this.pixelRatio=I,this.reduceFactor=E}init(){const t=this.container,e=t.actualOptions;this.pixelRatio=!e.detectRetina||Le()?I:window.devicePixelRatio,this.reduceFactor=E;const i=this.pixelRatio,s=t.canvas;if(s.element){const t=s.element;s.size.width=t.offsetWidth*i,s.size.height=t.offsetHeight*i}const n=e.particles,o=n.move;this.maxSpeed=he(o.gravity.maxSpeed)*i,this.sizeAnimationSpeed=he(n.size.animation.speed)*i}initParticle(t){const e=t.options,i=this.pixelRatio,s=e.move,n=s.distance,o=t.retina;o.moveDrift=he(s.drift)*i,o.moveSpeed=he(s.speed)*i,o.sizeAnimationSpeed=he(e.size.animation.speed)*i;const a=o.maxDistance;a.horizontal=void 0!==n.horizontal?n.horizontal*i:void 0,a.vertical=void 0!==n.vertical?n.vertical*i:void 0,o.maxSpeed=he(s.gravity.maxSpeed)*i}}function wn(t){return t&&!t.destroyed}function bn(t,e,...i){const s=new cn(t,e);return an(s,...i),s}class xn{constructor(t,e,i){this._intersectionManager=t=>{if(wn(this)&&this.actualOptions.pauseOnOutsideViewport)for(const e of t)e.target===this.interactivity.element&&(e.isIntersecting?this.play():this.pause())},this._nextFrame=t=>{try{if(!this._smooth&&void 0!==this._lastFrameTime&&t<this._lastFrameTime+g/this.fpsLimit)return void this.draw(!1);this._lastFrameTime??=t;const e=function(t,e=M,i=!1){return{value:t,factor:i?M/e:M*t/g}}(t-this._lastFrameTime,this.fpsLimit,this._smooth);if(this.addLifeTime(e.value),this._lastFrameTime=t,e.value>g)return void this.draw(!1);if(this.particles.draw(e),!this.alive())return void this.destroy();this.animationStatus&&this.draw(!1)}catch(t){Ie().error(`${f} in animation loop`,t)}},this._engine=t,this.id=Symbol(e),this.fpsLimit=120,this._smooth=!1,this._delay=0,this._duration=0,this._lifeTime=0,this._firstStart=!0,this.started=!1,this.destroyed=!1,this._paused=!0,this._lastFrameTime=0,this.zLayers=100,this.pageHidden=!1,this._clickHandlers=new Map,this._sourceOptions=i,this._initialSourceOptions=i,this.retina=new _n(this),this.canvas=new ss(this,this._engine),this.particles=new yn(this._engine,this),this.pathGenerators=new Map,this.interactivity={mouse:{clicking:!1,inside:!1}},this.plugins=new Map,this.effectDrawers=new Map,this.shapeDrawers=new Map,this._options=bn(this._engine,this),this.actualOptions=bn(this._engine,this),this._eventListeners=new os(this),this._intersectionObserver=Be((t=>this._intersectionManager(t))),this._engine.dispatchEvent(pi.containerBuilt,{container:this})}get animationStatus(){return!this._paused&&!this.pageHidden&&wn(this)}get options(){return this._options}get sourceOptions(){return this._sourceOptions}addClickHandler(t){if(!wn(this))return;const e=this.interactivity.element;if(!e)return;const i=(e,i,s)=>{if(!wn(this))return;const n=this.retina.pixelRatio,o={x:i.x*n,y:i.y*n},a=this.particles.quadTree.queryCircle(o,s*n);t(e,a)};let s=!1,n=!1;this._clickHandlers.set("click",(t=>{if(!wn(this))return;const e=t,s={x:e.offsetX||e.clientX,y:e.offsetY||e.clientY};i(t,s,K)})),this._clickHandlers.set("touchstart",(()=>{wn(this)&&(s=!0,n=!1)})),this._clickHandlers.set("touchmove",(()=>{wn(this)&&(n=!0)})),this._clickHandlers.set("touchend",(t=>{if(wn(this)){if(s&&!n){const e=t;let s=e.touches[e.touches.length-tt];if(!s&&(s=e.changedTouches[e.changedTouches.length-tt],!s))return;const n=this.canvas.element,o=n?n.getBoundingClientRect():void 0,a={x:s.clientX-(o?o.left:et),y:s.clientY-(o?o.top:et)};i(t,a,Math.max(s.radiusX,s.radiusY))}s=!1,n=!1}})),this._clickHandlers.set("touchcancel",(()=>{wn(this)&&(s=!1,n=!1)}));for(const[t,i]of this._clickHandlers)e.addEventListener(t,i)}addLifeTime(t){this._lifeTime+=t}addPath(t,e,i=!1){return!(!wn(this)||!i&&this.pathGenerators.has(t))&&(this.pathGenerators.set(t,e),!0)}alive(){return!this._duration||this._lifeTime<=this._duration}clearClickHandlers(){if(wn(this)){for(const[t,e]of this._clickHandlers)this.interactivity.element?.removeEventListener(t,e);this._clickHandlers.clear()}}destroy(t=!0){if(wn(this)){this.stop(),this.clearClickHandlers(),this.particles.destroy(),this.canvas.destroy();for(const t of this.effectDrawers.values())t.destroy?.(this);for(const t of this.shapeDrawers.values())t.destroy?.(this);for(const t of this.effectDrawers.keys())this.effectDrawers.delete(t);for(const t of this.shapeDrawers.keys())this.shapeDrawers.delete(t);if(this._engine.clearPlugins(this),this.destroyed=!0,t){const t=this._engine.items,e=t.findIndex((t=>t===this));e>=st&&t.splice(e,it)}this._engine.dispatchEvent(pi.containerDestroyed,{container:this})}}draw(t){if(!wn(this))return;let e=t;const i=t=>{e&&(this._lastFrameTime=void 0,e=!1),this._nextFrame(t)};this._drawAnimationFrame=ne((t=>i(t)))}async export(t,e={}){for(const i of this.plugins.values()){if(!i.export)continue;const s=await i.export(t,e);if(s.supported)return s.blob}Ie().error(`${f} - Export plugin with type ${t} not found`)}handleClickMode(t){if(wn(this)){this.particles.handleClickMode(t);for(const e of this.plugins.values())e.handleClickMode?.(t)}}async init(){if(!wn(this))return;const t=this._engine.getSupportedEffects();for(const e of t){const t=this._engine.getEffectDrawer(e);t&&this.effectDrawers.set(e,t)}const e=this._engine.getSupportedShapes();for(const t of e){const e=this._engine.getShapeDrawer(t);e&&this.shapeDrawers.set(t,e)}await this.particles.initPlugins(),this._options=bn(this._engine,this,this._initialSourceOptions,this.sourceOptions),this.actualOptions=bn(this._engine,this,this._options);const i=await this._engine.getAvailablePlugins(this);for(const[t,e]of i)this.plugins.set(t,e);this.retina.init(),await this.canvas.init(),this.updateActualOptions(),this.canvas.initBackground(),this.canvas.resize();const{zLayers:s,duration:n,delay:o,fpsLimit:a,smooth:r}=this.actualOptions;this.zLayers=s,this._duration=he(n)*g,this._delay=he(o)*g,this._lifeTime=0,this.fpsLimit=a>ot?a:nt,this._smooth=r;for(const t of this.effectDrawers.values())await(t.init?.(this));for(const t of this.shapeDrawers.values())await(t.init?.(this));for(const t of this.plugins.values())await(t.init?.());this._engine.dispatchEvent(pi.containerInit,{container:this}),await this.particles.init(),this.particles.setDensity();for(const t of this.plugins.values())t.particlesSetup?.();this._engine.dispatchEvent(pi.particlesSetup,{container:this})}async loadTheme(t){wn(this)&&(this._currentTheme=t,await this.refresh())}pause(){if(wn(this)&&(void 0!==this._drawAnimationFrame&&(oe(this._drawAnimationFrame),delete this._drawAnimationFrame),!this._paused)){for(const t of this.plugins.values())t.pause?.();this.pageHidden||(this._paused=!0),this._engine.dispatchEvent(pi.containerPaused,{container:this})}}play(t){if(!wn(this))return;const e=this._paused||t;if(!this._firstStart||this.actualOptions.autoPlay){if(this._paused&&(this._paused=!1),e)for(const t of this.plugins.values())t.play&&t.play();this._engine.dispatchEvent(pi.containerPlay,{container:this}),this.draw(e??!1)}else this._firstStart=!1}async refresh(){if(wn(this))return this.stop(),this.start()}async reset(t){if(wn(this))return this._initialSourceOptions=t,this._sourceOptions=t,this._options=bn(this._engine,this,this._initialSourceOptions,this.sourceOptions),this.actualOptions=bn(this._engine,this,this._options),this.refresh()}async start(){wn(this)&&!this.started&&(await this.init(),this.started=!0,await new Promise((t=>{const e=async()=>{this._eventListeners.addListeners(),this.interactivity.element instanceof HTMLElement&&this._intersectionObserver&&this._intersectionObserver.observe(this.interactivity.element);for(const t of this.plugins.values())await(t.start?.());this._engine.dispatchEvent(pi.containerStarted,{container:this}),this.play(),t()};this._delayTimeout=setTimeout((()=>{e()}),this._delay)})))}stop(){if(wn(this)&&this.started){this._delayTimeout&&(clearTimeout(this._delayTimeout),delete this._delayTimeout),this._firstStart=!0,this.started=!1,this._eventListeners.removeListeners(),this.pause(),this.particles.clear(),this.canvas.stop(),this.interactivity.element instanceof HTMLElement&&this._intersectionObserver&&this._intersectionObserver.unobserve(this.interactivity.element);for(const t of this.plugins.values())t.stop?.();for(const t of this.plugins.keys())this.plugins.delete(t);this._sourceOptions=this._options,this._engine.dispatchEvent(pi.containerStopped,{container:this})}}updateActualOptions(){this.actualOptions.responsive=[];const t=this.actualOptions.setResponsive(this.canvas.size.width,this.retina.pixelRatio,this._options);return this.actualOptions.setTheme(this._currentTheme),this._responsiveMaxWidth!==t&&(this._responsiveMaxWidth=t,!0)}}class zn{constructor(){this._listeners=new Map}addEventListener(t,e){this.removeEventListener(t,e);let i=this._listeners.get(t);i||(i=[],this._listeners.set(t,i)),i.push(e)}dispatchEvent(t,e){const i=this._listeners.get(t);i?.forEach((t=>t(e)))}hasEventListener(t){return!!this._listeners.get(t)}removeAllEventListeners(t){t?this._listeners.delete(t):this._listeners=new Map}removeEventListener(t,e){const i=this._listeners.get(t);if(!i)return;const s=i.length,n=i.indexOf(e);n<xt||(s===Pt?this._listeners.delete(t):i.splice(n,Pt))}}async function Mn(t,e,i,s=!1){let n=e.get(t);return n&&!s||(n=await Promise.all([...i.values()].map((e=>e(t)))),e.set(t,n)),n}class On{constructor(){this._configs=new Map,this._domArray=[],this._eventDispatcher=new zn,this._initialized=!1,this.plugins=[],this.colorManagers=new Map,this.easingFunctions=new Map,this._initializers={interactors:new Map,movers:new Map,updaters:new Map},this.interactors=new Map,this.movers=new Map,this.updaters=new Map,this.presets=new Map,this.effectDrawers=new Map,this.shapeDrawers=new Map,this.pathGenerators=new Map}get configs(){const t={};for(const[e,i]of this._configs)t[e]=i;return t}get items(){return this._domArray}get version(){return"3.8.1"}async addColorManager(t,e=!0){this.colorManagers.set(t.key,t),await this.refresh(e)}addConfig(t){const e=t.key??t.name??"default";this._configs.set(e,t),this._eventDispatcher.dispatchEvent(pi.configAdded,{data:{name:e,config:t}})}async addEasing(t,e,i=!0){this.getEasing(t)||(this.easingFunctions.set(t,e),await this.refresh(i))}async addEffect(t,e,i=!0){ei(t,(t=>{this.getEffectDrawer(t)||this.effectDrawers.set(t,e)})),await this.refresh(i)}addEventListener(t,e){this._eventDispatcher.addEventListener(t,e)}async addInteractor(t,e,i=!0){this._initializers.interactors.set(t,e),await this.refresh(i)}async addMover(t,e,i=!0){this._initializers.movers.set(t,e),await this.refresh(i)}async addParticleUpdater(t,e,i=!0){this._initializers.updaters.set(t,e),await this.refresh(i)}async addPathGenerator(t,e,i=!0){this.getPathGenerator(t)||this.pathGenerators.set(t,e),await this.refresh(i)}async addPlugin(t,e=!0){this.getPlugin(t.id)||this.plugins.push(t),await this.refresh(e)}async addPreset(t,e,i=!1,s=!0){!i&&this.getPreset(t)||this.presets.set(t,e),await this.refresh(s)}async addShape(t,e=!0){for(const e of t.validTypes)this.getShapeDrawer(e)||this.shapeDrawers.set(e,t);await this.refresh(e)}checkVersion(t){if(this.version!==t)throw new Error(`The tsParticles version is different from the loaded plugins version. Engine version: ${this.version}. Plugin version: ${t}`)}clearPlugins(t){this.updaters.delete(t),this.movers.delete(t),this.interactors.delete(t)}dispatchEvent(t,e){this._eventDispatcher.dispatchEvent(t,e)}dom(){return this.items}domItem(t){return this.item(t)}async getAvailablePlugins(t){const e=new Map;for(const i of this.plugins)i.needsPlugin(t.actualOptions)&&e.set(i.id,await i.getPlugin(t));return e}getEasing(t){return this.easingFunctions.get(t)??(t=>t)}getEffectDrawer(t){return this.effectDrawers.get(t)}async getInteractors(t,e=!1){return Mn(t,this.interactors,this._initializers.interactors,e)}async getMovers(t,e=!1){return Mn(t,this.movers,this._initializers.movers,e)}getPathGenerator(t){return this.pathGenerators.get(t)}getPlugin(t){return this.plugins.find((e=>e.id===t))}getPreset(t){return this.presets.get(t)}getShapeDrawer(t){return this.shapeDrawers.get(t)}getSupportedEffects(){return this.effectDrawers.keys()}getSupportedShapes(){return this.shapeDrawers.keys()}async getUpdaters(t,e=!1){return Mn(t,this.updaters,this._initializers.updaters,e)}init(){this._initialized||(this._initialized=!0)}item(t){const{items:e}=this,i=e[t];if(i&&!i.destroyed)return i;e.splice(t,it)}async load(t){const e=t.id??t.element?.id??`tsparticles${Math.floor(ie()*rt)}`,{index:s,url:n}=t,o=n?await async function(t){const e=ii(t.url,t.index);if(!e)return t.fallback;const i=await fetch(e);return i.ok?await i.json():(Ie().error(`${f} ${i.status} while retrieving config file`),t.fallback)}({fallback:t.options,url:n,index:s}):t.options,a=ii(o,s),{items:r}=this,c=r.findIndex((t=>t.id.description===e)),h=new xn(this,e,a);if(c>=ct){const t=this.item(c),e=t?ht:lt;t&&!t.destroyed&&t.destroy(!1),r.splice(c,e,h)}else r.push(h);const l=((t,e)=>{let s=e??document.getElementById(t);return s||(s=document.createElement("div"),s.id=t,s.dataset[i]=P,document.body.append(s),s)})(e,t.element),d=(t=>{let e;if(t instanceof HTMLCanvasElement||t.tagName.toLowerCase()===S)e=t,e.dataset[i]||(e.dataset[i]=k);else{const s=t.getElementsByTagName(S);s.length?(e=s[at],e.dataset[i]=k):(e=document.createElement(S),e.dataset[i]=P,t.appendChild(e))}const s="100%";return e.style.width||(e.style.width=s),e.style.height||(e.style.height=s),e})(l);return h.canvas.loadCanvas(d),await h.start(),h}loadOptions(t,e){this.plugins.forEach((i=>i.loadOptions?.(t,e)))}loadParticlesOptions(t,e,...i){const s=this.updaters.get(t);s&&s.forEach((t=>t.loadOptions?.(e,...i)))}async refresh(t=!0){t&&await Promise.all(this.items.map((t=>t.refresh())))}removeEventListener(t,e){this._eventDispatcher.removeEventListener(t,e)}setOnClickHandler(t){const{items:e}=this;if(!e.length)throw new Error(`${f} can only set click handlers after calling tsParticles.load()`);e.forEach((e=>e.addClickHandler(t)))}}class Pn{constructor(t){this.type=wi.external,this.container=t}}class kn{constructor(t){this.type=wi.particles,this.container=t}}var Sn,Cn,Rn;!function(t){t.clockwise="clockwise",t.counterClockwise="counter-clockwise",t.random="random"}(Sn||(Sn={})),function(t){t.linear="linear",t.radial="radial",t.random="random"}(Cn||(Cn={})),function(t){t.easeInBack="ease-in-back",t.easeInCirc="ease-in-circ",t.easeInCubic="ease-in-cubic",t.easeInLinear="ease-in-linear",t.easeInQuad="ease-in-quad",t.easeInQuart="ease-in-quart",t.easeInQuint="ease-in-quint",t.easeInExpo="ease-in-expo",t.easeInSine="ease-in-sine",t.easeOutBack="ease-out-back",t.easeOutCirc="ease-out-circ",t.easeOutCubic="ease-out-cubic",t.easeOutLinear="ease-out-linear",t.easeOutQuad="ease-out-quad",t.easeOutQuart="ease-out-quart",t.easeOutQuint="ease-out-quint",t.easeOutExpo="ease-out-expo",t.easeOutSine="ease-out-sine",t.easeInOutBack="ease-in-out-back",t.easeInOutCirc="ease-in-out-circ",t.easeInOutCubic="ease-in-out-cubic",t.easeInOutLinear="ease-in-out-linear",t.easeInOutQuad="ease-in-out-quad",t.easeInOutQuart="ease-in-out-quart",t.easeInOutQuint="ease-in-out-quint",t.easeInOutExpo="ease-in-out-expo",t.easeInOutSine="ease-in-out-sine"}(Rn||(Rn={}));const Tn=function(){const t=new On;return t.init(),t}();return Le()||(window.tsParticles=Tn),e})()));